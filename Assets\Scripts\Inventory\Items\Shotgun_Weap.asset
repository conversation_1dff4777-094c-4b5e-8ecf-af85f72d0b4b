%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 52d37c52629771a4ebf176d3c8a3a22b, type: 3}
  m_Name: Shotgun_Weap
  m_EditorClassIdentifier: 
  itemName: Shotgun
  size: {x: 4, y: 2}
  itemType: 1
  inventoryIcon: {fileID: 21300000, guid: eded3c9fe1d6de84ca8ce651c44b001e, type: 3}
  worldSprite: {fileID: 21300000, guid: 694b61492d6a7084d92cf83f3f5f4df0, type: 3}
  canRotate: 1
  magazineSize: 5
  requiredAmmoType: 1
  weaponType: 2
  fireRate: 300
  baseAccuracy: 2
  maxRecoilSpread: 10
  accuracyRecoveryRate: 15
  movementSpreadMultiplier: 2
  pumpDelay: 0.8
  ammoType: 0
  maxStackSize: 50
