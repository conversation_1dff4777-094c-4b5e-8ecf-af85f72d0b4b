using System;
using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Container inventory system - separate from player inventory but uses same logic
/// </summary>
public class ContainerInventory : MonoBehaviour
{
    [Header("Container Settings")]
    [Tooltip("Width of the container grid")]
    public int width = 6;
    
    [<PERSON>lt<PERSON>("Height of the container grid")]
    public int height = 4;
    
    [<PERSON><PERSON>("Container Info")]
    [Toolt<PERSON>("Display name for this container")]
    public string containerName = "Container";
    
    [Header("Starting Items")]
    [Tooltip("Items that should be in this container when it's first opened")]
    public List<ItemData> startingItems = new List<ItemData>();
    
    // Grid to store items
    private InventoryItem[,] inventoryGrid;
    
    // List of all items in this container
    private List<InventoryItem> items = new List<InventoryItem>();
    
    // Event for when container inventory changes
    public event Action OnInventoryChanged;
    
    // Track if container has been initialized
    private bool isInitialized = false;
    
    private void Awake()
    {
        // Initialize the grid
        inventoryGrid = new InventoryItem[width, height];
    }
    
    private void Start()
    {
        // Initialize with starting items if not already done
        if (!isInitialized)
        {
            InitializeStartingItems();
            isInitialized = true;
        }
    }
    
    /// <summary>
    /// Initialize container with starting items
    /// </summary>
    private void InitializeStartingItems()
    {
        foreach (ItemData itemData in startingItems)
        {
            if (itemData != null)
            {
                // Create item instance
                GameObject itemObj = new GameObject($"Container_{itemData.itemName}");
                itemObj.transform.SetParent(transform);
                
                InventoryItem item = itemObj.AddComponent<InventoryItem>();
                item.itemData = itemData;
                item.stackCount = itemData.maxStackSize > 0 ? itemData.maxStackSize : 1;
                
                // Try to add to container
                if (!TryAddItemToContainer(item))
                {
                    Debug.LogWarning($"Could not fit starting item {itemData.itemName} in container {containerName}");
                    Destroy(itemObj);
                }
            }
        }
    }
    
    /// <summary>
    /// Try to add an item to the container at the first available position
    /// </summary>
    public bool TryAddItemToContainer(InventoryItem item)
    {
        if (item == null) return false;
        
        // Find first available position
        for (int y = 0; y < height; y++)
        {
            for (int x = 0; x < width; x++)
            {
                Vector2Int position = new Vector2Int(x, y);
                
                // Try normal orientation first
                if (CanPlaceItem(item, position, item.itemData.size))
                {
                    PlaceItemInGrid(item, position, item.itemData.size);
                    item.position = position;
                    item.isRotated = false;
                    items.Add(item);
                    OnInventoryChanged?.Invoke();
                    return true;
                }
                
                // Try rotated orientation if item can rotate
                if (item.itemData.canRotate)
                {
                    Vector2Int rotatedSize = new Vector2Int(item.itemData.size.y, item.itemData.size.x);
                    if (CanPlaceItem(item, position, rotatedSize))
                    {
                        PlaceItemInGrid(item, position, rotatedSize);
                        item.position = position;
                        item.isRotated = true;
                        items.Add(item);
                        OnInventoryChanged?.Invoke();
                        return true;
                    }
                }
            }
        }
        
        return false;
    }
    
    /// <summary>
    /// Check if an item can be placed at a specific position
    /// </summary>
    public bool CanPlaceItem(InventoryItem item, Vector2Int position, Vector2Int size)
    {
        // Check bounds
        if (position.x < 0 || position.y < 0) return false;
        if (position.x + size.x > width || position.y + size.y > height) return false;
        
        // Check for overlapping items
        for (int x = position.x; x < position.x + size.x; x++)
        {
            for (int y = position.y; y < position.y + size.y; y++)
            {
                if (inventoryGrid[x, y] != null && inventoryGrid[x, y] != item)
                {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /// <summary>
    /// Place an item in the grid
    /// </summary>
    private void PlaceItemInGrid(InventoryItem item, Vector2Int position, Vector2Int size)
    {
        for (int x = position.x; x < position.x + size.x; x++)
        {
            for (int y = position.y; y < position.y + size.y; y++)
            {
                inventoryGrid[x, y] = item;
            }
        }
    }
    
    /// <summary>
    /// Remove an item from the container
    /// </summary>
    public bool RemoveItem(InventoryItem item)
    {
        if (item == null || !items.Contains(item)) return false;
        
        // Clear from grid
        ClearItemFromGrid(item);
        
        // Remove from items list
        items.Remove(item);
        
        OnInventoryChanged?.Invoke();
        return true;
    }
    
    /// <summary>
    /// Clear an item from the grid
    /// </summary>
    private void ClearItemFromGrid(InventoryItem item)
    {
        for (int x = 0; x < width; x++)
        {
            for (int y = 0; y < height; y++)
            {
                if (inventoryGrid[x, y] == item)
                {
                    inventoryGrid[x, y] = null;
                }
            }
        }
    }
    
    /// <summary>
    /// Get all items in this container
    /// </summary>
    public List<InventoryItem> GetAllItems()
    {
        return new List<InventoryItem>(items);
    }
    
    /// <summary>
    /// Get container display name
    /// </summary>
    public string GetContainerName()
    {
        return containerName;
    }

    /// <summary>
    /// Check if item can be placed with rotation support
    /// </summary>
    public bool CanPlaceItemWithRotation(InventoryItem item, Vector2Int position, bool rotated)
    {
        Vector2Int size = rotated ?
            new Vector2Int(item.itemData.size.y, item.itemData.size.x) :
            item.itemData.size;

        return CanPlaceItem(item, position, size);
    }

    /// <summary>
    /// Update item position in container
    /// </summary>
    public bool UpdateItemPosition(InventoryItem item, Vector2Int newPosition, Vector2Int size, bool rotated)
    {
        if (item == null || !items.Contains(item)) return false;

        // Clear item from current position
        ClearItemFromGrid(item);

        // Check if new position is valid
        if (!CanPlaceItem(item, newPosition, size))
        {
            // Restore item to original position
            Vector2Int originalSize = item.isRotated ?
                new Vector2Int(item.itemData.size.y, item.itemData.size.x) :
                item.itemData.size;
            PlaceItemInGrid(item, item.position, originalSize);
            return false;
        }

        // Place item at new position
        PlaceItemInGrid(item, newPosition, size);
        item.position = newPosition;
        item.isRotated = rotated;

        OnInventoryChanged?.Invoke();
        return true;
    }
}
