{ "pid": 21312, "tid": 60129542144, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559114518, "dur": 15098, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559129625, "dur": 73921, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559129634, "dur": 37, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559129674, "dur": 26301, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559155983, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559155986, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559156015, "dur": 3, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559156019, "dur": 2021, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158047, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158049, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158122, "dur": 3, "ph": "X", "name": "ProcessMessages 4969", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158126, "dur": 27, "ph": "X", "name": "ReadAsync 4969", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158157, "dur": 40, "ph": "X", "name": "ReadAsync 757", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158201, "dur": 1, "ph": "X", "name": "ProcessMessages 709", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158203, "dur": 39, "ph": "X", "name": "ReadAsync 709", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158243, "dur": 1, "ph": "X", "name": "ProcessMessages 1761", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158245, "dur": 28, "ph": "X", "name": "ReadAsync 1761", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158276, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158278, "dur": 32, "ph": "X", "name": "ReadAsync 540", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158313, "dur": 1, "ph": "X", "name": "ProcessMessages 1388", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158315, "dur": 24, "ph": "X", "name": "ReadAsync 1388", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158343, "dur": 19, "ph": "X", "name": "ReadAsync 572", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158365, "dur": 32, "ph": "X", "name": "ReadAsync 398", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158400, "dur": 1, "ph": "X", "name": "ProcessMessages 1089", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158402, "dur": 32, "ph": "X", "name": "ReadAsync 1089", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158436, "dur": 1, "ph": "X", "name": "ProcessMessages 1508", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158438, "dur": 23, "ph": "X", "name": "ReadAsync 1508", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158463, "dur": 1, "ph": "X", "name": "ProcessMessages 630", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158465, "dur": 101, "ph": "X", "name": "ReadAsync 630", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158567, "dur": 1, "ph": "X", "name": "ProcessMessages 1019", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158569, "dur": 47, "ph": "X", "name": "ReadAsync 1019", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158619, "dur": 2, "ph": "X", "name": "ProcessMessages 4342", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158622, "dur": 31, "ph": "X", "name": "ReadAsync 4342", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158657, "dur": 1, "ph": "X", "name": "ProcessMessages 1128", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158659, "dur": 27, "ph": "X", "name": "ReadAsync 1128", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158687, "dur": 1, "ph": "X", "name": "ProcessMessages 1394", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158689, "dur": 27, "ph": "X", "name": "ReadAsync 1394", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158719, "dur": 23, "ph": "X", "name": "ReadAsync 596", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158745, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158747, "dur": 31, "ph": "X", "name": "ReadAsync 386", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158781, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158784, "dur": 34, "ph": "X", "name": "ReadAsync 733", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158820, "dur": 1, "ph": "X", "name": "ProcessMessages 1568", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158822, "dur": 34, "ph": "X", "name": "ReadAsync 1568", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158860, "dur": 1, "ph": "X", "name": "ProcessMessages 1258", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158863, "dur": 25, "ph": "X", "name": "ReadAsync 1258", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158893, "dur": 24, "ph": "X", "name": "ReadAsync 601", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158918, "dur": 1, "ph": "X", "name": "ProcessMessages 1321", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158920, "dur": 23, "ph": "X", "name": "ReadAsync 1321", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158946, "dur": 20, "ph": "X", "name": "ReadAsync 753", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158967, "dur": 1, "ph": "X", "name": "ProcessMessages 828", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158969, "dur": 20, "ph": "X", "name": "ReadAsync 828", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158991, "dur": 1, "ph": "X", "name": "ProcessMessages 856", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559158992, "dur": 20, "ph": "X", "name": "ReadAsync 856", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159014, "dur": 47, "ph": "X", "name": "ReadAsync 539", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159064, "dur": 27, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159094, "dur": 21, "ph": "X", "name": "ReadAsync 888", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159117, "dur": 21, "ph": "X", "name": "ReadAsync 676", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159139, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159141, "dur": 22, "ph": "X", "name": "ReadAsync 608", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159164, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159166, "dur": 18, "ph": "X", "name": "ReadAsync 578", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159186, "dur": 24, "ph": "X", "name": "ReadAsync 353", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159213, "dur": 21, "ph": "X", "name": "ReadAsync 877", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159237, "dur": 22, "ph": "X", "name": "ReadAsync 647", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159261, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159262, "dur": 19, "ph": "X", "name": "ReadAsync 749", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159283, "dur": 1, "ph": "X", "name": "ProcessMessages 749", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159285, "dur": 22, "ph": "X", "name": "ReadAsync 749", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159308, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159310, "dur": 20, "ph": "X", "name": "ReadAsync 871", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159333, "dur": 22, "ph": "X", "name": "ReadAsync 881", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159357, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159361, "dur": 48, "ph": "X", "name": "ReadAsync 798", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159411, "dur": 1, "ph": "X", "name": "ProcessMessages 996", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159413, "dur": 27, "ph": "X", "name": "ReadAsync 996", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159442, "dur": 1, "ph": "X", "name": "ProcessMessages 1341", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159443, "dur": 21, "ph": "X", "name": "ReadAsync 1341", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159467, "dur": 20, "ph": "X", "name": "ReadAsync 777", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159489, "dur": 31, "ph": "X", "name": "ReadAsync 883", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159523, "dur": 19, "ph": "X", "name": "ReadAsync 734", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159546, "dur": 17, "ph": "X", "name": "ReadAsync 751", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159565, "dur": 18, "ph": "X", "name": "ReadAsync 187", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159585, "dur": 25, "ph": "X", "name": "ReadAsync 289", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159612, "dur": 1, "ph": "X", "name": "ProcessMessages 959", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159613, "dur": 27, "ph": "X", "name": "ReadAsync 959", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159643, "dur": 33, "ph": "X", "name": "ReadAsync 971", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159678, "dur": 34, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159715, "dur": 22, "ph": "X", "name": "ReadAsync 1251", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159740, "dur": 19, "ph": "X", "name": "ReadAsync 575", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159761, "dur": 21, "ph": "X", "name": "ReadAsync 693", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159785, "dur": 27, "ph": "X", "name": "ReadAsync 663", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159814, "dur": 19, "ph": "X", "name": "ReadAsync 1083", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159836, "dur": 19, "ph": "X", "name": "ReadAsync 706", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159857, "dur": 19, "ph": "X", "name": "ReadAsync 853", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159879, "dur": 19, "ph": "X", "name": "ReadAsync 679", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159900, "dur": 35, "ph": "X", "name": "ReadAsync 839", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159938, "dur": 18, "ph": "X", "name": "ReadAsync 727", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159959, "dur": 35, "ph": "X", "name": "ReadAsync 500", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159995, "dur": 1, "ph": "X", "name": "ProcessMessages 1125", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559159997, "dur": 129, "ph": "X", "name": "ReadAsync 1125", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559160132, "dur": 2, "ph": "X", "name": "ProcessMessages 283", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559160136, "dur": 3550, "ph": "X", "name": "ReadAsync 283", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163690, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163692, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163732, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163734, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163768, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163770, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163804, "dur": 19, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163826, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163853, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163855, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163879, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163939, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163941, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163982, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559163984, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164016, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164018, "dur": 43, "ph": "X", "name": "ReadAsync 60", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164064, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164067, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164104, "dur": 2, "ph": "X", "name": "ProcessMessages 148", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164108, "dur": 23, "ph": "X", "name": "ReadAsync 148", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164133, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164134, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164170, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164172, "dur": 28, "ph": "X", "name": "ReadAsync 108", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164203, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164205, "dur": 35, "ph": "X", "name": "ReadAsync 100", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164244, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164246, "dur": 30, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164279, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164289, "dur": 56, "ph": "X", "name": "ReadAsync 144", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164349, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164352, "dur": 50, "ph": "X", "name": "ReadAsync 128", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164406, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164410, "dur": 48, "ph": "X", "name": "ReadAsync 288", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164461, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164464, "dur": 41, "ph": "X", "name": "ReadAsync 208", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164508, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164511, "dur": 23, "ph": "X", "name": "ReadAsync 176", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164535, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164538, "dur": 25, "ph": "X", "name": "ReadAsync 208", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164564, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164566, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164589, "dur": 16, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164608, "dur": 142, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164753, "dur": 17, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559164773, "dur": 31748, "ph": "X", "name": "ReadAsync 96", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559196529, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559196533, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559196573, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559196576, "dur": 1807, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559198392, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559198396, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559198451, "dur": 14, "ph": "X", "name": "ProcessMessages 50", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559198466, "dur": 591, "ph": "X", "name": "ReadAsync 50", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559199062, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 60129542144, "ts": 1753105559199092, "dur": 4450, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559204019, "dur": 292, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553479250, "dur": 15869, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553495120, "dur": 5459228, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553495129, "dur": 193, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553495325, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553495326, "dur": 29660, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553524994, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553524997, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553525032, "dur": 4, "ph": "X", "name": "ProcessMessages 47", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553525037, "dur": 4700, "ph": "X", "name": "ReadAsync 47", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553529746, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553529749, "dur": 121, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553529874, "dur": 2, "ph": "X", "name": "ProcessMessages 2419", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553529877, "dur": 112, "ph": "X", "name": "ReadAsync 2419", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553529993, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553529994, "dur": 69, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530067, "dur": 1, "ph": "X", "name": "ProcessMessages 2180", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530069, "dur": 38, "ph": "X", "name": "ReadAsync 2180", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530111, "dur": 25, "ph": "X", "name": "ReadAsync 362", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530138, "dur": 35, "ph": "X", "name": "ReadAsync 354", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530176, "dur": 1, "ph": "X", "name": "ProcessMessages 789", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530177, "dur": 35, "ph": "X", "name": "ReadAsync 789", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530214, "dur": 1, "ph": "X", "name": "ProcessMessages 1514", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530216, "dur": 128, "ph": "X", "name": "ReadAsync 1514", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530348, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530401, "dur": 1, "ph": "X", "name": "ProcessMessages 1538", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530403, "dur": 29, "ph": "X", "name": "ReadAsync 1538", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530436, "dur": 27, "ph": "X", "name": "ReadAsync 758", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530465, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530467, "dur": 28, "ph": "X", "name": "ReadAsync 762", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530500, "dur": 32, "ph": "X", "name": "ReadAsync 320", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530534, "dur": 1, "ph": "X", "name": "ProcessMessages 1228", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530536, "dur": 28, "ph": "X", "name": "ReadAsync 1228", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530567, "dur": 21, "ph": "X", "name": "ReadAsync 1060", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530591, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530593, "dur": 38, "ph": "X", "name": "ReadAsync 456", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530632, "dur": 1, "ph": "X", "name": "ProcessMessages 1580", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530634, "dur": 22, "ph": "X", "name": "ReadAsync 1580", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530659, "dur": 1, "ph": "X", "name": "ProcessMessages 760", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530661, "dur": 36, "ph": "X", "name": "ReadAsync 760", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530700, "dur": 1, "ph": "X", "name": "ProcessMessages 766", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530702, "dur": 115, "ph": "X", "name": "ReadAsync 766", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530821, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530822, "dur": 52, "ph": "X", "name": "ReadAsync 874", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530877, "dur": 3, "ph": "X", "name": "ProcessMessages 5060", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530881, "dur": 23, "ph": "X", "name": "ReadAsync 5060", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530905, "dur": 1, "ph": "X", "name": "ProcessMessages 1119", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530907, "dur": 21, "ph": "X", "name": "ReadAsync 1119", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530930, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530933, "dur": 24, "ph": "X", "name": "ReadAsync 854", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530960, "dur": 31, "ph": "X", "name": "ReadAsync 1142", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530994, "dur": 1, "ph": "X", "name": "ProcessMessages 1027", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553530996, "dur": 32, "ph": "X", "name": "ReadAsync 1027", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531029, "dur": 1, "ph": "X", "name": "ProcessMessages 1783", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531031, "dur": 45, "ph": "X", "name": "ReadAsync 1783", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531078, "dur": 1, "ph": "X", "name": "ProcessMessages 1822", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531081, "dur": 22, "ph": "X", "name": "ReadAsync 1822", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531105, "dur": 1, "ph": "X", "name": "ProcessMessages 693", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531106, "dur": 20, "ph": "X", "name": "ReadAsync 693", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531128, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531130, "dur": 41, "ph": "X", "name": "ReadAsync 807", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531174, "dur": 1, "ph": "X", "name": "ProcessMessages 1007", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531176, "dur": 23, "ph": "X", "name": "ReadAsync 1007", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531201, "dur": 1, "ph": "X", "name": "ProcessMessages 1148", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531204, "dur": 25, "ph": "X", "name": "ReadAsync 1148", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531234, "dur": 21, "ph": "X", "name": "ReadAsync 552", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531257, "dur": 23, "ph": "X", "name": "ReadAsync 1158", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531281, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531283, "dur": 22, "ph": "X", "name": "ReadAsync 814", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531307, "dur": 20, "ph": "X", "name": "ReadAsync 1036", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531330, "dur": 21, "ph": "X", "name": "ReadAsync 865", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531353, "dur": 36, "ph": "X", "name": "ReadAsync 364", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531391, "dur": 1, "ph": "X", "name": "ProcessMessages 1565", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531392, "dur": 20, "ph": "X", "name": "ReadAsync 1565", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531415, "dur": 21, "ph": "X", "name": "ReadAsync 924", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531438, "dur": 20, "ph": "X", "name": "ReadAsync 816", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531461, "dur": 21, "ph": "X", "name": "ReadAsync 809", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531484, "dur": 20, "ph": "X", "name": "ReadAsync 884", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531507, "dur": 49, "ph": "X", "name": "ReadAsync 834", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531558, "dur": 1, "ph": "X", "name": "ProcessMessages 1172", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531559, "dur": 16, "ph": "X", "name": "ReadAsync 1172", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531577, "dur": 20, "ph": "X", "name": "ReadAsync 160", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531600, "dur": 21, "ph": "X", "name": "ReadAsync 858", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531622, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531623, "dur": 19, "ph": "X", "name": "ReadAsync 790", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531646, "dur": 38, "ph": "X", "name": "ReadAsync 699", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531686, "dur": 25, "ph": "X", "name": "ReadAsync 1005", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531714, "dur": 21, "ph": "X", "name": "ReadAsync 761", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531738, "dur": 18, "ph": "X", "name": "ReadAsync 852", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531759, "dur": 19, "ph": "X", "name": "ReadAsync 530", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531781, "dur": 19, "ph": "X", "name": "ReadAsync 763", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531803, "dur": 20, "ph": "X", "name": "ReadAsync 824", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531826, "dur": 22, "ph": "X", "name": "ReadAsync 848", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531851, "dur": 24, "ph": "X", "name": "ReadAsync 747", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531877, "dur": 19, "ph": "X", "name": "ReadAsync 1013", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531899, "dur": 20, "ph": "X", "name": "ReadAsync 732", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531927, "dur": 23, "ph": "X", "name": "ReadAsync 328", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531953, "dur": 24, "ph": "X", "name": "ReadAsync 970", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553531980, "dur": 21, "ph": "X", "name": "ReadAsync 542", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553532004, "dur": 4115, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536124, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536126, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536179, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536185, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536221, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536222, "dur": 184, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536411, "dur": 12, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536424, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536497, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536499, "dur": 77, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536580, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536582, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536634, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536638, "dur": 136, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536779, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536819, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536865, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536868, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536915, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536917, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553536962, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537004, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537006, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537045, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537047, "dur": 65, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537115, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537117, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537161, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537163, "dur": 138, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537307, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537345, "dur": 412, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537760, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537802, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553537805, "dur": 263, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538072, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538073, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538123, "dur": 293, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538420, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538422, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538469, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538471, "dur": 80, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538556, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538604, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538606, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538652, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538654, "dur": 133, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538791, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538830, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538832, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553538946, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539012, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539015, "dur": 114, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539138, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539140, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539195, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539197, "dur": 41, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539242, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539244, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539289, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539292, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539408, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539452, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539454, "dur": 123, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539582, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539629, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539631, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539673, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539675, "dur": 73, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539752, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539754, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539802, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539804, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539851, "dur": 15, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539868, "dur": 43, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539915, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539917, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539972, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553539974, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540013, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540015, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540060, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540063, "dur": 59, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540125, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540127, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540172, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540175, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540215, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540240, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540278, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540279, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540326, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540328, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540383, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540385, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540430, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540432, "dur": 40, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540475, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540477, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540517, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540519, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540561, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540566, "dur": 64, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540633, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540635, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540675, "dur": 2, "ph": "X", "name": "ProcessMessages 56", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540679, "dur": 41, "ph": "X", "name": "ReadAsync 56", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540724, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540726, "dur": 249, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540979, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553540981, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541021, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541022, "dur": 61, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541087, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541123, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541125, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541168, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541170, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541204, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541236, "dur": 51, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541292, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541331, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541376, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541424, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541426, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541504, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541561, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541603, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541605, "dur": 66, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541676, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541723, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541725, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541771, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541773, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541807, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541809, "dur": 85, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541899, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541964, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553541966, "dur": 55, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542024, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542027, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542065, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542067, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542110, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542112, "dur": 469, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542587, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542589, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542638, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542641, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542680, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542714, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542744, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542746, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542786, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542788, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542851, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542853, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542898, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542900, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542937, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542983, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553542985, "dur": 36, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543026, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543028, "dur": 75, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543108, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543143, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543145, "dur": 36, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543184, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543186, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543227, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543229, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543287, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543289, "dur": 81, "ph": "X", "name": "ReadAsync 48", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543374, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543376, "dur": 547, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543929, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543983, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553543986, "dur": 47, "ph": "X", "name": "ReadAsync 64", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544037, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544040, "dur": 52, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544097, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544099, "dur": 68, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544171, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544172, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544210, "dur": 3, "ph": "X", "name": "ProcessMessages 56", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544214, "dur": 39, "ph": "X", "name": "ReadAsync 56", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544256, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544260, "dur": 84, "ph": "X", "name": "ReadAsync 36", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544349, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544394, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544398, "dur": 81, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544483, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544485, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544511, "dur": 1, "ph": "X", "name": "ProcessMessages 24", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544514, "dur": 86, "ph": "X", "name": "ReadAsync 24", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544604, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544606, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544664, "dur": 2, "ph": "X", "name": "ProcessMessages 24", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553544667, "dur": 1413, "ph": "X", "name": "ReadAsync 24", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553546093, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553546098, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553546169, "dur": 5, "ph": "X", "name": "ProcessMessages 24", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553546176, "dur": 567, "ph": "X", "name": "ReadAsync 24", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553546750, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553546752, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553546818, "dur": 27, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553546847, "dur": 63, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553546914, "dur": 2, "ph": "X", "name": "ProcessMessages 40", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553546917, "dur": 1637, "ph": "X", "name": "ReadAsync 40", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553548568, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553548572, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553548643, "dur": 16, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553548662, "dur": 112, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553548777, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553548779, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553548833, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553548837, "dur": 208, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549050, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549052, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549122, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549125, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549196, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549199, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549246, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549248, "dur": 388, "ph": "X", "name": "ReadAsync 16", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549640, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549642, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549694, "dur": 15, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549711, "dur": 44, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549759, "dur": 14, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553549775, "dur": 222, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553550002, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553550004, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553550061, "dur": 13, "ph": "X", "name": "ProcessMessages 80", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553550076, "dur": 326, "ph": "X", "name": "ReadAsync 80", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553550407, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553550450, "dur": 3, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553550455, "dur": 30, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553550487, "dur": 3, "ph": "X", "name": "ProcessMessages 46", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553550491, "dur": 928, "ph": "X", "name": "ReadAsync 46", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553551427, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553551429, "dur": 166, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553551600, "dur": 11, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553551613, "dur": 412, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553552033, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553552104, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553552107, "dur": 456, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553552567, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553552569, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553552637, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553552640, "dur": 15975, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553568626, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553568630, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553568704, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553568710, "dur": 1363, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553570081, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553570083, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553570152, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553570156, "dur": 1527, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553571691, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553571693, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553571749, "dur": 13, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553571763, "dur": 5720, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553577492, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553577495, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553577522, "dur": 221, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553577747, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553577749, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553577846, "dur": 14712, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553592566, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553592569, "dur": 148, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553592721, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553592723, "dur": 332097, "ph": "X", "name": "ReadAsync 4", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553924829, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553924832, "dur": 232, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553925074, "dur": 6, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553925083, "dur": 213, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553925308, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553925313, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553925448, "dur": 2005, "ph": "X", "name": "ProcessMessages 16930", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105553927456, "dur": 952901, "ph": "X", "name": "ReadAsync 16930", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554880366, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554880369, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554880447, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554880450, "dur": 81558, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554962016, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554962019, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554962061, "dur": 10, "ph": "X", "name": "ProcessMessages 516", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554962072, "dur": 4598, "ph": "X", "name": "ReadAsync 516", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554966674, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554966679, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554966720, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554966723, "dur": 682, "ph": "X", "name": "ReadAsync 8", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554967410, "dur": 96, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554967507, "dur": 10, "ph": "X", "name": "ProcessMessages 34", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105554967518, "dur": 3976739, "ph": "X", "name": "ReadAsync 34", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105558944263, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105558944266, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105558944370, "dur": 9, "ph": "X", "name": "ProcessMessages 68", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105558944382, "dur": 2610, "ph": "X", "name": "ReadAsync 68", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105558946998, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105558947000, "dur": 171, "ph": "X", "name": "ReadAsync 12", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105558947175, "dur": 1, "ph": "X", "name": "ProcessMessages 13", "args": {} },
{ "pid": 21312, "tid": 55834574848, "ts": 1753105558947177, "dur": 6934, "ph": "X", "name": "ReadAsync 13", "args": {} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559204313, "dur": 455, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 21312, "tid": 51539607552, "ph": "M", "name": "thread_name", "args": { "name": "ReadEntireBinlogFromIpcAsync" } },
{ "pid": 21312, "tid": 51539607552, "ts": 1753105552003254, "dur": 15910, "ph": "X", "name": "WaitForConnectionAsync", "args": {} },
{ "pid": 21312, "tid": 51539607552, "ts": 1753105552019166, "dur": 64, "ph": "X", "name": "UpdateFromStreamAsync", "args": {} },
{ "pid": 21312, "tid": 51539607552, "ts": 1753105552019182, "dur": 28, "ph": "X", "name": "ReadAsync 0", "args": {} },
{ "pid": 21312, "tid": 51539607552, "ts": 1753105552019211, "dur": 1, "ph": "X", "name": "ProcessMessages 29", "args": {} },
{ "pid": 21312, "tid": 51539607552, "ts": 1753105552019213, "dur": 5, "ph": "X", "name": "ReadAsync 29", "args": {} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559204771, "dur": 64, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {} },
{ "pid": 21312, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": { "name": "BuildAsync" } },
{ "pid": 21312, "tid": 47244640256, "ts": 1753105552000488, "dur": 18770, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 21312, "tid": 47244640256, "ts": 1753105552000580, "dur": 2629, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 21312, "tid": 47244640256, "ts": 1753105552019262, "dur": 1456653, "ph": "X", "name": "await ExecuteBuildProgram", "args": {} },
{ "pid": 21312, "tid": 47244640256, "ts": 1753105553475926, "dur": 5478456, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 21312, "tid": 47244640256, "ts": 1753105553476009, "dur": 3186, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 21312, "tid": 47244640256, "ts": 1753105558954392, "dur": 157029, "ph": "X", "name": "await ExecuteBuildProgram", "args": {} },
{ "pid": 21312, "tid": 47244640256, "ts": 1753105559111434, "dur": 92137, "ph": "X", "name": "RunBackend", "args": {} },
{ "pid": 21312, "tid": 47244640256, "ts": 1753105559111542, "dur": 2905, "ph": "X", "name": "BackendProgram.Start", "args": {} },
{ "pid": 21312, "tid": 47244640256, "ts": 1753105559203574, "dur": 113, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559204837, "dur": 9, "ph": "X", "name": "BuildAsync", "args": {} },
{ "pid": 21312, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 21312, "tid": 1, "ts": 1753105551904020, "dur": 1132, "ph": "X", "name": "<Add>b__0", "args": {} },
{ "pid": 21312, "tid": 1, "ts": 1753105551905157, "dur": 94366, "ph": "X", "name": "<SetupBuildRequest>b__0", "args": {} },
{ "pid": 21312, "tid": 1, "ts": 1753105551999525, "dur": 944, "ph": "X", "name": "WriteJson", "args": {} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559204848, "dur": 4, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1753105559008998, "dur": 91261, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105559009827, "dur": 31948, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105559047137, "dur": 34052, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105559047460, "dur": 25520, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105559047894, "dur": 17532, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105559065722, "dur": 392, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105559067411, "dur": 4338, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105559086406, "dur": 2213, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105559088620, "dur": 11632, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105559089371, "dur": 9193, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105559104717, "dur": 910, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105559104390, "dur": 1391, "ph": "X", "name": "Write chrome-trace events", "args": {} },
{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1753105553495255, "dur":31734, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105553526994, "dur":2099, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105553529127, "dur":734, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105553529956, "dur":154, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Resources/unity_builtin_extra" }}
,{ "pid":12345, "tid":0, "ts":1753105553530388, "dur":155, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/UnityEngine.ClusterInputModule.dll" }}
,{ "pid":12345, "tid":0, "ts":1753105553529885, "dur":2286, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105553532172, "dur":5414372, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105558946547, "dur":418, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105558947117, "dur":2189, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1753105553529778, "dur":2400, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753105553532185, "dur":3452, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753105553537074, "dur":429, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/csharpactions/boot.config_tyr4.info" }}
,{ "pid":12345, "tid":1, "ts":1753105553537506, "dur":75, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win64\\Data\\boot.config" }}
,{ "pid":12345, "tid":1, "ts":1753105553537587, "dur":8420, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win64\\Data\\RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":1, "ts":1753105553546154, "dur":98, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\Player7b6476bc-inputdata.json" }}
,{ "pid":12345, "tid":1, "ts":1753105553546292, "dur":40455, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.BuildTools.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105553586749, "dur":1026, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Core.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105553587777, "dur":350913, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.CSharpSupport.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105553938691, "dur":808, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.DotNet.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105553939501, "dur":137, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.NativeProgramSupport.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105553939638, "dur":884, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Stevedore.Program.exe" }}
,{ "pid":12345, "tid":1, "ts":1753105553940522, "dur":342, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.TinyProfiler2.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105553940865, "dur":202945, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Toolchain.GNU.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554143811, "dur":112457, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Toolchain.LLVM.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554256270, "dur":337737, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Toolchain.VisualStudio.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554594008, "dur":274486, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Toolchain.Xcode.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554868495, "dur":685, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.Tools.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554869181, "dur":759, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.TundraBackend.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554869941, "dur":76, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Bee.VisualStudioSolution.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554870017, "dur":296, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\BeeBuildProgramCommon.Data.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554870314, "dur":242, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\BeeBuildProgramCommon.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554870557, "dur":755, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\BeeLocalCacheTool.exe" }}
,{ "pid":12345, "tid":1, "ts":1753105554871313, "dur":1494, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Newtonsoft.Json.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554872808, "dur":572, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\NiceIO.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554873381, "dur":244, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.Data.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554873626, "dur":112, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\PlayerBuildProgramLibrary.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554873738, "dur":276, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\ScriptCompilationBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554874015, "dur":419, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\ScriptCompilationBuildProgram.exe" }}
,{ "pid":12345, "tid":1, "ts":1753105554874434, "dur":855, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\SharpYaml.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554875290, "dur":473, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Api.Attributes.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554875764, "dur":875, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Cecil.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554876639, "dur":509, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Cecil.Mdb.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554877149, "dur":453, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Cecil.Pdb.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554877603, "dur":553, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Cecil.Rocks.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554878159, "dur":430, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.IL2CPP.Api.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554878589, "dur":777, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.IL2CPP.Bee.BuildLogic.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554879366, "dur":218, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.IL2CPP.Bee.IL2CPPExeCompileCppBuildProgram.Data.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554879585, "dur":283, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Linker.Api.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105554879869, "dur":485, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\BuildPipeline\\Unity.Options.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105553537505, "dur":1342850, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":1, "ts":1753105554880543, "dur":81576, "ph":"X", "name": "AddBootConfigGUID",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":1, "ts":1753105554964834, "dur":1950, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\boot.config" }}
,{ "pid":12345, "tid":1, "ts":1753105554964834, "dur":1951, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/boot.config" }}
,{ "pid":12345, "tid":1, "ts":1753105554966802, "dur":787, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/boot.config" }}
,{ "pid":12345, "tid":1, "ts":1753105554967591, "dur":3978985, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553530173, "dur":2034, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553532213, "dur":1079, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553533292, "dur":2353, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553537128, "dur":380, "ph":"X", "name": "WriteText",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/Data/app.info" }}
,{ "pid":12345, "tid":2, "ts":1753105553537769, "dur":2265, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/app.info" }}
,{ "pid":12345, "tid":2, "ts":1753105553540036, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Universal.Shaders.dll" }}
,{ "pid":12345, "tid":2, "ts":1753105553540035, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.RenderPipelines.Universal.Shaders.dll" }}
,{ "pid":12345, "tid":2, "ts":1753105553540109, "dur":314, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553540449, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553540582, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553540815, "dur":500, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553541325, "dur":574, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553541957, "dur":1131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553543098, "dur":206, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553543313, "dur":105, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553543422, "dur":722, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553544157, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553544420, "dur":181, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win64\\Data\\ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":2, "ts":1753105553544419, "dur":183, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/ScriptingAssemblies.json" }}
,{ "pid":12345, "tid":2, "ts":1753105553544602, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105553544796, "dur":5119, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/resources.assets" }}
,{ "pid":12345, "tid":2, "ts":1753105553549944, "dur":5396592, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553531020, "dur":1294, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553532315, "dur":1062, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553533377, "dur":2295, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553535673, "dur":202, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/browscap.ini" }}
,{ "pid":12345, "tid":3, "ts":1753105553535876, "dur":685, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553536582, "dur":138, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":3, "ts":1753105553536720, "dur":557, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553537291, "dur":101, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Transactions.dll" }}
,{ "pid":12345, "tid":3, "ts":1753105553537285, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Transactions.dll" }}
,{ "pid":12345, "tid":3, "ts":1753105553537393, "dur":1184, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553538603, "dur":721, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":3, "ts":1753105553538602, "dur":723, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Runtime.Serialization.dll" }}
,{ "pid":12345, "tid":3, "ts":1753105553539325, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553539404, "dur":141, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Configuration.dll" }}
,{ "pid":12345, "tid":3, "ts":1753105553539403, "dur":142, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Configuration.dll" }}
,{ "pid":12345, "tid":3, "ts":1753105553539546, "dur":262, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553539813, "dur":121, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1753105553539811, "dur":124, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.VisualScripting.Antlr3.Runtime.dll" }}
,{ "pid":12345, "tid":3, "ts":1753105553539936, "dur":348, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553540293, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553540399, "dur":875, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553541331, "dur":2123, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553543460, "dur":738, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553544206, "dur":413, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105553544638, "dur":5524, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/resources.assets.resS" }}
,{ "pid":12345, "tid":3, "ts":1753105553550193, "dur":5396347, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105553531105, "dur":1273, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105553532379, "dur":3300, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105553535681, "dur":305, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":4, "ts":1753105553535987, "dur":1085, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105553537077, "dur":83, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":4, "ts":1753105553537161, "dur":454, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105553537626, "dur":201, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ServiceModel.Internals.dll" }}
,{ "pid":12345, "tid":4, "ts":1753105553537620, "dur":208, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.ServiceModel.Internals.dll" }}
,{ "pid":12345, "tid":4, "ts":1753105553537829, "dur":785, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105553538620, "dur":324, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Numerics.dll" }}
,{ "pid":12345, "tid":4, "ts":1753105553538619, "dur":326, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Numerics.dll" }}
,{ "pid":12345, "tid":4, "ts":1753105553538946, "dur":306, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105553539264, "dur":1460, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.dll" }}
,{ "pid":12345, "tid":4, "ts":1753105553539264, "dur":1461, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Data.dll" }}
,{ "pid":12345, "tid":4, "ts":1753105553540726, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105553544205, "dur":2755, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":4, "ts":1753105553547046, "dur":3523, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/globalgamemanagers.assets" }}
,{ "pid":12345, "tid":4, "ts":1753105553550592, "dur":5395924, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553531148, "dur":1277, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553532425, "dur":3229, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553535744, "dur":2777, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":5, "ts":1753105553535663, "dur":2859, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/UnityCrashHandler64.exe" }}
,{ "pid":12345, "tid":5, "ts":1753105553538523, "dur":260, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553538789, "dur":139, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.IO.Compression.dll" }}
,{ "pid":12345, "tid":5, "ts":1753105553538789, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.IO.Compression.dll" }}
,{ "pid":12345, "tid":5, "ts":1753105553538930, "dur":174, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553539114, "dur":122, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.EnterpriseServices.dll" }}
,{ "pid":12345, "tid":5, "ts":1753105553539113, "dur":123, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.EnterpriseServices.dll" }}
,{ "pid":12345, "tid":5, "ts":1753105553539237, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553539298, "dur":729, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Core.dll" }}
,{ "pid":12345, "tid":5, "ts":1753105553539297, "dur":730, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Core.dll" }}
,{ "pid":12345, "tid":5, "ts":1753105553540028, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553540088, "dur":104, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipeline.Universal.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1753105553540087, "dur":106, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.RenderPipeline.Universal.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":5, "ts":1753105553540193, "dur":392, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553540591, "dur":220, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553540816, "dur":321, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553541147, "dur":396, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553541551, "dur":338, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553541896, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553541985, "dur":66, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553542061, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553542119, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553542176, "dur":1219, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553543403, "dur":699, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553544109, "dur":224, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553544345, "dur":32992, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\UnityPlayer.dll" }}
,{ "pid":12345, "tid":5, "ts":1753105553544345, "dur":32994, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/UnityPlayer.dll" }}
,{ "pid":12345, "tid":5, "ts":1753105553577340, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105553577508, "dur":5369017, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105553530256, "dur":2014, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105553532361, "dur":833, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105553533194, "dur":2480, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105553535684, "dur":13380, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.5\\web.config" }}
,{ "pid":12345, "tid":6, "ts":1753105553535675, "dur":13391, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/web.config" }}
,{ "pid":12345, "tid":6, "ts":1753105553549068, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105553549209, "dur":5397320, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105553530613, "dur":1767, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105553532381, "dur":3336, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105553535718, "dur":231, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":7, "ts":1753105553535949, "dur":1111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105553537074, "dur":32985, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp\\StagingArea\\WindowsPlayer.exe" }}
,{ "pid":12345, "tid":7, "ts":1753105553537067, "dur":32993, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager.exe" }}
,{ "pid":12345, "tid":7, "ts":1753105553570062, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105553570180, "dur":1518, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager.exe" }}
,{ "pid":12345, "tid":7, "ts":1753105553571744, "dur":5374817, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105553530452, "dur":1834, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105553532287, "dur":631, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105553532918, "dur":2767, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105553535688, "dur":230, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/settings.map" }}
,{ "pid":12345, "tid":8, "ts":1753105553535919, "dur":1023, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105553536984, "dur":40771, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\EmbedRuntime\\MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":8, "ts":1753105553536954, "dur":40803, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":8, "ts":1753105553577757, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105553577865, "dur":5368667, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105553530477, "dur":1923, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105553532400, "dur":3319, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105553535737, "dur":16719, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\2.0\\machine.config" }}
,{ "pid":12345, "tid":9, "ts":1753105553535720, "dur":16738, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":9, "ts":1753105553552459, "dur":127, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105553552598, "dur":5393966, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1753105553530700, "dur":1673, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1753105553532374, "dur":3277, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1753105553535766, "dur":156, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\mono\\Managed\\UnityEngine.NVIDIAModule.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553536021, "dur":180, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Assembly-CSharp.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553536202, "dur":65, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.IK.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553536268, "dur":421, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553536691, "dur":92, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Universal.Config.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553536784, "dur":267, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Flow.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553537052, "dur":62, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Core.ShaderLibrary.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553537115, "dur":285, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Collections.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553537401, "dur":133, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.SpriteShape.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553537535, "dur":59, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.PixelPerfect.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553537595, "dur":286, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.TextMeshPro.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553537882, "dur":89, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553537972, "dur":520, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.RenderPipelines.Universal.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553538492, "dur":262, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Burst.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553538755, "dur":616, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.Core.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553539371, "dur":184, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.Animation.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553539556, "dur":128, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.Tilemap.Extras.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553539685, "dur":251, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553539937, "dur":63, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.InternalAPIEngineBridge.001.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553540001, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.2D.Common.Runtime.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553540138, "dur":102, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553540241, "dur":550, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":10, "ts":1753105553540831, "dur":84, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"ProjectSettings\\BurstAotSettings_StandaloneWindows.json" }}
,{ "pid":12345, "tid":10, "ts":1753105553540943, "dur":383596, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\PackageCache\\com.unity.burst@1.8.15\\.Runtime\\bcl.exe" }}
,{ "pid":12345, "tid":10, "ts":1753105553535660, "dur":388881, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":10, "ts":1753105553925407, "dur":5018905, "ph":"X", "name": "GenerateNativePluginsForAssemblies",  "args": { "detail":"Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":11, "ts":1753105553530726, "dur":1707, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753105553532434, "dur":3249, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753105553535697, "dur":13545, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.0\\web.config" }}
,{ "pid":12345, "tid":11, "ts":1753105553535683, "dur":13561, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/web.config" }}
,{ "pid":12345, "tid":11, "ts":1753105553549244, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753105553549324, "dur":5397198, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553530843, "dur":1561, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553532405, "dur":3277, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553535682, "dur":125, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":12, "ts":1753105553535808, "dur":1311, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553537141, "dur":1773, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.dll" }}
,{ "pid":12345, "tid":12, "ts":1753105553537133, "dur":1782, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.dll" }}
,{ "pid":12345, "tid":12, "ts":1753105553538915, "dur":340, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553539268, "dur":89, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":12, "ts":1753105553539267, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Data.DataSetExtensions.dll" }}
,{ "pid":12345, "tid":12, "ts":1753105553539358, "dur":69, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553539431, "dur":221, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Mono.Security.dll" }}
,{ "pid":12345, "tid":12, "ts":1753105553539430, "dur":223, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Mono.Security.dll" }}
,{ "pid":12345, "tid":12, "ts":1753105553539654, "dur":259, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553539921, "dur":72, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll" }}
,{ "pid":12345, "tid":12, "ts":1753105553539920, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.Collections.LowLevel.ILSupport.dll" }}
,{ "pid":12345, "tid":12, "ts":1753105553539994, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553540062, "dur":173, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Timeline.dll" }}
,{ "pid":12345, "tid":12, "ts":1753105553540061, "dur":174, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.Timeline.dll" }}
,{ "pid":12345, "tid":12, "ts":1753105553540236, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553540301, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553540464, "dur":225, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553540694, "dur":470, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553541171, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553541340, "dur":189, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553541548, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553541767, "dur":331, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553542193, "dur":599, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553542800, "dur":78, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553542929, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553543028, "dur":156, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553543189, "dur":75, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553543329, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553543426, "dur":696, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553544157, "dur":195, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105553544396, "dur":5412, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/sharedassets0.assets" }}
,{ "pid":12345, "tid":12, "ts":1753105553549847, "dur":5396673, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553530856, "dur":1552, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553532409, "dur":3266, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553535675, "dur":207, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/settings.map" }}
,{ "pid":12345, "tid":13, "ts":1753105553535883, "dur":1205, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553537099, "dur":3144, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\mscorlib.dll" }}
,{ "pid":12345, "tid":13, "ts":1753105553537092, "dur":3152, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/mscorlib.dll" }}
,{ "pid":12345, "tid":13, "ts":1753105553540244, "dur":162, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553540412, "dur":132, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553540552, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553540613, "dur":53, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553540717, "dur":541, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553541267, "dur":491, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553541766, "dur":76, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553541851, "dur":396, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553542257, "dur":854, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553543118, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553543273, "dur":102, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553543379, "dur":727, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553544113, "dur":403, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553544523, "dur":1487, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\PlayerDataCache\\Win64\\Data\\RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":13, "ts":1753105553544523, "dur":1489, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/RuntimeInitializeOnLoads.json" }}
,{ "pid":12345, "tid":13, "ts":1753105553546013, "dur":171, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105553546216, "dur":3954, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/level0" }}
,{ "pid":12345, "tid":13, "ts":1753105553550210, "dur":5396367, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1753105553530903, "dur":1443, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1753105553532347, "dur":745, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1753105553533092, "dur":2571, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1753105553535670, "dur":102, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/config" }}
,{ "pid":12345, "tid":14, "ts":1753105553535773, "dur":601, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1753105553536379, "dur":81, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":14, "ts":1753105553536461, "dur":532, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1753105553537005, "dur":55557, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\EmbedRuntime\\mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":14, "ts":1753105553536998, "dur":55566, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":14, "ts":1753105553592565, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1753105553592663, "dur":5353942, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553529880, "dur":2305, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553532191, "dur":638, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553532829, "dur":2872, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553535703, "dur":179, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":15, "ts":1753105553535883, "dur":1284, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553537177, "dur":2148, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.dll" }}
,{ "pid":12345, "tid":15, "ts":1753105553537171, "dur":2155, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Xml.dll" }}
,{ "pid":12345, "tid":15, "ts":1753105553539326, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553539425, "dur":243, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":15, "ts":1753105553539424, "dur":245, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.ComponentModel.Composition.dll" }}
,{ "pid":12345, "tid":15, "ts":1753105553539670, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553539762, "dur":71, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.Unsafe.dll" }}
,{ "pid":12345, "tid":15, "ts":1753105553539761, "dur":73, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.Burst.Unsafe.dll" }}
,{ "pid":12345, "tid":15, "ts":1753105553539834, "dur":214, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553540056, "dur":732, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":15, "ts":1753105553540054, "dur":734, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.Mathematics.dll" }}
,{ "pid":12345, "tid":15, "ts":1753105553540789, "dur":97, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553540891, "dur":561, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553541462, "dur":254, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553541769, "dur":1078, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553542874, "dur":181, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553543063, "dur":81, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553543152, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553543277, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553543346, "dur":739, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553544099, "dur":84, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553544188, "dur":335, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105553544598, "dur":2320, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Resources/unity_builtin_extra" }}
,{ "pid":12345, "tid":15, "ts":1753105553546990, "dur":4594, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/globalgamemanagers.assets.resS" }}
,{ "pid":12345, "tid":15, "ts":1753105553551603, "dur":5394968, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1753105553531061, "dur":1296, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1753105553532358, "dur":3299, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1753105553535675, "dur":32722, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\mconfig\\config.xml" }}
,{ "pid":12345, "tid":16, "ts":1753105553535659, "dur":32740, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":16, "ts":1753105553568400, "dur":223, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1753105553568644, "dur":5377909, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553530071, "dur":2128, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553532206, "dur":671, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553532878, "dur":2821, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553535701, "dur":237, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":17, "ts":1753105553535939, "dur":1244, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553537189, "dur":179, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553537186, "dur":183, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Xml.Linq.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553537369, "dur":864, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553538240, "dur":342, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Security.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553538239, "dur":344, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Security.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553538584, "dur":131, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553538722, "dur":175, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Net.Http.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553538722, "dur":176, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Net.Http.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553538898, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553538963, "dur":112, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553538962, "dur":114, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.IO.Compression.FileSystem.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553539076, "dur":57, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553539136, "dur":368, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\System.Drawing.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553539135, "dur":370, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Drawing.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553539505, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553539582, "dur":107, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\netstandard.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553539581, "dur":109, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/netstandard.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553539690, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553539754, "dur":66, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\MonoBleedingEdge\\lib\\mono\\unityjit-win32\\Facades\\System.Runtime.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553539753, "dur":67, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Runtime.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553539821, "dur":99, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553539924, "dur":98, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\Bee\\PlayerScriptAssemblies\\Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553539923, "dur":99, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.VisualScripting.State.dll" }}
,{ "pid":12345, "tid":17, "ts":1753105553540023, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553540196, "dur":164, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553540392, "dur":284, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553540681, "dur":499, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553541185, "dur":161, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553541352, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553541409, "dur":255, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553541674, "dur":257, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553541936, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553542063, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553542188, "dur":548, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553542750, "dur":146, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553542904, "dur":237, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553543149, "dur":112, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553543271, "dur":91, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553543367, "dur":720, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553544101, "dur":243, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105553544396, "dur":4304, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/sharedassets0.assets.resS" }}
,{ "pid":12345, "tid":17, "ts":1753105553548950, "dur":1716, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/globalgamemanagers" }}
,{ "pid":12345, "tid":17, "ts":1753105553550693, "dur":5395850, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1753105553529904, "dur":2289, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1753105553532199, "dur":728, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1753105553532927, "dur":2748, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1753105553535681, "dur":13653, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.5\\machine.config" }}
,{ "pid":12345, "tid":18, "ts":1753105553535676, "dur":13660, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":18, "ts":1753105553549336, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1753105553549407, "dur":5397131, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1753105553530998, "dur":1354, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1753105553532353, "dur":359, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1753105553532712, "dur":2983, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1753105553535721, "dur":16281, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.0\\machine.config" }}
,{ "pid":12345, "tid":19, "ts":1753105553535697, "dur":16307, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":19, "ts":1753105553552006, "dur":80, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1753105553552094, "dur":5394457, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1753105553531077, "dur":1299, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1753105553532376, "dur":3336, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1753105553535724, "dur":13465, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\2.0\\web.config" }}
,{ "pid":12345, "tid":20, "ts":1753105553535712, "dur":13479, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/web.config" }}
,{ "pid":12345, "tid":20, "ts":1753105553549191, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1753105553549284, "dur":5397347, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105558952274, "dur":1682, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "cat":"", "pid":12345, "tid":0, "ts":0, "ph":"M", "name":"process_name", "args": { "name":"bee_backend" } }
,{ "pid":12345, "tid":0, "ts":1753105559130900, "dur":27667, "ph":"X", "name": "DriverInitData",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105559158573, "dur":130, "ph":"X", "name": "RemoveStaleOutputs",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105559158730, "dur":498, "ph":"X", "name": "BuildQueueInit",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105559160372, "dur":64, "ph":"X", "name": "EmitFirstTimeEnqueue",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Unity.2D.IK.Runtime.dll" }}
,{ "pid":12345, "tid":0, "ts":1753105559159279, "dur":2099, "ph":"X", "name": "EnqueueRequestedNodes",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105559161380, "dur":38452, "ph":"X", "name": "WaitForBuildFinished", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105559199833, "dur":286, "ph":"X", "name": "JoinBuildThread",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105559200342, "dur":1573, "ph":"X", "name": "Tundra",  "args": { "detail":"Write AllBuiltNodes" }}
,{ "pid":12345, "tid":1, "ts":1753105559159344, "dur":2050, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753105559161399, "dur":651, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753105559162050, "dur":2956, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753105559165192, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753105559165253, "dur":51, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\EmbedRuntime\\MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105559165252, "dur":53, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105559165347, "dur":111, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753105559165532, "dur":66, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/UnityEngine.UI.dll" }}
,{ "pid":12345, "tid":1, "ts":1753105559165601, "dur":119, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753105559165867, "dur":124, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":1, "ts":1753105559165994, "dur":33849, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105559159648, "dur":1958, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105559161679, "dur":554, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105559162233, "dur":2939, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105559165190, "dur":86, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.0\\machine.config" }}
,{ "pid":12345, "tid":2, "ts":1753105559165180, "dur":97, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/machine.config" }}
,{ "pid":12345, "tid":2, "ts":1753105559165278, "dur":98, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105559165532, "dur":137, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105559165704, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105559165799, "dur":82, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105559165885, "dur":212, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":2, "ts":1753105559166098, "dur":33772, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105559159344, "dur":2043, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105559161393, "dur":3593, "ph":"X", "name": "CheckDagSignatures",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105559165049, "dur":261, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"AddBootConfigGUID Library/Bee/artifacts/WinPlayerBuildProgram/boot.config" }}
,{ "pid":12345, "tid":3, "ts":1753105559165311, "dur":86, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105559165441, "dur":144, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105559165692, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105559165848, "dur":73, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":3, "ts":1753105559165925, "dur":33958, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105559159814, "dur":1887, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105559161702, "dur":3539, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105559165251, "dur":111, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":4, "ts":1753105559165363, "dur":96, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105559165499, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105559165617, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105559165732, "dur":375, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":4, "ts":1753105559166108, "dur":33773, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105559159613, "dur":1915, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105559161535, "dur":932, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105559162467, "dur":2679, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105559165151, "dur":58, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":5, "ts":1753105559165209, "dur":109, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105559165341, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105559165466, "dur":91, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/System.Core.dll" }}
,{ "pid":12345, "tid":5, "ts":1753105559165561, "dur":143, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105559165712, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105559165783, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105559165848, "dur":152, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":5, "ts":1753105559166002, "dur":33847, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105559159533, "dur":1881, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105559161420, "dur":708, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105559162128, "dur":3029, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105559165167, "dur":223, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":6, "ts":1753105559165391, "dur":87, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105559165499, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105559165561, "dur":59, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105559165627, "dur":60, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105559165864, "dur":238, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":6, "ts":1753105559166103, "dur":33727, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105559159418, "dur":1982, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105559161404, "dur":626, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105559162031, "dur":3190, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105559165239, "dur":119, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\2.0\\machine.config" }}
,{ "pid":12345, "tid":7, "ts":1753105559165230, "dur":129, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":7, "ts":1753105559165360, "dur":151, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105559165511, "dur":69, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/machine.config" }}
,{ "pid":12345, "tid":7, "ts":1753105559165584, "dur":172, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105559165784, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105559165851, "dur":85, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":7, "ts":1753105559165940, "dur":33918, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105559159511, "dur":1895, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105559161412, "dur":651, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105559162063, "dur":3066, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105559165155, "dur":80, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\4.5\\machine.config" }}
,{ "pid":12345, "tid":8, "ts":1753105559165147, "dur":89, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.5/machine.config" }}
,{ "pid":12345, "tid":8, "ts":1753105559165236, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105559165321, "dur":67, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105559165397, "dur":65, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105559165504, "dur":68, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105559165634, "dur":145, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":8, "ts":1753105559165813, "dur":56, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/UnityEngine.GridModule.dll" }}
,{ "pid":12345, "tid":8, "ts":1753105559165870, "dur":138, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\UnityPlayer.dll" }}
,{ "pid":12345, "tid":8, "ts":1753105559165869, "dur":140, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/UnityPlayer.dll" }}
,{ "pid":12345, "tid":8, "ts":1753105559166028, "dur":33838, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105559159561, "dur":1952, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105559161529, "dur":678, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105559162207, "dur":2991, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105559165206, "dur":135, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/Browsers/Compat.browser" }}
,{ "pid":12345, "tid":9, "ts":1753105559165342, "dur":88, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105559165450, "dur":55, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105559165506, "dur":85, "ph":"X", "name": "EmitNodeUpToDate",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Managed/Mono.Security.dll" }}
,{ "pid":12345, "tid":9, "ts":1753105559165595, "dur":136, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105559165735, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105559165881, "dur":64, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":9, "ts":1753105559165948, "dur":33893, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1753105559159637, "dur":1920, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1753105559161571, "dur":602, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1753105559162173, "dur":3034, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1753105559165298, "dur":130, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1753105559165524, "dur":90, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1753105559165655, "dur":153, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1753105559165907, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":10, "ts":1753105559165964, "dur":33882, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753105559159661, "dur":1975, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753105559161637, "dur":105, "ph":"X", "name": "EarlyStatNonGeneratedFile",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753105559161742, "dur":3487, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753105559165311, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753105559165410, "dur":61, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753105559165520, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753105559165660, "dur":120, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753105559165874, "dur":244, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":11, "ts":1753105559166119, "dur":33736, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105559159684, "dur":2031, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105559161715, "dur":3552, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105559165284, "dur":83, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Temp\\StagingArea\\WindowsPlayer.exe" }}
,{ "pid":12345, "tid":12, "ts":1753105559165277, "dur":91, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager.exe" }}
,{ "pid":12345, "tid":12, "ts":1753105559165368, "dur":70, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105559165450, "dur":115, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105559165576, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105559165789, "dur":128, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":12, "ts":1753105559165920, "dur":33913, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105559159768, "dur":1910, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105559161678, "dur":3341, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105559165030, "dur":96, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\etc\\mono\\mconfig\\config.xml" }}
,{ "pid":12345, "tid":13, "ts":1753105559165020, "dur":107, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/mconfig/config.xml" }}
,{ "pid":12345, "tid":13, "ts":1753105559165127, "dur":52, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105559165184, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/4.0/DefaultWsdlHelpGenerator.aspx" }}
,{ "pid":12345, "tid":13, "ts":1753105559165293, "dur":74, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105559165374, "dur":140, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105559165593, "dur":203, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105559165799, "dur":56, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":13, "ts":1753105559165937, "dur":33891, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1753105559159793, "dur":1894, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1753105559161687, "dur":3591, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1753105559165286, "dur":76, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Resources/unity default resources" }}
,{ "pid":12345, "tid":14, "ts":1753105559165462, "dur":178, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1753105559165784, "dur":117, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":14, "ts":1753105559165941, "dur":33903, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105559159892, "dur":1828, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105559161720, "dur":3534, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105559165268, "dur":102, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\Variations\\win64_player_nondevelopment_mono\\MonoBleedingEdge\\EmbedRuntime\\mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":15, "ts":1753105559165263, "dur":108, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/EmbedRuntime/mono-2.0-bdwgc.dll" }}
,{ "pid":12345, "tid":15, "ts":1753105559165371, "dur":92, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105559165494, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105559165555, "dur":89, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105559165666, "dur":54, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105559165724, "dur":142, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105559165872, "dur":58, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":15, "ts":1753105559165936, "dur":33902, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1753105559159840, "dur":1835, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1753105559161676, "dur":3646, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1753105559165375, "dur":163, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1753105559165580, "dur":106, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1753105559165726, "dur":122, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1753105559165857, "dur":138, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":16, "ts":1753105559165997, "dur":33881, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105559159866, "dur":1813, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105559161679, "dur":3613, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105559165310, "dur":168, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105559165518, "dur":71, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105559165654, "dur":194, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":17, "ts":1753105559165921, "dur":33904, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1753105559159912, "dur":1754, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1753105559161666, "dur":3334, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1753105559165265, "dur":77, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"C:\\Users\\<USER>\\Hydra Games\\Void Voyager\\Library\\PackageCache\\com.unity.burst@1.8.15\\.Runtime\\bcl.exe" }}
,{ "pid":12345, "tid":18, "ts":1753105559165006, "dur":337, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"GenerateNativePluginsForAssemblies Library/Bee/artifacts/WinPlayerBuildProgram/AsyncPluginsFromLinker" }}
,{ "pid":12345, "tid":18, "ts":1753105559165343, "dur":158, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":18, "ts":1753105559166125, "dur":31250, "ph":"X", "name": "ComputeFileSignatureSha1",  "args": { "detail":"Library\\Bee\\artifacts\\WinPlayerBuildProgram\\AsyncPluginsFromLinker\\x86_64\\lib_burst_generated.dll" }}
,{ "pid":12345, "tid":18, "ts":1753105559166125, "dur":31265, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":18, "ts":1753105559198169, "dur":1582, "ph":"X", "name": "CopyFiles",  "args": { "detail":"C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/Void Voyager_Data/Plugins/x86_64/lib_burst_generated.dll" }}
,{ "pid":12345, "tid":19, "ts":1753105559159929, "dur":1727, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1753105559161656, "dur":3343, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1753105559165219, "dur":98, "ph":"X", "name": "CheckInputSignature",  "args": { "detail":"CopyFiles C:/Users/<USER>/Desktop/Computer/Cascade Games/Void Voyager/Build/MonoBleedingEdge/etc/mono/2.0/settings.map" }}
,{ "pid":12345, "tid":19, "ts":1753105559165318, "dur":93, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1753105559165609, "dur":104, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":19, "ts":1753105559165915, "dur":33919, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1753105559159974, "dur":1676, "ph":"X", "name": "FirstLock",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1753105559161651, "dur":3659, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1753105559165325, "dur":50, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1753105559165430, "dur":160, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1753105559165785, "dur":110, "ph":"X", "name": "OutputFilesMissingFor",  "args": { "detail":"" }}
,{ "pid":12345, "tid":20, "ts":1753105559165943, "dur":33884, "ph":"X", "name": "WaitingForWork", "cname":"thread_state_sleeping",  "args": { "detail":"" }}
,{ "pid":12345, "tid":0, "ts":1753105559204053, "dur":549, "ph":"X", "name": "ProfilerWriteOutput" }
,{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_name", "args": { "name": "netcorerun.dll" } },
{ "pid": 35942, "tid": -1, "ph": "M", "name": "process_sort_index", "args": { "sort_index": "-1" } },
{ "pid": 35942, "tid": 1, "ph": "M", "name": "thread_name", "args": { "name": "" } },
{ "pid": 35942, "tid": 1, "ts": 1753105553367884, "dur": 96609, "ph": "X", "name": "BuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105553368681, "dur": 30438, "ph": "X", "name": "BuildProgramContextConstructor", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105553404262, "dur": 42436, "ph": "X", "name": "RunBuildProgram", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105553404543, "dur": 27974, "ph": "X", "name": "PlayerBuildProgramBase.SetupPlayerBuild", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105553404966, "dur": 18411, "ph": "X", "name": "SetupDataFiles", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105553423720, "dur": 411, "ph": "X", "name": "SetupCopyPlugins", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105553426659, "dur": 3760, "ph": "X", "name": "SetupUnityLinker", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105553451890, "dur": 1596, "ph": "X", "name": "OutputData.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105553453487, "dur": 11001, "ph": "X", "name": "Backend.Write", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105553454254, "dur": 9004, "ph": "X", "name": "JsonToString", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105553469533, "dur": 900, "ph": "X", "name": "", "args": {} },
{ "pid": 35942, "tid": 1, "ts": 1753105553469033, "dur": 1578, "ph": "X", "name": "Write chrome-trace events", "args": {} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559204888, "dur": 22, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram1.traceevents"} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559205222, "dur": 319, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend2.traceevents"} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559206066, "dur": 40, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend3.traceevents"} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559215772, "dur": 42, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "buildprogram0.traceevents"} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559216028, "dur": 17, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559204988, "dur": 234, "ph": "X", "name": "buildprogram1.traceevents", "args": {} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559205606, "dur": 459, "ph": "X", "name": "backend2.traceevents", "args": {} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559206161, "dur": 9609, "ph": "X", "name": "backend3.traceevents", "args": {} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559215883, "dur": 144, "ph": "X", "name": "buildprogram0.traceevents", "args": {} },
{ "pid": 21312, "tid": 6028, "ts": 1753105559204015, "dur": 12093, "ph": "X", "name": "Write chrome-trace events", "args": {} },
