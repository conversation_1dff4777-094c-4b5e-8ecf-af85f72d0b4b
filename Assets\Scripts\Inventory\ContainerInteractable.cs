using UnityEngine;

/// <summary>
/// Makes an object interactable as a container that can be opened with E key
/// </summary>
public class ContainerInteractable : MonoBehaviour
{
    [Header("Interaction Settings")]
    [Tooltip("Range within which player can interact with this container")]
    public float interactionRange = 2f;
    
    [Head<PERSON>("Container References")]
    [Toolt<PERSON>("The container inventory component")]
    public ContainerInventory containerInventory;
    
    [<PERSON><PERSON>("Visual Feedback")]
    [Tooltip("Optional sprite renderer for visual feedback")]
    public SpriteRenderer spriteRenderer;
    
    [Toolt<PERSON>("Color to tint the container when it's interactable")]
    public Color highlightColor = Color.white;
    
    private Color originalColor;
    private bool isHighlighted = false;
    private DualInventoryUI dualInventoryUI;
    
    private void Start()
    {
        // Get container inventory if not assigned
        if (containerInventory == null)
        {
            containerInventory = GetComponent<ContainerInventory>();
        }
        
        // Get sprite renderer if not assigned
        if (spriteRenderer == null)
        {
            spriteRenderer = GetComponent<SpriteRenderer>();
        }
        
        // Store original color
        if (spriteRenderer != null)
        {
            originalColor = spriteRenderer.color;
        }
        
        // Find the dual inventory UI in the scene
        dualInventoryUI = FindObjectOfType<DualInventoryUI>();
        if (dualInventoryUI == null)
        {
            Debug.LogWarning("No DualInventoryUI found in scene. Container interaction may not work properly.");
        }
    }
    
    /// <summary>
    /// Open this container's inventory alongside the player's inventory
    /// </summary>
    public void OpenContainer()
    {
        if (containerInventory == null)
        {
            Debug.LogError($"Container {name} has no ContainerInventory component!");
            return;
        }
        
        if (dualInventoryUI == null)
        {
            Debug.LogError("No DualInventoryUI found! Cannot open container.");
            return;
        }
        
        Debug.Log($"Opening container: {name}");
        dualInventoryUI.OpenDualInventory(containerInventory);
    }
    
    /// <summary>
    /// Show visual feedback that this container can be interacted with
    /// </summary>
    public void ShowInteractionFeedback()
    {
        if (isHighlighted || spriteRenderer == null) return;
        
        isHighlighted = true;
        spriteRenderer.color = highlightColor;
    }
    
    /// <summary>
    /// Hide visual feedback
    /// </summary>
    public void HideInteractionFeedback()
    {
        if (!isHighlighted || spriteRenderer == null) return;
        
        isHighlighted = false;
        spriteRenderer.color = originalColor;
    }
    
    /// <summary>
    /// Check if player is within interaction range
    /// </summary>
    public bool IsPlayerInRange(Transform playerTransform)
    {
        if (playerTransform == null) return false;
        
        float distance = Vector2.Distance(transform.position, playerTransform.position);
        return distance <= interactionRange;
    }
    
    private void OnDrawGizmosSelected()
    {
        // Draw interaction range in scene view
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireCircle(transform.position, interactionRange);
    }
}
