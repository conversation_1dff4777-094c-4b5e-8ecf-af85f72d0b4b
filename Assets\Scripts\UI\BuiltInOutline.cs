using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(RectTransform))]
public class BuiltInOutline : MonoBehaviour
{
    private Image[] outlineImages;
    private RectTransform rectTransform;
    private Image sourceImage;
    
    [SerializeField] private Color outlineColor = new Color(1f, 1f, 1f, 1f);
    [SerializeField] private float outlineWidth = 1f;
    
    private void Awake()
    {
        rectTransform = GetComponent<RectTransform>();
        sourceImage = GetComponent<Image>();
        if (sourceImage == null)
        {
            Debug.LogError("BuiltInOutline requires an Image component on the same GameObject");
            enabled = false;
            return;
        }
        CreateOutlineImages();
        SetOutlineActive(false);
    }
    
    private void CreateOutlineImages()
    {
        // Create outline parts (top, right, bottom, left)
        outlineImages = new Image[4];
        Vector2[] directions = new Vector2[]
        {
            new Vector2(0, 1),   // Top
            new Vector2(1, 0),   // Right
            new Vector2(0, -1),  // Bottom
            new Vector2(-1, 0)   // Left
        };
        
        // Get the parent Canvas to ensure proper scaling
        Canvas canvas = GetComponentInParent<Canvas>();
        float scaleFactor = canvas != null ? canvas.scaleFactor : 1f;
        
        for (int i = 0; i < 4; i++)
        {
            GameObject outlineObj = new GameObject($"Outline_{i}");
            outlineObj.transform.SetParent(transform);
            
            RectTransform outlineRect = outlineObj.AddComponent<RectTransform>();
            
            // Match the source RectTransform settings
            outlineRect.pivot = rectTransform.pivot;
            outlineRect.anchorMin = Vector2.zero;
            outlineRect.anchorMax = Vector2.one;
            outlineRect.sizeDelta = Vector2.zero;
            outlineRect.offsetMin = Vector2.zero;
            outlineRect.offsetMax = Vector2.zero;
            outlineRect.localScale = Vector3.one;
            outlineRect.localRotation = Quaternion.identity;
            
            Image outlineImage = outlineObj.AddComponent<Image>();
            outlineImage.sprite = sourceImage.sprite;
            outlineImage.type = sourceImage.type;
            outlineImage.preserveAspect = sourceImage.preserveAspect;
            outlineImage.raycastTarget = false; // Don't block raycasts to text elements
            outlineImage.color = outlineColor;
            
            // Position the outline part
            Vector2 offset = directions[i] * (outlineWidth / scaleFactor);
            outlineRect.anchoredPosition = offset;
            
            outlineImages[i] = outlineImage;
        }
    }
    
    public void SetOutlineColor(Color color)
    {
        outlineColor = color;
        if (outlineImages != null)
        {
            foreach (var image in outlineImages)
            {
                if (image != null)
                {
                    image.color = color;
                }
            }
        }
    }
    
    public void SetOutlineWidth(float width)
    {
        outlineWidth = width;
        if (outlineImages != null)
        {
            // Get the current canvas scale factor
            Canvas canvas = GetComponentInParent<Canvas>();
            float scaleFactor = canvas != null ? canvas.scaleFactor : 1f;
            
            Vector2[] directions = new Vector2[]
            {
                new Vector2(0, 1),   // Top
                new Vector2(1, 0),   // Right
                new Vector2(0, -1),  // Bottom
                new Vector2(-1, 0)   // Left
            };
            
            for (int i = 0; i < outlineImages.Length; i++)
            {
                if (outlineImages[i] != null)
                {
                    Vector2 offset = directions[i] * (width / scaleFactor);
                    outlineImages[i].rectTransform.anchoredPosition = offset;
                }
            }
        }
    }
    
    public void SetOutlineActive(bool active)
    {
        if (outlineImages != null)
        {
            foreach (var image in outlineImages)
            {
                if (image != null)
                {
                    image.enabled = active;
                }
            }
        }
    }
    
    private void OnDestroy()
    {
        if (outlineImages != null)
        {
            foreach (var image in outlineImages)
            {
                if (image != null && image.gameObject != null)
                {
                    Destroy(image.gameObject);
                }
            }
        }
    }
    
    // Update outline when source image changes
    private void LateUpdate()
    {
        if (sourceImage == null || outlineImages == null) return;
        
        foreach (var outlineImage in outlineImages)
        {
            if (outlineImage != null)
            {
                outlineImage.sprite = sourceImage.sprite;
                outlineImage.type = sourceImage.type;
                outlineImage.preserveAspect = sourceImage.preserveAspect;
            }
        }
    }
} 