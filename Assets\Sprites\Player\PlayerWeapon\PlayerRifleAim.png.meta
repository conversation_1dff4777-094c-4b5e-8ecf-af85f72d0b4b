fileFormatVersion: 2
guid: c7b12578ccf8d9143b5953c7f52f3ea1
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 30
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: PlayerRifleAim_0
      rect:
        serializedVersion: 2
        x: 0
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 3ded5da04b0d2b942aae88456df2b9fc
      internalID: -1062876265
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_1
      rect:
        serializedVersion: 2
        x: 26
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0ac939c16c672db43ab1738ac045e27e
      internalID: -1691012827
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_2
      rect:
        serializedVersion: 2
        x: 52
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7a87401200d931045bb723ec62920231
      internalID: -930276316
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_3
      rect:
        serializedVersion: 2
        x: 78
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 76c62aca6038a42489ff827ceb063ad2
      internalID: -1505566029
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_4
      rect:
        serializedVersion: 2
        x: 0
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 36647e3fb7c9b1549ad8c49e03a60113
      internalID: 698052241
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_5
      rect:
        serializedVersion: 2
        x: 26
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9959346145f21ab4b96dc3071708dc75
      internalID: 1806737535
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_6
      rect:
        serializedVersion: 2
        x: 52
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9586afd2dcaaeb94e9519986ee7bb116
      internalID: -765686064
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_7
      rect:
        serializedVersion: 2
        x: 78
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: acf994b201cb522499c4fca971667b9a
      internalID: 1117911680
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_8
      rect:
        serializedVersion: 2
        x: 0
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2a1832d927b5e07439cefbf6a253b6a1
      internalID: -168122283
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_9
      rect:
        serializedVersion: 2
        x: 26
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 00ce2d4a5f90609468266ef4ed222fd4
      internalID: 315640917
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_10
      rect:
        serializedVersion: 2
        x: 52
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ff344cf208a78b340997336758f7c4f8
      internalID: 1638583454
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_11
      rect:
        serializedVersion: 2
        x: 78
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 49049db8206d6b94ebbbef317b3fa778
      internalID: -36875001
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_12
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: fd0ab945251f8ec4f968629fe226cb39
      internalID: 1976156740
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_13
      rect:
        serializedVersion: 2
        x: 26
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 9c007678e7ac483469d20eea1c9cc837
      internalID: -1279723068
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_14
      rect:
        serializedVersion: 2
        x: 52
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1025b84a61da070468e2852cc0b3b4a1
      internalID: 1597873033
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: PlayerRifleAim_15
      rect:
        serializedVersion: 2
        x: 78
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1d8cbb804ebf5b6478641e8cb1d68bd1
      internalID: 1325139745
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      PlayerRifleAim_0: -1062876265
      PlayerRifleAim_1: -1691012827
      PlayerRifleAim_10: 1638583454
      PlayerRifleAim_11: -36875001
      PlayerRifleAim_12: 1976156740
      PlayerRifleAim_13: -1279723068
      PlayerRifleAim_14: 1597873033
      PlayerRifleAim_15: 1325139745
      PlayerRifleAim_2: -930276316
      PlayerRifleAim_3: -1505566029
      PlayerRifleAim_4: 698052241
      PlayerRifleAim_5: 1806737535
      PlayerRifleAim_6: -765686064
      PlayerRifleAim_7: 1117911680
      PlayerRifleAim_8: -168122283
      PlayerRifleAim_9: 315640917
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
