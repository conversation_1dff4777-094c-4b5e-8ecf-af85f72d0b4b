using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// One-click fix for inventory setup issues
/// </summary>
public class InventorySetupFixer : MonoBehaviour
{
    [Header("Quick Fix")]
    [Tooltip("Click this to automatically fix the inventory setup")]
    public bool fixInventorySetup = false;
    
    private void Start()
    {
        if (fixInventorySetup)
        {
            FixInventorySetup();
        }
    }
    
    [ContextMenu("Fix Inventory Setup")]
    public void FixInventorySetup()
    {
        Debug.Log("=== FIXING INVENTORY SETUP ===");
        
        // Step 1: Find or create DualInventoryPanel
        GameObject dualPanel = GameObject.Find("DualInventoryPanel");
        if (dualPanel == null)
        {
            Debug.LogError("DualInventoryPanel not found! Please create it first.");
            return;
        }
        
        DualInventoryUI dualUI = dualPanel.GetComponent<DualInventoryUI>();
        if (dualUI == null)
        {
            Debug.LogError("DualInventoryUI component not found on DualInventoryPanel!");
            return;
        }
        
        // Step 2: Find existing InventoryPanel (your current inventory)
        GameObject existingInventory = GameObject.Find("InventoryPanel");
        if (existingInventory == null)
        {
            // Try alternative names
            InventoryUI inventoryUI = FindObjectOfType<InventoryUI>();
            if (inventoryUI != null)
            {
                existingInventory = inventoryUI.gameObject;
                Debug.Log($"Found existing inventory: {existingInventory.name}");
            }
        }
        
        if (existingInventory == null)
        {
            Debug.LogError("Could not find existing InventoryPanel or InventoryUI!");
            return;
        }
        
        // Step 3: Move existing inventory under DualInventoryPanel if it's not already
        if (existingInventory.transform.parent != dualPanel.transform)
        {
            Debug.Log("Moving existing inventory under DualInventoryPanel...");
            existingInventory.transform.SetParent(dualPanel.transform, false);
            
            // Position it on the left side
            RectTransform existingRect = existingInventory.GetComponent<RectTransform>();
            if (existingRect != null)
            {
                existingRect.anchorMin = new Vector2(0, 0);
                existingRect.anchorMax = new Vector2(0.5f, 1);
                existingRect.offsetMin = new Vector2(10, 10);
                existingRect.offsetMax = new Vector2(-5, -10);
            }
        }
        
        // Step 4: Create ContainerInventoryPanel if it doesn't exist
        Transform containerPanel = dualPanel.transform.Find("ContainerInventoryPanel");
        if (containerPanel == null)
        {
            Debug.Log("Creating ContainerInventoryPanel...");
            GameObject containerPanelObj = new GameObject("ContainerInventoryPanel");
            containerPanelObj.transform.SetParent(dualPanel.transform, false);
            
            // Add RectTransform and position on right side
            RectTransform containerRect = containerPanelObj.AddComponent<RectTransform>();
            containerRect.anchorMin = new Vector2(0.5f, 0);
            containerRect.anchorMax = new Vector2(1, 1);
            containerRect.offsetMin = new Vector2(5, 10);
            containerRect.offsetMax = new Vector2(-10, -10);
            
            // Add background
            Image backgroundImage = containerPanelObj.AddComponent<Image>();
            backgroundImage.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);
            
            containerPanel = containerPanelObj.transform;
        }
        
        // Step 5: Set up DualInventoryUI references
        dualUI.dualInventoryPanel = dualPanel;
        dualUI.playerInventoryPanel = existingInventory;
        dualUI.containerInventoryPanel = containerPanel.gameObject;
        
        // Get InventoryUI component
        InventoryUI playerInventoryUI = existingInventory.GetComponent<InventoryUI>();
        if (playerInventoryUI != null)
        {
            dualUI.playerInventoryUI = playerInventoryUI;
        }
        else
        {
            Debug.LogError("InventoryUI component not found on player inventory panel!");
        }
        
        // Add ContainerInventoryUI if needed
        ContainerInventoryUI containerInventoryUI = containerPanel.GetComponent<ContainerInventoryUI>();
        if (containerInventoryUI == null)
        {
            containerInventoryUI = containerPanel.gameObject.AddComponent<ContainerInventoryUI>();
            Debug.Log("Added ContainerInventoryUI component");
        }
        dualUI.containerInventoryUI = containerInventoryUI;
        
        // Step 6: Set up ContainerInventoryUI references
        SetupContainerInventoryUI(containerInventoryUI, playerInventoryUI);
        
        // Step 7: Create close button if needed
        CreateCloseButton(dualUI, dualPanel);
        
        // Step 8: Fix PlayerController references
        FixPlayerControllerReferences(dualPanel, existingInventory);
        
        // Step 9: Hide dual inventory initially
        dualPanel.SetActive(false);
        
        Debug.Log("✓ Inventory setup fixed successfully!");
        Debug.Log("✓ DualInventoryPanel is ready");
        Debug.Log("✓ Player inventory moved to left side");
        Debug.Log("✓ Container inventory created on right side");
        Debug.Log("✓ All references assigned");
    }
    
    private void SetupContainerInventoryUI(ContainerInventoryUI containerUI, InventoryUI playerUI)
    {
        if (containerUI == null || playerUI == null) return;
        
        // Copy settings from player inventory
        containerUI.slotPrefab = playerUI.slotPrefab;
        containerUI.itemUIPrefab = playerUI.itemUIPrefab;
        containerUI.slotSize = playerUI.slotSize;
        
        // Create slot and item containers
        GameObject slotContainer = new GameObject("ChestSlotContainer");
        slotContainer.transform.SetParent(containerUI.transform, false);
        RectTransform slotRect = slotContainer.AddComponent<RectTransform>();
        slotRect.anchorMin = Vector2.zero;
        slotRect.anchorMax = Vector2.one;
        slotRect.offsetMin = Vector2.zero;
        slotRect.offsetMax = Vector2.zero;
        containerUI.slotContainer = slotContainer.transform;
        
        GameObject itemContainer = new GameObject("ChestItemContainer");
        itemContainer.transform.SetParent(containerUI.transform, false);
        RectTransform itemRect = itemContainer.AddComponent<RectTransform>();
        itemRect.anchorMin = Vector2.zero;
        itemRect.anchorMax = Vector2.one;
        itemRect.offsetMin = Vector2.zero;
        itemRect.offsetMax = Vector2.zero;
        containerUI.itemContainer = itemContainer.transform;
        
        Debug.Log("Set up ContainerInventoryUI references");
    }
    
    private void CreateCloseButton(DualInventoryUI dualUI, GameObject dualPanel)
    {
        if (dualUI.closeButton != null) return;
        
        GameObject closeButtonObj = new GameObject("CloseButton");
        closeButtonObj.transform.SetParent(dualPanel.transform, false);
        
        Button closeButton = closeButtonObj.AddComponent<Button>();
        Image buttonImage = closeButtonObj.AddComponent<Image>();
        buttonImage.color = Color.red;
        
        // Position in top-right corner
        RectTransform rect = closeButtonObj.GetComponent<RectTransform>();
        rect.anchorMin = new Vector2(1, 1);
        rect.anchorMax = new Vector2(1, 1);
        rect.pivot = new Vector2(1, 1);
        rect.anchoredPosition = new Vector2(-10, -10);
        rect.sizeDelta = new Vector2(50, 30);
        
        // Add text
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(closeButtonObj.transform, false);
        Text buttonText = textObj.AddComponent<Text>();
        buttonText.text = "X";
        buttonText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        buttonText.fontSize = 18;
        buttonText.color = Color.white;
        buttonText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform textRect = textObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        dualUI.closeButton = closeButton;
        Debug.Log("Created close button");
    }
    
    private void FixPlayerControllerReferences(GameObject dualPanel, GameObject playerInventory)
    {
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
        {
            playerController.dualInventoryUI = dualPanel;
            playerController.inventoryUI = playerInventory;
            Debug.Log("Fixed PlayerController references");
        }
        else
        {
            Debug.LogWarning("PlayerController not found!");
        }
    }
}
