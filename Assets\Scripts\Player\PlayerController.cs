using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

public class PlayerController : MonoBehaviour
{
    public float moveSpeed = 5f;
    public float sprintSpeed = 8f;
    public float acceleration = 10f;
    public float deceleration = 15f;
    public Camera cam;
    public GameObject inventoryUI; // Assign this in the Inspector
    public GameObject dualInventoryUI; // Reference to DualInventoryPanel for container interactions

    [Tooltip("Reference to the InventoryManager - leave empty to find automatically")]
    public InventoryManager inventoryManager; // Reference to InventoryManager

    private Rigidbody2D rb;
    private Vector2 movement;
    private Vector2 mousePos;
    private bool isSprinting = false;
    private bool isAiming = false;
    private Vector2 inputMovement;
    private Animator animator;
    [HideInInspector]
    public bool isInventoryOpen = false;

    void Start()
    {
        try
        {
            // Get required components
            if (rb == null) rb = GetComponent<Rigidbody2D>();
            if (animator == null) animator = GetComponent<Animator>();

            // Double check camera reference
            if (cam == null)
            {
                Debug.LogWarning("Camera reference is missing - attempting to find main camera");
                cam = Camera.main;
                if (cam == null)
                {
                    Debug.LogError("Could not find a main camera. Please assign one in the inspector.");
                }
            }

            // Try to find inventory manager if not set
            if (inventoryManager == null)
            {
                Debug.Log("Looking for InventoryManager on start");
                FindInventoryManager();
            }

            // Make sure inventoryUI is set
            if (inventoryUI == null)
            {
                Debug.LogWarning("Inventory UI reference is missing - attempting to find it");

                // Try to find by tag first (more reliable)
                GameObject uiObject = GameObject.FindGameObjectWithTag("InventoryUI");
                if (uiObject != null)
                {
                    inventoryUI = uiObject;
                    Debug.Log("Found InventoryUI by tag");
                }
                else
                {
                    // Try to find by name - now it's called InventoryPanel under DualInventoryPanel
                    uiObject = GameObject.Find("InventoryPanel");
                    if (uiObject == null)
                    {
                        // Try alternative names
                        uiObject = GameObject.Find("PlayerInventoryPanel");
                    }
                    if (uiObject != null)
                    {
                        inventoryUI = uiObject;
                        Debug.Log("Found InventoryUI by name");
                    }
                    else
                    {
                        Debug.LogError("Could not find inventory UI object in scene. Please assign it in the inspector.");
                    }
                }
            }

            // Find dual inventory UI if not assigned
            if (dualInventoryUI == null)
            {
                DualInventoryUI dualUIComponent = FindObjectOfType<DualInventoryUI>();
                if (dualUIComponent != null)
                {
                    dualInventoryUI = dualUIComponent.gameObject;
                    Debug.Log("Found DualInventoryUI component");
                }
                else
                {
                    // Try multiple ways to find the DualInventoryPanel
                    GameObject dualObject = GameObject.Find("DualInventoryPanel");
                    if (dualObject == null)
                    {
                        // Try finding by Canvas/DualInventoryPanel path
                        GameObject canvas = GameObject.Find("Canvas");
                        if (canvas != null)
                        {
                            Transform dualTransform = canvas.transform.Find("DualInventoryPanel");
                            if (dualTransform != null)
                            {
                                dualObject = dualTransform.gameObject;
                                Debug.Log("Found DualInventoryPanel under Canvas");
                            }
                        }
                    }

                    if (dualObject != null)
                    {
                        // Add DualInventoryUI component if it doesn't exist
                        DualInventoryUI dualUI = dualObject.GetComponent<DualInventoryUI>();
                        if (dualUI == null)
                        {
                            dualUI = dualObject.AddComponent<DualInventoryUI>();
                            Debug.Log("Added DualInventoryUI component to DualInventoryPanel");
                        }
                        dualInventoryUI = dualObject;
                        Debug.Log($"Found DualInventoryUI: {dualObject.name}");
                    }
                    else
                    {
                        // Last resort: try to find any DualInventoryUI component in the scene
                        DualInventoryUI anyDualUI = FindObjectOfType<DualInventoryUI>();
                        if (anyDualUI != null)
                        {
                            dualInventoryUI = anyDualUI.gameObject;
                            Debug.Log($"Found DualInventoryUI component on: {anyDualUI.gameObject.name}");
                        }
                        else
                        {
                            Debug.LogWarning("Could not find DualInventoryPanel or DualInventoryUI anywhere. Container interactions may not work.");
                        }
                    }
                }
            }

            // Hide inventory at start
            if (inventoryUI != null)
            {
                inventoryUI.SetActive(false);
                isInventoryOpen = false;
            }
            else
            {
                Debug.LogError("Inventory UI reference is still missing after attempts to find it.");
            }

            // Hide dual inventory at start
            if (dualInventoryUI != null)
            {
                dualInventoryUI.SetActive(false);
            }

            // Ensure cursor is visible and unlocked at the start
            Cursor.visible = true;
            Cursor.lockState = CursorLockMode.None;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in PlayerController.Start: {e.Message}\n{e.StackTrace}");
        }
    }

    private void FindInventoryManager()
    {
        // First check if it's already assigned in inspector
        if (inventoryManager != null)
        {
            Debug.Log("InventoryManager already assigned");
            return;
        }

        // Try to get it from this GameObject
        inventoryManager = GetComponent<InventoryManager>();

        // If still null, try finding it in the scene
        if (inventoryManager == null)
        {
            Debug.Log("InventoryManager not found on player, searching in scene...");
            inventoryManager = FindObjectOfType<InventoryManager>();

            if (inventoryManager != null)
            {
                Debug.Log("Found InventoryManager in scene");
            }
            else
            {
                // Try to find InventoryUI first, which may have a reference to the manager
                InventoryUI inventoryUI = FindObjectOfType<InventoryUI>();
                if (inventoryUI != null && inventoryUI.inventoryManager != null)
                {
                    inventoryManager = inventoryUI.inventoryManager;
                    Debug.Log("Found InventoryManager through InventoryUI reference");
                }
                else
                {
                    Debug.LogWarning("InventoryManager not found anywhere in the scene!");
                }
            }
        }
        else
        {
            Debug.Log("InventoryManager found on player");
        }
    }

    void Update()
    {
        try
        {
            // Toggle inventory with TAB
            if (Input.GetKeyDown(KeyCode.Tab))
            {
                ToggleInventory();
            }

            // If the inventory is open, check for clicks outside
            if (isInventoryOpen && Input.GetMouseButtonDown(0))
            {
                // Check if EventSystem is available
                if (EventSystem.current != null && !EventSystem.current.IsPointerOverGameObject())
                {
                    // Deselect the current item when clicking outside any UI
                    DeselectCurrentItem();
                }
            }

            // If the inventory is open, freeze player movement
            if (isInventoryOpen)
            {
                movement = Vector2.zero;
                if (animator != null)
                {
                    animator.SetBool("isMoving", false); // Ensure the player is in the idle state
                }
                return; // Skip the rest of the update logic
            }

            // Normal movement and camera controls
            inputMovement = new Vector2(Input.GetAxisRaw("Horizontal"), Input.GetAxisRaw("Vertical")).normalized;

            if (cam != null)
            {
                mousePos = cam.ScreenToWorldPoint(Input.mousePosition);
            }

            // Check if sprinting is allowed (only when moving forward within a slight angle tolerance)
            isSprinting = Input.GetKey(KeyCode.LeftShift) && IsMovingForward(inputMovement, 0.5f);

            // Pass sprinting state to WeaponShootingSystem
            WeaponShootingSystem shootingSystem = FindObjectOfType<WeaponShootingSystem>();
            if (shootingSystem != null)
            {
                shootingSystem.SetSprintingState(isSprinting);
            }

            // Prevent aiming while sprinting
            isAiming = !isSprinting && Input.GetMouseButton(1);

            // --- Rifle Animation Logic ---
            bool hasRifleSelected = false;
            Hotbar hotbar = FindObjectOfType<Hotbar>();
            if (hotbar != null)
            {
                HotbarSlot selectedSlot = hotbar.GetSelectedSlot();
                if (selectedSlot != null && selectedSlot.storedItem != null &&
                    selectedSlot.storedItem.itemData != null &&
                    selectedSlot.storedItem.itemData.itemType == ItemType.Weapon &&
                    selectedSlot.storedItem.itemData.weaponType == WeaponType.Rifle)
                {
                    hasRifleSelected = true;
                }
            }

            // Smooth speed transition
            float targetSpeed = isSprinting ? sprintSpeed : moveSpeed;
            float speedDiff = targetSpeed - movement.magnitude;
            float speedChange = speedDiff > 0 ? acceleration : deceleration;

            movement = Vector2.MoveTowards(movement, inputMovement * targetSpeed, speedChange * Time.deltaTime);

            // Update Animator parameters
            if (animator != null)
            {
                animator.SetBool("isMoving", inputMovement != Vector2.zero);
                animator.SetBool("isSprinting", isSprinting);
                animator.SetBool("hasRifleSelected", hasRifleSelected);
                animator.SetBool("isAiming", isAiming && hasRifleSelected);
            }

            // Update pickup highlighting to show which item will be picked up
            UpdatePickupHighlighting();

            // Update container highlighting to show which container can be interacted with
            UpdateContainerHighlighting();

            // Check for E key input (pickup items or interact with containers)
            if (Input.GetKeyDown(KeyCode.E))
            {
                // First check for container interaction
                if (TryInteractWithContainer())
                {
                    // Container interaction handled
                }
                else
                {
                    // No container nearby, try item pickup
                    PickupItem();
                }
            }

            // Check for reload input (R key)
            if (Input.GetKeyDown(KeyCode.R))
            {
                TryReloadCurrentWeapon();
            }

            // Debug key to force cleanup of stuck drag visuals (F9)
            if (Input.GetKeyDown(KeyCode.F9))
            {
                ForceCleanupDragVisuals();
            }

            // Check for weapon firing (left click)
            // if (Input.GetMouseButtonDown(0) && !EventSystem.current.IsPointerOverGameObject())
            // {
            //     TryFireCurrentWeapon();
            // }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in PlayerController.Update: {e.Message}\n{e.StackTrace}");
        }
    }

    void FixedUpdate()
    {
        try
        {
            if (isInventoryOpen) return; // Prevent movement when inventory is open

            // Handle player movement
            if (rb != null)
            {
                rb.MovePosition(rb.position + movement * Time.fixedDeltaTime);

                // Handle player rotation (looking towards mouse)
                Vector2 lookDir = mousePos - rb.position;
                float angle = Mathf.Atan2(lookDir.y, lookDir.x) * Mathf.Rad2Deg - 90f;
                rb.rotation = angle + 180f;
            }
            else
            {
                Debug.LogWarning("Rigidbody2D is null in FixedUpdate");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in FixedUpdate: {e.Message}\n{e.StackTrace}");
        }
    }

    void ToggleInventory()
    {
        try
        {
            isInventoryOpen = !isInventoryOpen;
            Debug.Log($"ToggleInventory called - new state: {(isInventoryOpen ? "OPEN" : "CLOSED")}");

            // Check if we have a dual inventory system
            DualInventoryUI dualUI = null;
            if (dualInventoryUI != null)
            {
                dualUI = dualInventoryUI.GetComponent<DualInventoryUI>();
                Debug.Log($"DualInventoryUI component found: {(dualUI != null ? "YES" : "NO")}");
            }
            else
            {
                Debug.Log("dualInventoryUI GameObject is null");
            }

            if (dualUI != null)
            {
                // Use the dual inventory system for player-only inventory
                if (isInventoryOpen)
                {
                    Debug.Log("Calling OpenPlayerInventoryOnly()");
                    dualUI.OpenPlayerInventoryOnly();
                }
                else
                {
                    Debug.Log("Calling CloseDualInventory()");
                    dualUI.CloseDualInventory();
                }
            }
            else
            {
                Debug.Log("Falling back to old inventory system");
                // Fallback to old system
                if (inventoryUI != null)
                {
                    inventoryUI.SetActive(isInventoryOpen);
                    Debug.Log($"Set old inventory UI active: {isInventoryOpen}");
                }
                else
                {
                    Debug.LogWarning("Inventory UI reference is missing. Cannot toggle inventory display.");
                }
            }

            // If we're closing the inventory, deselect any selected item
            if (!isInventoryOpen)
            {
                DeselectCurrentItem();
            }

            // Always keep the cursor visible and unlocked
            Cursor.visible = true;
            Cursor.lockState = CursorLockMode.None;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in ToggleInventory: {e.Message}");

            // Ensure cursor is always unlocked even if an error occurs
            Cursor.visible = true;
            Cursor.lockState = CursorLockMode.None;
        }
    }

    private void DeselectCurrentItem()
    {
        try
        {
            // Check if the inventory system is properly initialized
            if (inventoryManager == null)
            {
                Debug.LogWarning("Cannot deselect item - inventory manager is null");
                return;
            }

            // Safely get the selected item
            var selectedItem = InventoryItemUIDrag.GetSelectedItem();
            if (selectedItem != null)
            {
                selectedItem.Deselect();
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"Error in DeselectCurrentItem: {e.Message}");
        }
    }

    // Track the currently highlighted item for pickup
    private InventoryItem currentHighlightedItem = null;

    // Track the currently highlighted container for interaction
    private ContainerInteractable currentHighlightedContainer = null;

    void PickupItem()
    {
        try
        {
            if (inventoryManager == null)
            {
                Debug.LogWarning("No InventoryManager available - trying to find one");
                FindInventoryManager();

                if (inventoryManager == null)
                {
                    Debug.LogError("Could not find InventoryManager - cannot pick up items");
                    return;
                }
            }

            // Find the closest pickupable item
            InventoryItem closestItem = FindClosestPickupableItem();

            if (closestItem == null)
            {
                Debug.Log("No items in pickup range");
                return;
            }

            Debug.Log($"Attempting to pick up {closestItem.GetName()}");

            // Try to add the item to inventory
            if (inventoryManager.PickupItem(closestItem))
            {
                Debug.Log($"Successfully picked up {closestItem.GetName()}");

                // Hide the pickup indicator before deactivating
                closestItem.HidePickupIndicator();
                currentHighlightedItem = null;

                closestItem.gameObject.SetActive(false);
            }
            else
            {
                Debug.Log($"Failed to pick up {closestItem.GetName()}, no space available");
            }
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error in PickupItem: {e.Message}\n{e.StackTrace}");
        }
    }

    // Find the closest item that can be picked up
    private InventoryItem FindClosestPickupableItem()
    {
        Collider2D[] hitColliders = Physics2D.OverlapCircleAll(transform.position, 1.5f);
        if (hitColliders == null || hitColliders.Length == 0)
            return null;

        InventoryItem closestItem = null;
        float closestDistance = float.MaxValue;

        foreach (var hitCollider in hitColliders)
        {
            if (hitCollider == null)
                continue;

            InventoryItem item = hitCollider.GetComponent<InventoryItem>();
            if (item == null || item.itemData == null)
                continue;

            float distance = Vector2.Distance(transform.position, hitCollider.transform.position);
            if (distance < closestDistance)
            {
                closestDistance = distance;
                closestItem = item;
            }
        }

        return closestItem;
    }

    // Update pickup highlighting - call this regularly to show which item will be picked up
    private void UpdatePickupHighlighting()
    {
        InventoryItem closestItem = FindClosestPickupableItem();

        // If the highlighted item changed
        if (currentHighlightedItem != closestItem)
        {
            // Hide previous highlight
            if (currentHighlightedItem != null)
            {
                currentHighlightedItem.HidePickupIndicator();
            }

            // Show new highlight
            currentHighlightedItem = closestItem;
            if (currentHighlightedItem != null)
            {
                currentHighlightedItem.ShowPickupIndicator();
            }
        }
    }

    // Try to interact with a nearby container
    private bool TryInteractWithContainer()
    {
        ContainerInteractable closestContainer = FindClosestContainer();

        if (closestContainer != null)
        {
            Debug.Log($"Interacting with container: {closestContainer.name}");
            closestContainer.OpenContainer();
            return true;
        }

        return false;
    }

    // Find the closest interactable container within range
    private ContainerInteractable FindClosestContainer()
    {
        ContainerInteractable[] containers = FindObjectsOfType<ContainerInteractable>();
        ContainerInteractable closestContainer = null;
        float closestDistance = float.MaxValue;

        foreach (ContainerInteractable container in containers)
        {
            if (container == null || !container.gameObject.activeInHierarchy) continue;

            float distance = Vector2.Distance(transform.position, container.transform.position);

            if (distance <= container.interactionRange && distance < closestDistance)
            {
                closestDistance = distance;
                closestContainer = container;
            }
        }

        return closestContainer;
    }

    // Update container highlighting - call this regularly to show which container can be interacted with
    private void UpdateContainerHighlighting()
    {
        ContainerInteractable closestContainer = FindClosestContainer();

        // If the highlighted container changed
        if (currentHighlightedContainer != closestContainer)
        {
            // Hide previous highlight
            if (currentHighlightedContainer != null)
            {
                currentHighlightedContainer.HideInteractionFeedback();
            }

            // Show new highlight
            currentHighlightedContainer = closestContainer;
            if (currentHighlightedContainer != null)
            {
                currentHighlightedContainer.ShowInteractionFeedback();
            }
        }
    }

    public bool IsSprinting()
    {
        return isSprinting;
    }

    public bool IsMoving()
    {
        return inputMovement != Vector2.zero;
    }

    public bool IsAiming()
    {
        return isAiming;
    }

    private bool IsMovingForward(Vector2 movementInput, float tolerance)
    {
        if (rb == null) return false;

        Vector2 forwardDirection = (mousePos - rb.position).normalized;
        float dotProduct = Vector2.Dot(forwardDirection, movementInput);
        return dotProduct > tolerance;
    }

    public bool IsInventoryOpen()
    {
        if (inventoryUI == null)
        {
            Debug.LogError("Inventory system is not initialized.");
            return false;
        }
        return isInventoryOpen;
    }

    public Vector2 GetMovementInput()
    {
        return inputMovement;
    }

    /// <summary>
    /// Set the inventory open state (used by container system)
    /// </summary>
    public void SetInventoryOpen(bool open)
    {
        isInventoryOpen = open;

        if (open)
        {
            // Show cursor when inventory is open
            Cursor.visible = true;
            Cursor.lockState = CursorLockMode.None;
        }
    }

    /// <summary>
    /// Try to reload the currently selected weapon in hotbar
    /// </summary>
    private void TryReloadCurrentWeapon()
    {
        // Find the hotbar and get the currently selected weapon
        Hotbar hotbar = FindObjectOfType<Hotbar>();
        if (hotbar == null) return;

        HotbarSlot selectedSlot = hotbar.GetSelectedSlot();
        if (selectedSlot == null || selectedSlot.storedItem == null) return;

        // Check if it's a weapon
        if (selectedSlot.storedItem.itemData.itemType != ItemType.Weapon) return;

        // Find weapon system and try to reload
        WeaponSystem weaponSystem = FindObjectOfType<WeaponSystem>();
        if (weaponSystem != null)
        {
            weaponSystem.TryReloadWeapon(selectedSlot.storedItem);
        }
        else
        {
            Debug.LogError("WeaponSystem not found! Cannot reload weapon.");
        }
    }

    /// <summary>
    /// Try to fire the currently selected weapon in hotbar
    /// </summary>
    private void TryFireCurrentWeapon()
    {
        // Don't fire if not aiming
        if (!isAiming) return;

        // Find the hotbar and get the currently selected weapon
        Hotbar hotbar = FindObjectOfType<Hotbar>();
        if (hotbar == null) return;

        HotbarSlot selectedSlot = hotbar.GetSelectedSlot();
        if (selectedSlot == null || selectedSlot.storedItem == null) return;

        // Check if it's a weapon
        if (selectedSlot.storedItem.itemData.itemType != ItemType.Weapon) return;

        // Find weapon system and try to fire
        WeaponSystem weaponSystem = FindObjectOfType<WeaponSystem>();
        if (weaponSystem != null)
        {
            weaponSystem.FireWeapon(selectedSlot.storedItem);
        }
        else
        {
            Debug.LogError("WeaponSystem not found! Cannot fire weapon.");
        }
    }

    /// <summary>
    /// Force cleanup of any stuck drag visuals (debug function)
    /// </summary>
    private void ForceCleanupDragVisuals()
    {
        Debug.Log("Force cleaning up drag visuals (F9 pressed)");

        // Find and cleanup inventory UI
        InventoryUI inventoryUI = FindObjectOfType<InventoryUI>();
        if (inventoryUI != null)
        {
            inventoryUI.ForceCleanupDragVisuals();
        }

        // Also clear any static drag states
        InventoryItemUIDrag.ClearCurrentDraggedItem();
    }

    // Clean up highlighting when the player is destroyed or disabled
    private void OnDisable()
    {
        if (currentHighlightedItem != null)
        {
            currentHighlightedItem.HidePickupIndicator();
            currentHighlightedItem = null;
        }
    }
}
