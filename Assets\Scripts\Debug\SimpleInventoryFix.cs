using UnityEngine;

/// <summary>
/// Simple script to manually fix inventory references
/// </summary>
public class SimpleInventoryFix : MonoBehaviour
{
    [Head<PERSON>("Manual Assignment")]
    [Tooltip("Drag your existing InventoryPanel here")]
    public GameObject existingInventoryPanel;
    
    [<PERSON>lt<PERSON>("Drag your DualInventoryPanel here")]
    public GameObject dualInventoryPanel;
    
    [ContextMenu("Fix References Now")]
    public void FixReferences()
    {
        Debug.Log("=== SIMPLE INVENTORY FIX ===");
        
        // Find components
        PlayerController playerController = FindObjectOfType<PlayerController>();
        DualInventoryUI dualUI = FindObjectOfType<DualInventoryUI>();
        InventoryUI inventoryUI = FindObjectOfType<InventoryUI>();
        
        if (playerController == null)
        {
            Debug.LogError("PlayerController not found!");
            return;
        }
        
        if (dualUI == null)
        {
            Debug.LogError("DualInventoryUI not found!");
            return;
        }
        
        if (inventoryUI == null)
        {
            Debug.LogError("InventoryUI not found!");
            return;
        }
        
        // Use assigned references or find them
        GameObject playerInventory = existingInventoryPanel != null ? existingInventoryPanel : inventoryUI.gameObject;
        GameObject dualPanel = dualInventoryPanel != null ? dualInventoryPanel : dualUI.gameObject;
        
        Debug.Log($"Player Inventory: {playerInventory.name}");
        Debug.Log($"Dual Panel: {dualPanel.name}");
        
        // Fix PlayerController references
        playerController.inventoryUI = playerInventory;
        playerController.dualInventoryUI = dualPanel;
        
        // Fix DualInventoryUI references
        dualUI.dualInventoryPanel = dualPanel;
        dualUI.playerInventoryPanel = playerInventory;
        dualUI.playerInventoryUI = inventoryUI;
        
        // Create container panel if it doesn't exist
        Transform containerPanel = dualPanel.transform.Find("ContainerInventoryPanel");
        if (containerPanel == null)
        {
            GameObject containerPanelObj = new GameObject("ContainerInventoryPanel");
            containerPanelObj.transform.SetParent(dualPanel.transform, false);
            
            // Add RectTransform
            RectTransform containerRect = containerPanelObj.AddComponent<RectTransform>();
            containerRect.anchorMin = new Vector2(0.5f, 0);
            containerRect.anchorMax = new Vector2(1, 1);
            containerRect.offsetMin = new Vector2(5, 10);
            containerRect.offsetMax = new Vector2(-10, -10);
            
            containerPanel = containerPanelObj.transform;
            Debug.Log("Created ContainerInventoryPanel");
        }
        
        dualUI.containerInventoryPanel = containerPanel.gameObject;
        
        // Add ContainerInventoryUI if needed
        ContainerInventoryUI containerUI = containerPanel.GetComponent<ContainerInventoryUI>();
        if (containerUI == null)
        {
            containerUI = containerPanel.gameObject.AddComponent<ContainerInventoryUI>();
            
            // Copy settings from player inventory
            containerUI.slotPrefab = inventoryUI.slotPrefab;
            containerUI.itemUIPrefab = inventoryUI.itemUIPrefab;
            containerUI.slotSize = inventoryUI.slotSize;
            
            Debug.Log("Added ContainerInventoryUI");
        }
        
        dualUI.containerInventoryUI = containerUI;
        
        // Hide dual inventory initially
        dualPanel.SetActive(false);
        
        Debug.Log("✓ References fixed!");
        Debug.Log($"✓ PlayerController.inventoryUI = {playerController.inventoryUI.name}");
        Debug.Log($"✓ PlayerController.dualInventoryUI = {playerController.dualInventoryUI.name}");
        Debug.Log($"✓ DualInventoryUI.playerInventoryPanel = {dualUI.playerInventoryPanel.name}");
        Debug.Log($"✓ DualInventoryUI.containerInventoryPanel = {dualUI.containerInventoryPanel.name}");
    }
    
    [ContextMenu("Debug Current State")]
    public void DebugCurrentState()
    {
        Debug.Log("=== CURRENT STATE DEBUG ===");
        
        // Find all relevant objects
        PlayerController playerController = FindObjectOfType<PlayerController>();
        DualInventoryUI dualUI = FindObjectOfType<DualInventoryUI>();
        InventoryUI inventoryUI = FindObjectOfType<InventoryUI>();
        
        Debug.Log($"PlayerController found: {playerController != null}");
        if (playerController != null)
        {
            Debug.Log($"  - inventoryUI: {(playerController.inventoryUI != null ? playerController.inventoryUI.name : "NULL")}");
            Debug.Log($"  - dualInventoryUI: {(playerController.dualInventoryUI != null ? playerController.dualInventoryUI.name : "NULL")}");
        }
        
        Debug.Log($"DualInventoryUI found: {dualUI != null}");
        if (dualUI != null)
        {
            Debug.Log($"  - dualInventoryPanel: {(dualUI.dualInventoryPanel != null ? dualUI.dualInventoryPanel.name : "NULL")}");
            Debug.Log($"  - playerInventoryPanel: {(dualUI.playerInventoryPanel != null ? dualUI.playerInventoryPanel.name : "NULL")}");
            Debug.Log($"  - containerInventoryPanel: {(dualUI.containerInventoryPanel != null ? dualUI.containerInventoryPanel.name : "NULL")}");
            Debug.Log($"  - playerInventoryUI: {(dualUI.playerInventoryUI != null ? dualUI.playerInventoryUI.name : "NULL")}");
            Debug.Log($"  - containerInventoryUI: {(dualUI.containerInventoryUI != null ? dualUI.containerInventoryUI.name : "NULL")}");
        }
        
        Debug.Log($"InventoryUI found: {inventoryUI != null}");
        if (inventoryUI != null)
        {
            Debug.Log($"  - GameObject: {inventoryUI.gameObject.name}");
            Debug.Log($"  - Active: {inventoryUI.gameObject.activeInHierarchy}");
        }
        
        // Check scene hierarchy
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        Debug.Log("=== SCENE GAMEOBJECTS ===");
        foreach (GameObject obj in allObjects)
        {
            if (obj.name.Contains("Inventory") || obj.name.Contains("Dual") || obj.name.Contains("Container"))
            {
                Debug.Log($"  - {obj.name} (parent: {(obj.transform.parent != null ? obj.transform.parent.name : "ROOT")})");
            }
        }
    }
}
