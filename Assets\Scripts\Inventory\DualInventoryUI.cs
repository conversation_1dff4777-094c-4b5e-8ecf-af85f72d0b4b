using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Manages the dual inventory UI showing player inventory on left and container inventory on right
/// </summary>
public class DualInventoryUI : MonoBehaviour
{
    [Header("UI Panels")]
    [Tooltip("The main panel containing both inventories")]
    public GameObject dualInventoryPanel;
    
    [<PERSON>lt<PERSON>("Panel containing the player inventory")]
    public GameObject playerInventoryPanel;
    
    [<PERSON>lt<PERSON>("Panel containing the container inventory")]
    public GameObject containerInventoryPanel;
    
    [<PERSON><PERSON>("UI References")]
    [Tooltip("Player inventory UI component")]
    public InventoryUI playerInventoryUI;
    
    [Tooltip("Container inventory UI component")]
    public ContainerInventoryUI containerInventoryUI;
    
    [Head<PERSON>("UI Elements")]
    [Tooltip("Text showing the container name")]
    public Text containerTitleText;
    
    [Tooltip("Button to close the dual inventory")]
    public Button closeButton;
    
    // Current container being displayed
    private ContainerInventory currentContainer;
    
    // Player controller reference for freezing movement
    private PlayerController playerController;
    
    private void Awake()
    {
        // Find player controller
        playerController = FindObjectOfType<PlayerController>();

        // Auto-assign references if they're missing
        AutoAssignReferences();

        // Set up close button
        if (closeButton != null)
        {
            closeButton.onClick.AddListener(CloseDualInventory);
        }

        // Start with dual inventory hidden
        if (dualInventoryPanel != null)
        {
            dualInventoryPanel.SetActive(false);
        }
        else
        {
            // If dualInventoryPanel is not assigned, use this GameObject
            dualInventoryPanel = gameObject;
            dualInventoryPanel.SetActive(false);
        }
    }

    /// <summary>
    /// Public method to manually setup references (useful when component is added dynamically)
    /// </summary>
    public void SetupReferences()
    {
        Debug.Log("=== FIXING INVENTORY SETUP ===");
        AutoAssignReferences();
        Debug.Log("=== TOP-LEFT SLOT INITIALIZED ===");
    }

    /// <summary>
    /// Debug method to check all references
    /// </summary>
    public void DebugReferences()
    {
        Debug.Log("=== DualInventoryUI References ===");
        Debug.Log($"dualInventoryPanel: {(dualInventoryPanel != null ? dualInventoryPanel.name : "NULL")}");
        Debug.Log($"playerInventoryPanel: {(playerInventoryPanel != null ? playerInventoryPanel.name : "NULL")}");
        Debug.Log($"containerInventoryPanel: {(containerInventoryPanel != null ? containerInventoryPanel.name : "NULL")}");
        Debug.Log($"playerInventoryUI: {(playerInventoryUI != null ? playerInventoryUI.name : "NULL")}");
        Debug.Log($"containerInventoryUI: {(containerInventoryUI != null ? containerInventoryUI.name : "NULL")}");
        Debug.Log($"containerTitleText: {(containerTitleText != null ? containerTitleText.name : "NULL")}");
        Debug.Log($"closeButton: {(closeButton != null ? closeButton.name : "NULL")}");
        Debug.Log("=== End References ===");
    }

    private void AutoAssignReferences()
    {
        Debug.Log("AutoAssignReferences called");

        // Auto-assign dual inventory panel if not set
        if (dualInventoryPanel == null)
        {
            dualInventoryPanel = gameObject;
            Debug.Log($"Set dualInventoryPanel to: {dualInventoryPanel.name}");
        }

        // Auto-find player inventory panel
        if (playerInventoryPanel == null)
        {
            Transform playerPanel = transform.Find("InventoryPanel");
            if (playerPanel == null)
            {
                playerPanel = transform.Find("PlayerInventoryPanel");
            }
            if (playerPanel != null)
            {
                playerInventoryPanel = playerPanel.gameObject;
                Debug.Log($"Found player inventory panel: {playerInventoryPanel.name}");
            }
            else
            {
                Debug.LogWarning("Player inventory panel not found. Looking for existing InventoryUI in scene...");
                // Try to find any existing InventoryUI in the scene
                InventoryUI existingInventoryUI = FindObjectOfType<InventoryUI>();
                if (existingInventoryUI != null)
                {
                    playerInventoryPanel = existingInventoryUI.gameObject;
                    Debug.Log($"Found existing InventoryUI: {existingInventoryUI.name}");
                }
                else
                {
                    Debug.LogError("No player inventory panel found anywhere!");
                }
            }
        }

        // Auto-find container inventory panel
        if (containerInventoryPanel == null)
        {
            Transform containerPanel = transform.Find("ContainerInventoryPanel");
            if (containerPanel != null)
            {
                containerInventoryPanel = containerPanel.gameObject;
            }
            else
            {
                Debug.LogWarning("Container inventory panel not found. Creating one...");
                CreateContainerInventoryPanel();
            }
        }

        // Auto-find player inventory UI
        if (playerInventoryUI == null && playerInventoryPanel != null)
        {
            playerInventoryUI = playerInventoryPanel.GetComponent<InventoryUI>();
            if (playerInventoryUI == null)
            {
                Debug.LogError("InventoryUI component not found on player inventory panel!");
            }
        }

        // Auto-find or add container inventory UI
        if (containerInventoryUI == null && containerInventoryPanel != null)
        {
            containerInventoryUI = containerInventoryPanel.GetComponent<ContainerInventoryUI>();
            if (containerInventoryUI == null)
            {
                containerInventoryUI = containerInventoryPanel.AddComponent<ContainerInventoryUI>();
                Debug.Log("Added ContainerInventoryUI component to container panel");
            }
        }
    }

    private void CreateContainerInventoryPanel()
    {
        // Create container inventory panel as a child of this GameObject
        GameObject containerPanel = new GameObject("ContainerInventoryPanel");
        containerPanel.transform.SetParent(transform, false);

        // Add RectTransform and configure it
        RectTransform containerRect = containerPanel.AddComponent<RectTransform>();
        containerRect.anchorMin = new Vector2(0.5f, 0);
        containerRect.anchorMax = new Vector2(1, 1);
        containerRect.offsetMin = new Vector2(10, 10);
        containerRect.offsetMax = new Vector2(-10, -10);

        // Add background image
        Image backgroundImage = containerPanel.AddComponent<Image>();
        backgroundImage.color = new Color(0.2f, 0.2f, 0.2f, 0.8f);

        containerInventoryPanel = containerPanel;
        Debug.Log("Created ContainerInventoryPanel");
    }
    
    /// <summary>
    /// Open the dual inventory with player inventory on left and container on right
    /// </summary>
    public void OpenDualInventory(ContainerInventory container)
    {
        Debug.Log("=== OpenDualInventory called ===");
        DebugReferences();

        if (container == null)
        {
            Debug.LogError("Cannot open dual inventory with null container!");
            return;
        }

        currentContainer = container;
        
        // Show the dual inventory panel
        if (dualInventoryPanel != null)
        {
            dualInventoryPanel.SetActive(true);
        }
        
        // Initialize container UI
        if (containerInventoryUI != null)
        {
            containerInventoryUI.Initialize(container);
        }
        
        // Update container title
        if (containerTitleText != null)
        {
            containerTitleText.text = container.GetContainerName();
        }
        
        // Show player inventory panel
        if (playerInventoryPanel != null)
        {
            playerInventoryPanel.SetActive(true);
        }
        
        // Show container inventory panel
        if (containerInventoryPanel != null)
        {
            containerInventoryPanel.SetActive(true);
        }
        
        // Refresh player inventory UI
        if (playerInventoryUI != null)
        {
            playerInventoryUI.RefreshInventoryUI();
        }
        
        // Freeze player movement
        FreezePlayer(true);
        
        // Show cursor
        Cursor.visible = true;
        Cursor.lockState = CursorLockMode.None;
        
        Debug.Log($"Opened dual inventory with container: {container.GetContainerName()}");
    }

    /// <summary>
    /// Open only the player inventory (for Tab key)
    /// </summary>
    public void OpenPlayerInventoryOnly()
    {
        Debug.Log("=== OpenPlayerInventoryOnly() called ===");

        // Show the dual inventory panel
        if (dualInventoryPanel != null)
        {
            dualInventoryPanel.SetActive(true);
            Debug.Log($"Set dualInventoryPanel active: {dualInventoryPanel.name}");
        }
        else
        {
            Debug.LogError("dualInventoryPanel is null!");
        }

        // Show only player inventory
        if (playerInventoryPanel != null)
        {
            playerInventoryPanel.SetActive(true);
            Debug.Log($"Set playerInventoryPanel active: {playerInventoryPanel.name}");
        }
        else
        {
            Debug.LogError("playerInventoryPanel is null!");
        }

        // Hide container inventory
        if (containerInventoryPanel != null)
        {
            containerInventoryPanel.SetActive(false);
            Debug.Log($"Set containerInventoryPanel inactive: {containerInventoryPanel.name}");
        }

        // Hide close button when only showing player inventory
        if (closeButton != null)
        {
            closeButton.gameObject.SetActive(false);
        }

        // Hide container title
        if (containerTitleText != null)
        {
            containerTitleText.gameObject.SetActive(false);
        }

        // Freeze player movement
        FreezePlayer(true);

        // Set cursor state
        Cursor.visible = true;
        Cursor.lockState = CursorLockMode.None;

        Debug.Log("=== OpenPlayerInventoryOnly() completed ===");
    }

    /// <summary>
    /// Close the dual inventory
    /// </summary>
    public void CloseDualInventory()
    {
        Debug.Log("=== CloseDualInventory() called ===");

        // Hide the dual inventory panel
        if (dualInventoryPanel != null)
        {
            dualInventoryPanel.SetActive(false);
            Debug.Log($"Set dualInventoryPanel inactive: {dualInventoryPanel.name}");
        }

        // Clean up container UI
        if (containerInventoryUI != null)
        {
            containerInventoryUI.Cleanup();
        }

        // Hide individual panels
        if (playerInventoryPanel != null)
        {
            playerInventoryPanel.SetActive(false);
            Debug.Log($"Set playerInventoryPanel inactive: {playerInventoryPanel.name}");
        }

        if (containerInventoryPanel != null)
        {
            containerInventoryPanel.SetActive(false);
            Debug.Log($"Set containerInventoryPanel inactive: {containerInventoryPanel.name}");
        }

        // Unfreeze player movement
        FreezePlayer(false);

        // Clear current container reference
        currentContainer = null;

        Debug.Log("=== CloseDualInventory() completed ===");
    }
    
    /// <summary>
    /// Freeze or unfreeze player movement
    /// </summary>
    private void FreezePlayer(bool freeze)
    {
        if (playerController != null)
        {
            // Use the public method to control inventory state
            playerController.SetInventoryOpen(freeze);

            if (freeze)
            {
                Debug.Log("Player movement frozen for container interaction");
            }
            else
            {
                Debug.Log("Player movement unfrozen");
            }
        }
    }
    
    /// <summary>
    /// Check if dual inventory is currently open
    /// </summary>
    public bool IsOpen()
    {
        return dualInventoryPanel != null && dualInventoryPanel.activeInHierarchy;
    }
    
    /// <summary>
    /// Get the current container being displayed
    /// </summary>
    public ContainerInventory GetCurrentContainer()
    {
        return currentContainer;
    }
    
    /// <summary>
    /// Handle ESC key to close inventory
    /// </summary>
    private void Update()
    {
        if (IsOpen() && Input.GetKeyDown(KeyCode.Escape))
        {
            CloseDualInventory();
        }
    }
    
    /// <summary>
    /// Transfer an item from player inventory to container
    /// </summary>
    public bool TransferToContainer(InventoryItem item)
    {
        if (currentContainer == null || item == null) return false;

        // Remove from player inventory
        if (playerInventoryUI != null && playerInventoryUI.inventoryManager != null)
        {
            // InventoryManager.RemoveItem returns void, so we just call it
            playerInventoryUI.inventoryManager.RemoveItem(item);

            // Try to add to container
            if (currentContainer.TryAddItemToContainer(item))
            {
                Debug.Log($"Transferred {item.GetName()} to container");
                return true;
            }
            else
            {
                // Failed to add to container, put back in player inventory
                playerInventoryUI.inventoryManager.PickupItem(item);
                Debug.Log($"Container full, could not transfer {item.GetName()}");
                return false;
            }
        }

        return false;
    }
    
    /// <summary>
    /// Transfer an item from container to player inventory
    /// </summary>
    public bool TransferToPlayer(InventoryItem item)
    {
        if (currentContainer == null || item == null) return false;
        
        // Remove from container
        if (currentContainer.RemoveItem(item))
        {
            // Add to player inventory
            if (playerInventoryUI != null && playerInventoryUI.inventoryManager != null)
            {
                if (playerInventoryUI.inventoryManager.PickupItem(item))
                {
                    Debug.Log($"Transferred {item.GetName()} to player inventory");
                    return true;
                }
                else
                {
                    // Failed to add to player inventory, put back in container
                    currentContainer.TryAddItemToContainer(item);
                    Debug.Log($"Player inventory full, could not transfer {item.GetName()}");
                    return false;
                }
            }
        }
        
        return false;
    }
}
