Using pre-set license
Built from '2022.3/staging' branch; Version is '2022.3.33f1 (b2c853adf198) revision 11716691'; Using compiler version '192829333'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Core' Language: 'en' Physical Memory: 16087 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\2022.3.33f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker2
-projectPath
C:/Users/<USER>/Hydra Games/Void Voyager
-logFile
Logs/AssetImportWorker2.log
-srvPort
51979
Successfully changed project path to: C:/Users/<USER>/Hydra Games/Void Voyager
C:/Users/<USER>/Hydra Games/Void Voyager
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [30700] Host "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2182805373 [EditorId] 2182805373 [Version] 1048832 [Id] WindowsEditor(7,MSI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined multi-casting on [***********:54997]...

Player connection [30700] Host "[IP] ************* [Port] 0 [Flags] 2 [Guid] 2182805373 [EditorId] 2182805373 [Version] 1048832 [Id] WindowsEditor(7,MSI) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" joined alternative multi-casting on [***********:34997]...

[PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
Refreshing native plugins compatible for Editor in 479.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 2022.3.33f1 (b2c853adf198)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Users/<USER>/Hydra Games/Void Voyager/Assets
GfxDevice: creating device client; threaded=0; jobified=0
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce RTX 4070 Laptop GPU (ID=0x2820)
    Vendor:   NVIDIA
    VRAM:     7948 MB
    Driver:   32.0.15.7652
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56004
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/2022.3.33f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.007934 seconds.
- Loaded All Assemblies, in  0.334 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.190 seconds
Domain Reload Profiling: 523ms
	BeginReloadAssembly (81ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (24ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (177ms)
		LoadAssemblies (79ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (176ms)
			TypeCache.Refresh (174ms)
				TypeCache.ScanAssembly (165ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (1ms)
	FinalizeReload (191ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (149ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (2ms)
			ProcessInitializeOnLoadAttributes (102ms)
			ProcessInitializeOnLoadMethodAttributes (34ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  5.225 seconds
Refreshing native plugins compatible for Editor in 4.64 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Package Manager log level set to [2]
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.560 seconds
Domain Reload Profiling: 5783ms
	BeginReloadAssembly (104ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (3ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (14ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (5061ms)
		LoadAssemblies (4959ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (163ms)
			TypeCache.Refresh (133ms)
				TypeCache.ScanAssembly (121ms)
			ScanForSourceGeneratedMonoScriptInfo (22ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (560ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (438ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (61ms)
			ProcessInitializeOnLoadAttributes (305ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.06 seconds
Refreshing native plugins compatible for Editor in 7.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5802 Unused Serialized files (Serialized files now loaded: 0)
Unloading 102 unused Assets / (0.9 MB). Loaded Objects now: 6247.
Memory consumption went from 225.4 MB to 224.5 MB.
Total: 3.724300 ms (FindLiveObjects: 0.318500 ms CreateObjectMapping: 0.151600 ms MarkObjects: 2.857200 ms  DeleteObjects: 0.396000 ms)

AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Import Request.
  Time since last request: 552973.595590 seconds.
  path: Assets/Prefabs/Container.prefab
  artifactKey: Guid(3aa44b49ff2b7b147b90dc1d67ae4694) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Prefabs/Container.prefab using Guid(3aa44b49ff2b7b147b90dc1d67ae4694) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
[PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '8fda37d228a3dbfab7d903fb07cb6427') in 0.204103 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 10
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.456 seconds
Refreshing native plugins compatible for Editor in 3.91 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.900 seconds
Domain Reload Profiling: 1348ms
	BeginReloadAssembly (167ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (32ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (207ms)
		LoadAssemblies (267ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (900ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (337ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (223ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 8.02 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5649 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6300.
Memory consumption went from 203.7 MB to 202.8 MB.
Total: 5.190000 ms (FindLiveObjects: 0.463600 ms CreateObjectMapping: 0.345800 ms MarkObjects: 3.738200 ms  DeleteObjects: 0.641000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.889 seconds
Refreshing native plugins compatible for Editor in 4.39 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 30.705 seconds
Domain Reload Profiling: 31592ms
	BeginReloadAssembly (130ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (38ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (707ms)
		LoadAssemblies (730ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (36ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (18ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (30705ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (419ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (69ms)
			ProcessInitializeOnLoadAttributes (279ms)
			ProcessInitializeOnLoadMethodAttributes (55ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 5.56 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5650 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6306.
Memory consumption went from 203.6 MB to 202.7 MB.
Total: 3.269600 ms (FindLiveObjects: 0.317500 ms CreateObjectMapping: 0.128800 ms MarkObjects: 2.501400 ms  DeleteObjects: 0.320800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.459 seconds
Refreshing native plugins compatible for Editor in 6.30 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.165 seconds
Domain Reload Profiling: 1621ms
	BeginReloadAssembly (140ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (35ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (253ms)
		LoadAssemblies (301ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (20ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1166ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (431ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (73ms)
			ProcessInitializeOnLoadAttributes (288ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 10.74 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5650 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6311.
Memory consumption went from 203.6 MB to 202.7 MB.
Total: 5.863200 ms (FindLiveObjects: 0.503600 ms CreateObjectMapping: 0.307000 ms MarkObjects: 4.330800 ms  DeleteObjects: 0.720300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.345 seconds
Refreshing native plugins compatible for Editor in 3.95 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.891 seconds
Domain Reload Profiling: 1234ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (183ms)
		LoadAssemblies (218ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (892ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (344ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (59ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 8.29 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5650 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6316.
Memory consumption went from 203.6 MB to 202.7 MB.
Total: 4.655500 ms (FindLiveObjects: 0.435500 ms CreateObjectMapping: 0.262700 ms MarkObjects: 3.269100 ms  DeleteObjects: 0.686900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.342 seconds
Refreshing native plugins compatible for Editor in 4.45 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.897 seconds
Domain Reload Profiling: 1236ms
	BeginReloadAssembly (109ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (183ms)
		LoadAssemblies (224ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (898ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (351ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (230ms)
			ProcessInitializeOnLoadMethodAttributes (49ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 8.60 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5650 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6321.
Memory consumption went from 203.5 MB to 202.7 MB.
Total: 4.795000 ms (FindLiveObjects: 0.466400 ms CreateObjectMapping: 0.211400 ms MarkObjects: 3.535900 ms  DeleteObjects: 0.579800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.336 seconds
Refreshing native plugins compatible for Editor in 3.56 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.902 seconds
Domain Reload Profiling: 1236ms
	BeginReloadAssembly (110ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (178ms)
		LoadAssemblies (219ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (902ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (343ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (60ms)
			ProcessInitializeOnLoadAttributes (224ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 8.67 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5650 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6326.
Memory consumption went from 203.6 MB to 202.7 MB.
Total: 4.719300 ms (FindLiveObjects: 0.469500 ms CreateObjectMapping: 0.425500 ms MarkObjects: 3.196600 ms  DeleteObjects: 0.625800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.351 seconds
Refreshing native plugins compatible for Editor in 4.05 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.918 seconds
Domain Reload Profiling: 1266ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (188ms)
		LoadAssemblies (228ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (919ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (354ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (228ms)
			ProcessInitializeOnLoadMethodAttributes (54ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 8.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5650 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6331.
Memory consumption went from 203.6 MB to 202.7 MB.
Total: 4.814500 ms (FindLiveObjects: 0.419700 ms CreateObjectMapping: 0.406100 ms MarkObjects: 3.404400 ms  DeleteObjects: 0.582700 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.348 seconds
Refreshing native plugins compatible for Editor in 3.82 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.887 seconds
Domain Reload Profiling: 1232ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (188ms)
		LoadAssemblies (227ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (888ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (338ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (225ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 6.70 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5650 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6336.
Memory consumption went from 203.6 MB to 202.7 MB.
Total: 4.754500 ms (FindLiveObjects: 0.588300 ms CreateObjectMapping: 0.253600 ms MarkObjects: 3.334200 ms  DeleteObjects: 0.576500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.342 seconds
Refreshing native plugins compatible for Editor in 3.72 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.893 seconds
Domain Reload Profiling: 1233ms
	BeginReloadAssembly (109ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (184ms)
		LoadAssemblies (222ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (893ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (341ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (225ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 8.05 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5650 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6341.
Memory consumption went from 203.6 MB to 202.7 MB.
Total: 4.825800 ms (FindLiveObjects: 0.502300 ms CreateObjectMapping: 0.428600 ms MarkObjects: 3.313300 ms  DeleteObjects: 0.580300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.378 seconds
Refreshing native plugins compatible for Editor in 4.38 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.902 seconds
Domain Reload Profiling: 1276ms
	BeginReloadAssembly (116ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (209ms)
		LoadAssemblies (250ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (902ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (335ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (222ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5650 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6346.
Memory consumption went from 203.6 MB to 202.7 MB.
Total: 2.909700 ms (FindLiveObjects: 0.259200 ms CreateObjectMapping: 0.230500 ms MarkObjects: 2.107900 ms  DeleteObjects: 0.311200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.345 seconds
Refreshing native plugins compatible for Editor in 3.91 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.900 seconds
Domain Reload Profiling: 1242ms
	BeginReloadAssembly (110ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (181ms)
		LoadAssemblies (219ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (901ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (337ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (221ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 6.38 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5650 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6351.
Memory consumption went from 203.6 MB to 202.8 MB.
Total: 4.322100 ms (FindLiveObjects: 0.395800 ms CreateObjectMapping: 0.227100 ms MarkObjects: 3.136100 ms  DeleteObjects: 0.561900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  5.801 seconds
Refreshing native plugins compatible for Editor in 4.04 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 35.500 seconds
Domain Reload Profiling: 41298ms
	BeginReloadAssembly (114ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (5636ms)
		LoadAssemblies (5660ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (14ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (35500ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (350ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (62ms)
			ProcessInitializeOnLoadAttributes (230ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 4.56 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5651 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6357.
Memory consumption went from 203.7 MB to 202.8 MB.
Total: 3.449200 ms (FindLiveObjects: 0.287100 ms CreateObjectMapping: 0.125600 ms MarkObjects: 2.720400 ms  DeleteObjects: 0.315200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.339 seconds
Refreshing native plugins compatible for Editor in 4.10 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.889 seconds
Domain Reload Profiling: 1225ms
	BeginReloadAssembly (109ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (182ms)
		LoadAssemblies (222ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (889ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (344ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (227ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 6.66 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5651 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6362.
Memory consumption went from 203.7 MB to 202.8 MB.
Total: 3.930900 ms (FindLiveObjects: 0.419000 ms CreateObjectMapping: 0.218000 ms MarkObjects: 2.793700 ms  DeleteObjects: 0.499500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Import Request.
  Time since last request: 3103.475005 seconds.
  path: Assets/Scripts/Inventory/ContainerInventoryManager.cs
  artifactKey: Guid(88a98c49d0350a9419e0e0d04ec97547) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/ContainerInventoryManager.cs using Guid(88a98c49d0350a9419e0e0d04ec97547) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: '74e9615d8581ee5784dcf50831c61d69') in 0.026045 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Import Request.
  Time since last request: 16.981419 seconds.
  path: Assets/Scripts/Inventory/ContainerInteractable.cs
  artifactKey: Guid(81c0f2634822d6848b929ffa8917f238) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/ContainerInteractable.cs using Guid(81c0f2634822d6848b929ffa8917f238) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'be76038bf18a0244e5eaa3b82e115da8') in 0.001796 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  4.846 seconds
Refreshing native plugins compatible for Editor in 3.79 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 48.169 seconds
Domain Reload Profiling: 53013ms
	BeginReloadAssembly (208ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (17ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (85ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (4582ms)
		LoadAssemblies (4611ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (42ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (19ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (48169ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (478ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (314ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 5.75 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5649 Unused Serialized files (Serialized files now loaded: 0)
Unloading 67 unused Assets / (0.9 MB). Loaded Objects now: 6365.
Memory consumption went from 203.6 MB to 202.8 MB.
Total: 5.310900 ms (FindLiveObjects: 0.356600 ms CreateObjectMapping: 0.129900 ms MarkObjects: 4.463100 ms  DeleteObjects: 0.360000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.435 seconds
Refreshing native plugins compatible for Editor in 4.92 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.933 seconds
Domain Reload Profiling: 1365ms
	BeginReloadAssembly (147ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (231ms)
		LoadAssemblies (292ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (933ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (390ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (271ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 6.84 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5649 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6370.
Memory consumption went from 203.6 MB to 202.7 MB.
Total: 4.079400 ms (FindLiveObjects: 0.356100 ms CreateObjectMapping: 0.213900 ms MarkObjects: 2.994300 ms  DeleteObjects: 0.513900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  7.522 seconds
Refreshing native plugins compatible for Editor in 3.80 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 50.836 seconds
Domain Reload Profiling: 58357ms
	BeginReloadAssembly (516ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (64ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (1ms)
		CreateAndSetChildDomain (259ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (6943ms)
		LoadAssemblies (7032ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (22ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (50837ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (379ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (63ms)
			ProcessInitializeOnLoadAttributes (251ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 4.27 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5649 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6375.
Memory consumption went from 203.7 MB to 202.8 MB.
Total: 5.168400 ms (FindLiveObjects: 0.488300 ms CreateObjectMapping: 0.147300 ms MarkObjects: 3.994300 ms  DeleteObjects: 0.537500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.402 seconds
Refreshing native plugins compatible for Editor in 3.91 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.362 seconds
Domain Reload Profiling: 1762ms
	BeginReloadAssembly (122ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (229ms)
		LoadAssemblies (272ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (1363ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (374ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (61ms)
			ProcessInitializeOnLoadAttributes (243ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 7.33 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5649 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6380.
Memory consumption went from 203.7 MB to 202.8 MB.
Total: 3.480300 ms (FindLiveObjects: 0.459600 ms CreateObjectMapping: 0.192400 ms MarkObjects: 2.459200 ms  DeleteObjects: 0.368000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Import Request.
  Time since last request: 169233.575215 seconds.
  path: Assets/Scripts/Inventory/DualInventoryUI.cs
  artifactKey: Guid(2e7dc18117a7a024bbabff9594c4b962) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/DualInventoryUI.cs using Guid(2e7dc18117a7a024bbabff9594c4b962) Importer(815301076,1909f56bfc062723c751e8b465ee728b) [PhysX] Initialized MultithreadedTaskDispatcher with 20 workers.
 -> (artifact id: 'f1245071cd0bf0e74a46daf6028a8d1a') in 0.055484 seconds
Number of updated asset objects reloaded before import = 0
Number of asset objects unloaded after import = 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  5.757 seconds
Refreshing native plugins compatible for Editor in 10.76 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 31.005 seconds
Domain Reload Profiling: 36759ms
	BeginReloadAssembly (255ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (19ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (98ms)
	RebuildCommonClasses (31ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (5433ms)
		LoadAssemblies (5483ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (43ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (19ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (31005ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (460ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (9ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (71ms)
			ProcessInitializeOnLoadAttributes (307ms)
			ProcessInitializeOnLoadMethodAttributes (62ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 6.50 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5653 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6389.
Memory consumption went from 203.7 MB to 202.9 MB.
Total: 6.840600 ms (FindLiveObjects: 0.279200 ms CreateObjectMapping: 0.175100 ms MarkObjects: 5.725600 ms  DeleteObjects: 0.659900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.481 seconds
Refreshing native plugins compatible for Editor in 4.06 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.291 seconds
Domain Reload Profiling: 1763ms
	BeginReloadAssembly (131ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (33ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (38ms)
	LoadAllAssembliesAndSetupDomain (260ms)
		LoadAssemblies (303ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1291ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (572ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (14ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (88ms)
			ProcessInitializeOnLoadAttributes (389ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 7.77 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5653 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6394.
Memory consumption went from 203.7 MB to 202.9 MB.
Total: 4.571100 ms (FindLiveObjects: 0.388200 ms CreateObjectMapping: 0.250500 ms MarkObjects: 3.370100 ms  DeleteObjects: 0.561000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.341 seconds
Refreshing native plugins compatible for Editor in 3.69 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.890 seconds
Domain Reload Profiling: 1228ms
	BeginReloadAssembly (111ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (182ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (890ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (346ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (232ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5653 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6399.
Memory consumption went from 203.7 MB to 202.8 MB.
Total: 4.579600 ms (FindLiveObjects: 0.449000 ms CreateObjectMapping: 0.252200 ms MarkObjects: 3.303600 ms  DeleteObjects: 0.573300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.382 seconds
Refreshing native plugins compatible for Editor in 3.72 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.853 seconds
Domain Reload Profiling: 1232ms
	BeginReloadAssembly (113ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (213ms)
		LoadAssemblies (249ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (853ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (351ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (239ms)
			ProcessInitializeOnLoadMethodAttributes (43ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5653 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6404.
Memory consumption went from 203.8 MB to 202.9 MB.
Total: 4.496800 ms (FindLiveObjects: 0.454200 ms CreateObjectMapping: 0.213500 ms MarkObjects: 3.251600 ms  DeleteObjects: 0.575200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.347 seconds
Refreshing native plugins compatible for Editor in 3.72 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.903 seconds
Domain Reload Profiling: 1248ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (178ms)
		LoadAssemblies (218ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (903ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (352ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (56ms)
			ProcessInitializeOnLoadAttributes (233ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 9.07 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5653 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6409.
Memory consumption went from 203.8 MB to 202.9 MB.
Total: 4.379600 ms (FindLiveObjects: 0.422000 ms CreateObjectMapping: 0.216400 ms MarkObjects: 3.158200 ms  DeleteObjects: 0.581300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.343 seconds
Refreshing native plugins compatible for Editor in 3.56 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.982 seconds
Domain Reload Profiling: 1323ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (175ms)
		LoadAssemblies (215ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (983ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (349ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (234ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.47 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5653 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6414.
Memory consumption went from 203.8 MB to 202.9 MB.
Total: 4.157700 ms (FindLiveObjects: 0.390900 ms CreateObjectMapping: 0.245000 ms MarkObjects: 3.026800 ms  DeleteObjects: 0.493500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.349 seconds
Refreshing native plugins compatible for Editor in 4.28 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.924 seconds
Domain Reload Profiling: 1270ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (183ms)
		LoadAssemblies (223ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (925ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (368ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (245ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 7.40 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5653 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6419.
Memory consumption went from 203.8 MB to 202.9 MB.
Total: 4.544700 ms (FindLiveObjects: 0.426700 ms CreateObjectMapping: 0.244900 ms MarkObjects: 3.182800 ms  DeleteObjects: 0.689000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.340 seconds
Refreshing native plugins compatible for Editor in 4.94 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.883 seconds
Domain Reload Profiling: 1220ms
	BeginReloadAssembly (111ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (180ms)
		LoadAssemblies (219ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (883ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (350ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (234ms)
			ProcessInitializeOnLoadMethodAttributes (42ms)
			AfterProcessingInitializeOnLoad (7ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.62 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5653 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6424.
Memory consumption went from 203.7 MB to 202.8 MB.
Total: 4.961100 ms (FindLiveObjects: 0.852800 ms CreateObjectMapping: 0.324600 ms MarkObjects: 3.288200 ms  DeleteObjects: 0.493900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  3.420 seconds
Refreshing native plugins compatible for Editor in 4.40 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 27.652 seconds
Domain Reload Profiling: 31068ms
	BeginReloadAssembly (327ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (24ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (146ms)
	RebuildCommonClasses (28ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (31ms)
	LoadAllAssembliesAndSetupDomain (3023ms)
		LoadAssemblies (3080ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (52ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (23ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (27652ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (379ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (60ms)
			ProcessInitializeOnLoadAttributes (254ms)
			ProcessInitializeOnLoadMethodAttributes (48ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 5.79 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5654 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6430.
Memory consumption went from 203.8 MB to 202.9 MB.
Total: 6.337200 ms (FindLiveObjects: 0.624900 ms CreateObjectMapping: 0.232300 ms MarkObjects: 4.914600 ms  DeleteObjects: 0.564200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.385 seconds
Refreshing native plugins compatible for Editor in 3.62 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.929 seconds
Domain Reload Profiling: 1311ms
	BeginReloadAssembly (117ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (23ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (215ms)
		LoadAssemblies (258ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (929ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (343ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (228ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 5.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5654 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6435.
Memory consumption went from 203.8 MB to 202.9 MB.
Total: 4.184000 ms (FindLiveObjects: 0.420500 ms CreateObjectMapping: 0.258400 ms MarkObjects: 2.973900 ms  DeleteObjects: 0.530300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.343 seconds
Refreshing native plugins compatible for Editor in 3.49 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.904 seconds
Domain Reload Profiling: 1244ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (178ms)
		LoadAssemblies (220ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (904ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (355ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (235ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 6.43 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5654 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6440.
Memory consumption went from 203.8 MB to 202.9 MB.
Total: 4.828900 ms (FindLiveObjects: 0.513600 ms CreateObjectMapping: 0.230400 ms MarkObjects: 3.556800 ms  DeleteObjects: 0.526900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  5.629 seconds
Refreshing native plugins compatible for Editor in 3.91 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 50.731 seconds
Domain Reload Profiling: 56357ms
	BeginReloadAssembly (137ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (34ms)
	RebuildCommonClasses (22ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (5439ms)
		LoadAssemblies (5468ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (36ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (17ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (50731ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (361ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (60ms)
			ProcessInitializeOnLoadAttributes (241ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 6.51 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5655 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6446.
Memory consumption went from 203.8 MB to 203.0 MB.
Total: 6.153600 ms (FindLiveObjects: 0.508700 ms CreateObjectMapping: 0.235400 ms MarkObjects: 4.820200 ms  DeleteObjects: 0.588100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.520 seconds
Refreshing native plugins compatible for Editor in 4.12 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.219 seconds
Domain Reload Profiling: 1734ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (32ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (291ms)
		LoadAssemblies (339ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (25ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1220ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (478ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (81ms)
			ProcessInitializeOnLoadAttributes (321ms)
			ProcessInitializeOnLoadMethodAttributes (57ms)
			AfterProcessingInitializeOnLoad (8ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 6.71 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5655 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6451.
Memory consumption went from 203.8 MB to 202.9 MB.
Total: 5.009900 ms (FindLiveObjects: 0.460800 ms CreateObjectMapping: 0.279700 ms MarkObjects: 3.680000 ms  DeleteObjects: 0.587800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.390 seconds
Refreshing native plugins compatible for Editor in 4.32 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.095 seconds
Domain Reload Profiling: 1482ms
	BeginReloadAssembly (115ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (23ms)
	LoadAllAssembliesAndSetupDomain (222ms)
		LoadAssemblies (233ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (42ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (19ms)
			ResolveRequiredComponents (11ms)
	FinalizeReload (1095ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (382ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (61ms)
			ProcessInitializeOnLoadAttributes (259ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.51 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5656 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6457.
Memory consumption went from 203.9 MB to 203.0 MB.
Total: 3.510300 ms (FindLiveObjects: 0.325900 ms CreateObjectMapping: 0.217900 ms MarkObjects: 2.581100 ms  DeleteObjects: 0.384300 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.340 seconds
Refreshing native plugins compatible for Editor in 3.96 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.909 seconds
Domain Reload Profiling: 1247ms
	BeginReloadAssembly (109ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (25ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (24ms)
	LoadAllAssembliesAndSetupDomain (178ms)
		LoadAssemblies (214ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (16ms)
			TypeCache.Refresh (6ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (909ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (355ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (238ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 8.15 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5656 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6462.
Memory consumption went from 203.9 MB to 203.0 MB.
Total: 4.725700 ms (FindLiveObjects: 0.406500 ms CreateObjectMapping: 0.299000 ms MarkObjects: 3.506600 ms  DeleteObjects: 0.512100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.029 seconds
Refreshing native plugins compatible for Editor in 4.08 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.775 seconds
Domain Reload Profiling: 2801ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (866ms)
		LoadAssemblies (881ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (37ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (18ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (1775ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (343ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (56ms)
			ProcessInitializeOnLoadAttributes (228ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (10ms)
Refreshing native plugins compatible for Editor in 7.41 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5657 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6468.
Memory consumption went from 203.9 MB to 203.0 MB.
Total: 4.532300 ms (FindLiveObjects: 0.491900 ms CreateObjectMapping: 0.272100 ms MarkObjects: 3.250000 ms  DeleteObjects: 0.517100 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.327 seconds
Refreshing native plugins compatible for Editor in 3.56 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.927 seconds
Domain Reload Profiling: 1251ms
	BeginReloadAssembly (106ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (24ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (169ms)
		LoadAssemblies (206ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (927ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (349ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (58ms)
			ProcessInitializeOnLoadAttributes (234ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 6.88 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5657 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6473.
Memory consumption went from 203.9 MB to 203.0 MB.
Total: 4.636000 ms (FindLiveObjects: 0.546200 ms CreateObjectMapping: 0.307400 ms MarkObjects: 3.280300 ms  DeleteObjects: 0.500600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.749 seconds
Refreshing native plugins compatible for Editor in 4.33 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.932 seconds
Domain Reload Profiling: 2675ms
	BeginReloadAssembly (392ms)
		ExecutionOrderSort (1ms)
		DisableScriptedObjects (29ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (186ms)
	RebuildCommonClasses (36ms)
	RebuildNativeTypeToScriptingClass (12ms)
	initialDomainReloadingComplete (25ms)
	LoadAllAssembliesAndSetupDomain (279ms)
		LoadAssemblies (367ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (10ms)
	FinalizeReload (1932ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (505ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (79ms)
			ProcessInitializeOnLoadAttributes (340ms)
			ProcessInitializeOnLoadMethodAttributes (67ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 9.49 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5657 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6478.
Memory consumption went from 203.8 MB to 203.0 MB.
Total: 3.558700 ms (FindLiveObjects: 0.524900 ms CreateObjectMapping: 0.098100 ms MarkObjects: 2.539800 ms  DeleteObjects: 0.394800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.357 seconds
Refreshing native plugins compatible for Editor in 4.03 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.942 seconds
Domain Reload Profiling: 1296ms
	BeginReloadAssembly (112ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (27ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (22ms)
	LoadAllAssembliesAndSetupDomain (193ms)
		LoadAssemblies (233ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (942ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (372ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (65ms)
			ProcessInitializeOnLoadAttributes (245ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.24 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5657 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6483.
Memory consumption went from 203.9 MB to 203.0 MB.
Total: 4.697800 ms (FindLiveObjects: 0.461100 ms CreateObjectMapping: 0.292300 ms MarkObjects: 3.364300 ms  DeleteObjects: 0.578200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.349 seconds
Refreshing native plugins compatible for Editor in 3.55 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.894 seconds
Domain Reload Profiling: 1240ms
	BeginReloadAssembly (117ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (181ms)
		LoadAssemblies (219ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (894ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (351ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (237ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 6.91 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5657 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6488.
Memory consumption went from 203.9 MB to 203.0 MB.
Total: 4.636600 ms (FindLiveObjects: 0.367800 ms CreateObjectMapping: 0.270100 ms MarkObjects: 3.483600 ms  DeleteObjects: 0.514000 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.342 seconds
Refreshing native plugins compatible for Editor in 3.57 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.877 seconds
Domain Reload Profiling: 1216ms
	BeginReloadAssembly (119ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (20ms)
	RebuildNativeTypeToScriptingClass (6ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (174ms)
		LoadAssemblies (218ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (877ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (344ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (55ms)
			ProcessInitializeOnLoadAttributes (229ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 7.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5657 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6493.
Memory consumption went from 203.9 MB to 203.0 MB.
Total: 4.495200 ms (FindLiveObjects: 0.404500 ms CreateObjectMapping: 0.197500 ms MarkObjects: 3.320300 ms  DeleteObjects: 0.571200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  4.683 seconds
Refreshing native plugins compatible for Editor in 6.87 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 38.579 seconds
Domain Reload Profiling: 43258ms
	BeginReloadAssembly (149ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (12ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (4462ms)
		LoadAssemblies (4482ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (53ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (22ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (38579ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (366ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (63ms)
			ProcessInitializeOnLoadAttributes (240ms)
			ProcessInitializeOnLoadMethodAttributes (47ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Script is not up to date after domain reload: guid(cad46f0a899e4214ebe05fc47c8d3f6d) path("Assets/Scripts/Debug/QuickInventoryFix.cs") state(2)
Refreshing native plugins compatible for Editor in 8.08 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5657 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6498.
Memory consumption went from 203.9 MB to 203.0 MB.
Total: 4.911300 ms (FindLiveObjects: 0.509600 ms CreateObjectMapping: 0.447500 ms MarkObjects: 3.305800 ms  DeleteObjects: 0.647200 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.532 seconds
Refreshing native plugins compatible for Editor in 4.75 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.385 seconds
Domain Reload Profiling: 1913ms
	BeginReloadAssembly (150ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (36ms)
	RebuildCommonClasses (29ms)
	RebuildNativeTypeToScriptingClass (10ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (307ms)
		LoadAssemblies (322ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (57ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (2ms)
			ScanForSourceGeneratedMonoScriptInfo (31ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1385ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (513ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (10ms)
			SetLoadedEditorAssemblies (5ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (85ms)
			ProcessInitializeOnLoadAttributes (340ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (9ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 9.00 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5658 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6504.
Memory consumption went from 203.9 MB to 203.0 MB.
Total: 4.645700 ms (FindLiveObjects: 0.432900 ms CreateObjectMapping: 0.351200 ms MarkObjects: 3.278400 ms  DeleteObjects: 0.581900 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.335 seconds
Refreshing native plugins compatible for Editor in 4.01 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.891 seconds
Domain Reload Profiling: 1224ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (26ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (178ms)
		LoadAssemblies (216ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (13ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (891ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (346ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (7ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (56ms)
			ProcessInitializeOnLoadAttributes (229ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.53 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5658 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6509.
Memory consumption went from 203.9 MB to 203.1 MB.
Total: 5.057100 ms (FindLiveObjects: 0.427900 ms CreateObjectMapping: 0.472800 ms MarkObjects: 3.590900 ms  DeleteObjects: 0.564600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.345 seconds
Refreshing native plugins compatible for Editor in 3.76 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.891 seconds
Domain Reload Profiling: 1233ms
	BeginReloadAssembly (118ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (31ms)
	RebuildCommonClasses (19ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (19ms)
	LoadAllAssembliesAndSetupDomain (179ms)
		LoadAssemblies (219ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (14ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (892ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (353ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (57ms)
			ProcessInitializeOnLoadAttributes (238ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.28 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5658 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6514.
Memory consumption went from 204.0 MB to 203.1 MB.
Total: 4.614200 ms (FindLiveObjects: 0.426000 ms CreateObjectMapping: 0.218600 ms MarkObjects: 3.448500 ms  DeleteObjects: 0.519800 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.400 seconds
Refreshing native plugins compatible for Editor in 3.73 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.447 seconds
Domain Reload Profiling: 1845ms
	BeginReloadAssembly (117ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (28ms)
	RebuildCommonClasses (27ms)
	RebuildNativeTypeToScriptingClass (8ms)
	initialDomainReloadingComplete (21ms)
	LoadAllAssembliesAndSetupDomain (225ms)
		LoadAssemblies (250ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (31ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (1ms)
			ScanForSourceGeneratedMonoScriptInfo (15ms)
			ResolveRequiredComponents (7ms)
	FinalizeReload (1447ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (341ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (54ms)
			ProcessInitializeOnLoadAttributes (229ms)
			ProcessInitializeOnLoadMethodAttributes (44ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 7.85 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5658 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6519.
Memory consumption went from 204.0 MB to 203.1 MB.
Total: 2.998300 ms (FindLiveObjects: 0.286500 ms CreateObjectMapping: 0.126300 ms MarkObjects: 2.259200 ms  DeleteObjects: 0.325500 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.333 seconds
Refreshing native plugins compatible for Editor in 4.42 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.890 seconds
Domain Reload Profiling: 1221ms
	BeginReloadAssembly (108ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (23ms)
	RebuildCommonClasses (21ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (20ms)
	LoadAllAssembliesAndSetupDomain (173ms)
		LoadAssemblies (212ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (15ms)
			TypeCache.Refresh (5ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (8ms)
	FinalizeReload (891ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (350ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (3ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (56ms)
			ProcessInitializeOnLoadAttributes (233ms)
			ProcessInitializeOnLoadMethodAttributes (46ms)
			AfterProcessingInitializeOnLoad (6ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Refreshing native plugins compatible for Editor in 6.61 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5658 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6524.
Memory consumption went from 204.0 MB to 203.1 MB.
Total: 4.483300 ms (FindLiveObjects: 0.469000 ms CreateObjectMapping: 0.307300 ms MarkObjects: 3.187800 ms  DeleteObjects: 0.511600 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.485 seconds
Refreshing native plugins compatible for Editor in 5.81 ms, found 4 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
[Package Manager] Server::EnsureServerProcessIsRunning -- launch failed, reason: Unity was launched with the -noUpm command-line argument
[Package Manager] UpmClient::Send -- Unable to send message (not connected to UPM process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in 25.778 seconds
Domain Reload Profiling: 26260ms
	BeginReloadAssembly (129ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (30ms)
	RebuildCommonClasses (26ms)
	RebuildNativeTypeToScriptingClass (7ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (293ms)
		LoadAssemblies (336ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (19ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			ScanForSourceGeneratedMonoScriptInfo (0ms)
			ResolveRequiredComponents (9ms)
	FinalizeReload (25778ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (405ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (8ms)
			SetLoadedEditorAssemblies (4ms)
			RefreshPlugins (0ms)
			BeforeProcessingInitializeOnLoad (67ms)
			ProcessInitializeOnLoadAttributes (267ms)
			ProcessInitializeOnLoadMethodAttributes (53ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (11ms)
Refreshing native plugins compatible for Editor in 10.94 ms, found 4 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 5658 Unused Serialized files (Serialized files now loaded: 0)
Unloading 65 unused Assets / (0.9 MB). Loaded Objects now: 6529.
Memory consumption went from 203.9 MB to 203.0 MB.
Total: 3.365500 ms (FindLiveObjects: 0.291200 ms CreateObjectMapping: 0.119500 ms MarkObjects: 2.568700 ms  DeleteObjects: 0.385400 ms)

Prepare: number of updated asset objects reloaded= 0
AssetImportParameters requested are different than current active one (requested -> active):
  custom:container-demuxer-ogg: 62fdf1f143b41e24485cea50d1cbac27 -> 
  custom:video-codec-MediaFoundation-h265: 746d11721c4dcdbdad8f713fa42b33f4 -> 
  custom:video-decoder-ogg-theora: a1e56fd34408186e4bbccfd4996cb3dc -> 
  custom:framework-win-MediaFoundation: 216162199b28c13a410421893ffa2e32 -> 
  custom:scripting/monoscript/fileName/QuickSpriteFixer.cs: ******************************** -> 
  custom:CustomObjectIndexerAttribute: 72b91833f83ae208fae7752999fa3104 -> 
  custom:container-demuxer-webm: 4f35f7cbe854078d1ac9338744f61a02 -> 
  custom:scripting/monoscript/fileName/PixelPerfectImage.cs: 7d491e78debfbce25636498644ffe0da -> 
  custom:container-muxer-webm: aa71ff27fc2769a1b78a27578f13a17b -> 
  custom:video-decoder-webm-vp8: 9c59270c3fd7afecdb556c50c9e8de78 -> 
  custom:SearchIndexIgnoredProperties: e643bd26f0fe6173181afceb89e7c659 -> 
  custom:AudioImporter_EditorPlatform: d09bf68614088b80899f8185d706f6e7 -> 
  custom:video-encoder-webm-vp8: eb34c28f22e8b96e1ab97ce403110664 -> 
  custom:audio-decoder-ogg-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:audio-encoder-webm-vorbis: bf7c407c2cedff20999df2af8eb42d56 -> 
  custom:scripting/monoscript/fileName/PixelPerfectSpriteFixer.cs: ******************************** -> 
