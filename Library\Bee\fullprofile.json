{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21312, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21312, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21312, "tid": 8739, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21312, "tid": 8739, "ts": 1753299882692421, "dur": 8, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21312, "tid": 8739, "ts": 1753299882692439, "dur": 2, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21312, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21312, "tid": 1, "ts": 1753299881407195, "dur": 1256, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21312, "tid": 1, "ts": 1753299881408455, "dur": 17579, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21312, "tid": 1, "ts": 1753299881426036, "dur": 23940, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21312, "tid": 8739, "ts": 1753299882692443, "dur": 5, "ph": "X", "name": "", "args": {}}, {"pid": 21312, "tid": 34359738368, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881407168, "dur": 10961, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881418130, "dur": 1273570, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881418138, "dur": 469, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881418613, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881418616, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881418654, "dur": 7, "ph": "X", "name": "ProcessMessages 53", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881418662, "dur": 2403, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421070, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421137, "dur": 3, "ph": "X", "name": "ProcessMessages 1661", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421141, "dur": 109, "ph": "X", "name": "ReadAsync 1661", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421262, "dur": 6, "ph": "X", "name": "ProcessMessages 1949", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421269, "dur": 59, "ph": "X", "name": "ReadAsync 1949", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421330, "dur": 2, "ph": "X", "name": "ProcessMessages 1635", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421334, "dur": 40, "ph": "X", "name": "ReadAsync 1635", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421377, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421379, "dur": 32, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421414, "dur": 1, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421416, "dur": 36, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421455, "dur": 1, "ph": "X", "name": "ProcessMessages 1253", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421457, "dur": 27, "ph": "X", "name": "ReadAsync 1253", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421486, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421488, "dur": 25, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421517, "dur": 24, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421543, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421545, "dur": 18, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421569, "dur": 21, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421593, "dur": 25, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421620, "dur": 1, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421624, "dur": 42, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421668, "dur": 1, "ph": "X", "name": "ProcessMessages 1446", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421671, "dur": 23, "ph": "X", "name": "ReadAsync 1446", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421695, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421696, "dur": 23, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421721, "dur": 1, "ph": "X", "name": "ProcessMessages 847", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421723, "dur": 54, "ph": "X", "name": "ReadAsync 847", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421778, "dur": 1, "ph": "X", "name": "ProcessMessages 914", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421779, "dur": 26, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421807, "dur": 1, "ph": "X", "name": "ProcessMessages 1609", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421809, "dur": 18, "ph": "X", "name": "ReadAsync 1609", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421829, "dur": 1, "ph": "X", "name": "ProcessMessages 227", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421831, "dur": 22, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421856, "dur": 20, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421878, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421879, "dur": 21, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421905, "dur": 21, "ph": "X", "name": "ReadAsync 839", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421929, "dur": 58, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421990, "dur": 1, "ph": "X", "name": "ProcessMessages 1371", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881421993, "dur": 17, "ph": "X", "name": "ReadAsync 1371", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422012, "dur": 18, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422033, "dur": 21, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422057, "dur": 23, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422082, "dur": 1, "ph": "X", "name": "ProcessMessages 913", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422083, "dur": 62, "ph": "X", "name": "ReadAsync 913", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422149, "dur": 19, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422170, "dur": 24, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422196, "dur": 1, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422197, "dur": 20, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422220, "dur": 19, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422242, "dur": 19, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422265, "dur": 21, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422289, "dur": 22, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422313, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422315, "dur": 25, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422341, "dur": 1, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422343, "dur": 20, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422365, "dur": 1, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422366, "dur": 19, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422388, "dur": 22, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422413, "dur": 20, "ph": "X", "name": "ReadAsync 676", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422436, "dur": 17, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422456, "dur": 21, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422479, "dur": 20, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422502, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422503, "dur": 28, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422534, "dur": 21, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422557, "dur": 43, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422603, "dur": 44, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422648, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422650, "dur": 19, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422671, "dur": 23, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422696, "dur": 62, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422761, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422785, "dur": 1, "ph": "X", "name": "ProcessMessages 708", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422787, "dur": 22, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422811, "dur": 1, "ph": "X", "name": "ProcessMessages 617", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422813, "dur": 29, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422845, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422850, "dur": 28, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422879, "dur": 1, "ph": "X", "name": "ProcessMessages 892", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422881, "dur": 24, "ph": "X", "name": "ReadAsync 892", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422907, "dur": 16, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422926, "dur": 21, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881422950, "dur": 67, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881423020, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881423053, "dur": 1, "ph": "X", "name": "ProcessMessages 2073", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881423055, "dur": 22, "ph": "X", "name": "ReadAsync 2073", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881423079, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881423085, "dur": 941, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424027, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424030, "dur": 38, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424070, "dur": 2, "ph": "X", "name": "ProcessMessages 2138", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424073, "dur": 23, "ph": "X", "name": "ReadAsync 2138", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424098, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424100, "dur": 20, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424123, "dur": 31, "ph": "X", "name": "ReadAsync 183", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424157, "dur": 21, "ph": "X", "name": "ReadAsync 959", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424182, "dur": 21, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424212, "dur": 21, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424236, "dur": 29, "ph": "X", "name": "ReadAsync 249", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424269, "dur": 31, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424303, "dur": 35, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424340, "dur": 41, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424383, "dur": 1, "ph": "X", "name": "ProcessMessages 1190", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424386, "dur": 36, "ph": "X", "name": "ReadAsync 1190", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424426, "dur": 2, "ph": "X", "name": "ProcessMessages 868", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424429, "dur": 51, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424483, "dur": 1, "ph": "X", "name": "ProcessMessages 1434", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424486, "dur": 43, "ph": "X", "name": "ReadAsync 1434", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424539, "dur": 1, "ph": "X", "name": "ProcessMessages 1382", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424541, "dur": 30, "ph": "X", "name": "ReadAsync 1382", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424574, "dur": 1, "ph": "X", "name": "ProcessMessages 955", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424576, "dur": 23, "ph": "X", "name": "ReadAsync 955", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424602, "dur": 19, "ph": "X", "name": "ReadAsync 739", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424624, "dur": 23, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424649, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424651, "dur": 24, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424678, "dur": 26, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424707, "dur": 1, "ph": "X", "name": "ProcessMessages 668", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424709, "dur": 22, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424734, "dur": 23, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424759, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424760, "dur": 18, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424781, "dur": 22, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424805, "dur": 48, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424855, "dur": 1, "ph": "X", "name": "ProcessMessages 1151", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424856, "dur": 22, "ph": "X", "name": "ReadAsync 1151", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424881, "dur": 21, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424905, "dur": 33, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424941, "dur": 24, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424968, "dur": 27, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881424998, "dur": 38, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425040, "dur": 1, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425041, "dur": 22, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425066, "dur": 22, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425092, "dur": 25, "ph": "X", "name": "ReadAsync 965", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425119, "dur": 1, "ph": "X", "name": "ProcessMessages 872", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425121, "dur": 21, "ph": "X", "name": "ReadAsync 872", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425145, "dur": 23, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425170, "dur": 24, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425197, "dur": 19, "ph": "X", "name": "ReadAsync 953", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425219, "dur": 1, "ph": "X", "name": "ProcessMessages 733", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425221, "dur": 20, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425244, "dur": 23, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425270, "dur": 22, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425294, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425297, "dur": 21, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425320, "dur": 1, "ph": "X", "name": "ProcessMessages 731", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425323, "dur": 37, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425362, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425386, "dur": 29, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425419, "dur": 20, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425442, "dur": 19, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425464, "dur": 23, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425490, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425493, "dur": 26, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425520, "dur": 1, "ph": "X", "name": "ProcessMessages 877", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425522, "dur": 23, "ph": "X", "name": "ReadAsync 877", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425548, "dur": 23, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425574, "dur": 34, "ph": "X", "name": "ReadAsync 813", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425610, "dur": 1, "ph": "X", "name": "ProcessMessages 589", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425612, "dur": 20, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425635, "dur": 155, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425793, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425814, "dur": 1, "ph": "X", "name": "ProcessMessages 682", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425816, "dur": 21, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425840, "dur": 20, "ph": "X", "name": "ReadAsync 700", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425862, "dur": 28, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425893, "dur": 18, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425913, "dur": 24, "ph": "X", "name": "ReadAsync 255", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425939, "dur": 1, "ph": "X", "name": "ProcessMessages 755", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425941, "dur": 20, "ph": "X", "name": "ReadAsync 755", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425963, "dur": 19, "ph": "X", "name": "ReadAsync 579", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881425986, "dur": 35, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426024, "dur": 22, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426049, "dur": 1, "ph": "X", "name": "ProcessMessages 625", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426050, "dur": 19, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426072, "dur": 25, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426099, "dur": 18, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426120, "dur": 26, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426150, "dur": 18, "ph": "X", "name": "ReadAsync 852", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426170, "dur": 20, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426193, "dur": 21, "ph": "X", "name": "ReadAsync 648", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426217, "dur": 19, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426239, "dur": 18, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426260, "dur": 24, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426287, "dur": 21, "ph": "X", "name": "ReadAsync 765", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426310, "dur": 20, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426332, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426334, "dur": 17, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426354, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426376, "dur": 23, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426402, "dur": 19, "ph": "X", "name": "ReadAsync 801", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426424, "dur": 21, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426447, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426470, "dur": 20, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426493, "dur": 20, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426515, "dur": 18, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426537, "dur": 38, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426578, "dur": 20, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426599, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426600, "dur": 23, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426627, "dur": 17, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426646, "dur": 20, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426669, "dur": 20, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426692, "dur": 20, "ph": "X", "name": "ReadAsync 692", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426715, "dur": 19, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426736, "dur": 26, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426764, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426765, "dur": 24, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426792, "dur": 18, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426813, "dur": 18, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426833, "dur": 19, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426854, "dur": 28, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426885, "dur": 28, "ph": "X", "name": "ReadAsync 997", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426915, "dur": 1, "ph": "X", "name": "ProcessMessages 946", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426917, "dur": 19, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426939, "dur": 20, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426961, "dur": 22, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881426987, "dur": 19, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427008, "dur": 22, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427033, "dur": 20, "ph": "X", "name": "ReadAsync 766", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427056, "dur": 27, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427085, "dur": 24, "ph": "X", "name": "ReadAsync 951", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427113, "dur": 20, "ph": "X", "name": "ReadAsync 348", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427136, "dur": 21, "ph": "X", "name": "ReadAsync 761", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427160, "dur": 19, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427183, "dur": 18, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427203, "dur": 43, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427249, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427250, "dur": 21, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427274, "dur": 19, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427296, "dur": 20, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427319, "dur": 28, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427348, "dur": 1, "ph": "X", "name": "ProcessMessages 822", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427349, "dur": 20, "ph": "X", "name": "ReadAsync 822", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427372, "dur": 23, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427397, "dur": 20, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427420, "dur": 17, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427441, "dur": 19, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427463, "dur": 148, "ph": "X", "name": "ReadAsync 616", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427613, "dur": 51, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427665, "dur": 2, "ph": "X", "name": "ProcessMessages 4577", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427669, "dur": 23, "ph": "X", "name": "ReadAsync 4577", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427694, "dur": 17, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427713, "dur": 22, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427737, "dur": 33, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427773, "dur": 27, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427802, "dur": 27, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427831, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881427832, "dur": 203, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428050, "dur": 33, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428085, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428086, "dur": 38, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428126, "dur": 2, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428129, "dur": 29, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428161, "dur": 25, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428189, "dur": 22, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428214, "dur": 18, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428235, "dur": 25, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428262, "dur": 20, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428285, "dur": 24, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428312, "dur": 24, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428339, "dur": 23, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428364, "dur": 25, "ph": "X", "name": "ReadAsync 553", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428392, "dur": 21, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428417, "dur": 24, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428444, "dur": 26, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428473, "dur": 22, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428498, "dur": 60, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428559, "dur": 1, "ph": "X", "name": "ProcessMessages 1500", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428561, "dur": 21, "ph": "X", "name": "ReadAsync 1500", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428584, "dur": 21, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428607, "dur": 22, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428633, "dur": 24, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428660, "dur": 53, "ph": "X", "name": "ReadAsync 938", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428716, "dur": 31, "ph": "X", "name": "ReadAsync 115", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428749, "dur": 1, "ph": "X", "name": "ProcessMessages 1720", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428750, "dur": 24, "ph": "X", "name": "ReadAsync 1720", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428777, "dur": 19, "ph": "X", "name": "ReadAsync 619", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428801, "dur": 115, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881428919, "dur": 102, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429023, "dur": 1, "ph": "X", "name": "ProcessMessages 988", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429025, "dur": 88, "ph": "X", "name": "ReadAsync 988", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429114, "dur": 2, "ph": "X", "name": "ProcessMessages 3237", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429117, "dur": 19, "ph": "X", "name": "ReadAsync 3237", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429139, "dur": 17, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429159, "dur": 18, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429178, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429180, "dur": 21, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429203, "dur": 68, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429273, "dur": 1, "ph": "X", "name": "ProcessMessages 859", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429275, "dur": 18, "ph": "X", "name": "ReadAsync 859", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429296, "dur": 19, "ph": "X", "name": "ReadAsync 656", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429317, "dur": 19, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429340, "dur": 21, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429363, "dur": 18, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429383, "dur": 20, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429406, "dur": 21, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429430, "dur": 19, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429452, "dur": 18, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429473, "dur": 20, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429496, "dur": 20, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429519, "dur": 20, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429542, "dur": 19, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429564, "dur": 20, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429587, "dur": 19, "ph": "X", "name": "ReadAsync 467", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429609, "dur": 20, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429631, "dur": 19, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429653, "dur": 20, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429675, "dur": 21, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429699, "dur": 28, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429729, "dur": 20, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429752, "dur": 28, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429782, "dur": 18, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429803, "dur": 23, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429828, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429854, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429856, "dur": 19, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429878, "dur": 52, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429935, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429962, "dur": 18, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881429982, "dur": 42, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430027, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430050, "dur": 39, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430092, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430094, "dur": 19, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430116, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430146, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430175, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430176, "dur": 29, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430209, "dur": 55, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430268, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430294, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430296, "dur": 19, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430318, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430352, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430375, "dur": 20, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430398, "dur": 47, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430447, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430472, "dur": 19, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430493, "dur": 41, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430536, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430567, "dur": 19, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430589, "dur": 33, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430625, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430648, "dur": 20, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430671, "dur": 42, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430715, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430739, "dur": 25, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430767, "dur": 34, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430803, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430827, "dur": 20, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430849, "dur": 49, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430901, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430924, "dur": 19, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430946, "dur": 40, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881430988, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431011, "dur": 20, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431034, "dur": 37, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431074, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431104, "dur": 18, "ph": "X", "name": "ReadAsync 1022", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431124, "dur": 37, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431164, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431186, "dur": 20, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431209, "dur": 41, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431252, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431279, "dur": 34, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431315, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431316, "dur": 42, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431361, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431384, "dur": 20, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431407, "dur": 45, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431455, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431480, "dur": 1, "ph": "X", "name": "ProcessMessages 772", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431482, "dur": 19, "ph": "X", "name": "ReadAsync 772", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431504, "dur": 33, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431540, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431566, "dur": 19, "ph": "X", "name": "ReadAsync 374", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431586, "dur": 1, "ph": "X", "name": "ProcessMessages 371", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431588, "dur": 19, "ph": "X", "name": "ReadAsync 371", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431610, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431646, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431670, "dur": 19, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431692, "dur": 40, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431735, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431760, "dur": 18, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431781, "dur": 36, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431820, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431843, "dur": 20, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431865, "dur": 40, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431908, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431931, "dur": 23, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881431957, "dur": 43, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432003, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432041, "dur": 20, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432064, "dur": 40, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432106, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432137, "dur": 1, "ph": "X", "name": "ProcessMessages 848", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432138, "dur": 17, "ph": "X", "name": "ReadAsync 848", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432159, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432195, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432219, "dur": 22, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432244, "dur": 21, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432267, "dur": 35, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432305, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432327, "dur": 20, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432349, "dur": 23, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432376, "dur": 22, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432400, "dur": 17, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432420, "dur": 17, "ph": "X", "name": "ReadAsync 243", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432440, "dur": 29, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432471, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432499, "dur": 21, "ph": "X", "name": "ReadAsync 210", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432522, "dur": 18, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432543, "dur": 36, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432581, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432605, "dur": 20, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432628, "dur": 17, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432647, "dur": 30, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432680, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432704, "dur": 19, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432727, "dur": 39, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432769, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432793, "dur": 19, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432814, "dur": 39, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432857, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432878, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432900, "dur": 17, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432920, "dur": 32, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432954, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881432977, "dur": 21, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433000, "dur": 40, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433043, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433065, "dur": 21, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433089, "dur": 37, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433128, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433130, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433153, "dur": 19, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433174, "dur": 38, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433215, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433246, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433247, "dur": 18, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433267, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433301, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433324, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433345, "dur": 43, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433391, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433417, "dur": 19, "ph": "X", "name": "ReadAsync 720", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433438, "dur": 1, "ph": "X", "name": "ProcessMessages 365", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433440, "dur": 35, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433477, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433504, "dur": 18, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433525, "dur": 38, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433565, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433587, "dur": 20, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433609, "dur": 40, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433652, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433712, "dur": 23, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433738, "dur": 21, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433762, "dur": 24, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433789, "dur": 18, "ph": "X", "name": "ReadAsync 777", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433809, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433850, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433872, "dur": 20, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433895, "dur": 20, "ph": "X", "name": "ReadAsync 733", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433918, "dur": 58, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881433978, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434001, "dur": 18, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434021, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434044, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434084, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434105, "dur": 19, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434128, "dur": 16, "ph": "X", "name": "ReadAsync 641", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434146, "dur": 30, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434179, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434204, "dur": 19, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434227, "dur": 37, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434266, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434288, "dur": 17, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434308, "dur": 18, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434327, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434329, "dur": 45, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434376, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434406, "dur": 1, "ph": "X", "name": "ProcessMessages 871", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434407, "dur": 18, "ph": "X", "name": "ReadAsync 871", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434427, "dur": 32, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434461, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434491, "dur": 1, "ph": "X", "name": "ProcessMessages 834", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434492, "dur": 18, "ph": "X", "name": "ReadAsync 834", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434513, "dur": 35, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434550, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434574, "dur": 19, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434596, "dur": 40, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434638, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434662, "dur": 17, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434682, "dur": 18, "ph": "X", "name": "ReadAsync 238", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434703, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434745, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434767, "dur": 19, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434789, "dur": 40, "ph": "X", "name": "ReadAsync 637", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434831, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434896, "dur": 1, "ph": "X", "name": "ProcessMessages 1231", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434898, "dur": 29, "ph": "X", "name": "ReadAsync 1231", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434929, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434951, "dur": 21, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881434975, "dur": 38, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435015, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435038, "dur": 18, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435058, "dur": 19, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435081, "dur": 19, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435103, "dur": 19, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435125, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435147, "dur": 37, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435187, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435209, "dur": 19, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435230, "dur": 1, "ph": "X", "name": "ProcessMessages 537", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435231, "dur": 40, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435274, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435296, "dur": 41, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435340, "dur": 46, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435388, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435410, "dur": 20, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435432, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435434, "dur": 36, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435473, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435495, "dur": 19, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435517, "dur": 40, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435559, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435582, "dur": 36, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435621, "dur": 24, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435646, "dur": 1, "ph": "X", "name": "ProcessMessages 921", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435648, "dur": 27, "ph": "X", "name": "ReadAsync 921", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435677, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435679, "dur": 25, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435707, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435710, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435773, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435793, "dur": 1, "ph": "X", "name": "ProcessMessages 203", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435794, "dur": 22, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435819, "dur": 24, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435846, "dur": 19, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435868, "dur": 18, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435888, "dur": 20, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435910, "dur": 50, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435963, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881435982, "dur": 183, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436169, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436172, "dur": 87, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436263, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436265, "dur": 69, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436341, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436345, "dur": 63, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436412, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436415, "dur": 49, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436467, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436469, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436508, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436545, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436546, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436586, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436588, "dur": 22, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436612, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436614, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436646, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436648, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436698, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436701, "dur": 53, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436757, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436760, "dur": 47, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436810, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436813, "dur": 47, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436863, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436865, "dur": 46, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436915, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436917, "dur": 22, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881436943, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437055, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437057, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437114, "dur": 2, "ph": "X", "name": "ProcessMessages 244", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437117, "dur": 42, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437162, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437165, "dur": 41, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437209, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437212, "dur": 48, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437263, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437266, "dur": 27, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437295, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437297, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437336, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437339, "dur": 58, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437401, "dur": 2, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437405, "dur": 30, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437437, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437440, "dur": 38, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437482, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437485, "dur": 61, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437549, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437552, "dur": 48, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437603, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437606, "dur": 37, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437646, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437648, "dur": 44, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437695, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437698, "dur": 46, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437747, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437750, "dur": 44, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437797, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437799, "dur": 48, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437851, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437853, "dur": 52, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437909, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437913, "dur": 49, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437966, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881437969, "dur": 44, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438016, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438019, "dur": 41, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438064, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438069, "dur": 37, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438109, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438112, "dur": 37, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438153, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438156, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438201, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438203, "dur": 98, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438305, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438307, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438361, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438365, "dur": 45, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438413, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438416, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438443, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438445, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438476, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438478, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438541, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438544, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438579, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438581, "dur": 44, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438629, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438632, "dur": 42, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438678, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438681, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438730, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438733, "dur": 49, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438785, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438788, "dur": 54, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438847, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881438880, "dur": 14993, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881453882, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881453886, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881453945, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881453948, "dur": 1635, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881455589, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881455592, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881455642, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881455645, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881455697, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881455699, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881455745, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881455747, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881455784, "dur": 37, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881455823, "dur": 7559, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881463393, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881463397, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881463457, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881463460, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881463506, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881463529, "dur": 246, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881463777, "dur": 99, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881463881, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881463883, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881463926, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881463928, "dur": 84, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464017, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464049, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464051, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464083, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464085, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464119, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464121, "dur": 46, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464170, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464172, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464203, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464205, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464236, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464293, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464295, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464327, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464358, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464360, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464390, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464392, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464426, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464456, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464458, "dur": 454, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464916, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464918, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881464952, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465035, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465068, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465070, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465102, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465105, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465138, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465140, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465176, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465178, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465212, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465216, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465249, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465250, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465269, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465296, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465298, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465327, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465329, "dur": 102, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465436, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465464, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465465, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465502, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465505, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465529, "dur": 82, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465616, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465656, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465658, "dur": 64, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465728, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465759, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465761, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465792, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465794, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465826, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465828, "dur": 48, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465880, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465901, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465936, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881465964, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466008, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466038, "dur": 15, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466056, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466075, "dur": 242, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466321, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466325, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466348, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466350, "dur": 28, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466382, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466384, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466417, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466419, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466448, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466450, "dur": 25, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466479, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466509, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466511, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466540, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466572, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466575, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466609, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466611, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466649, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466651, "dur": 32, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466685, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466687, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466730, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466733, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466777, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466779, "dur": 163, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466946, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466948, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881466984, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467021, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467024, "dur": 38, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467066, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467068, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467103, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467105, "dur": 53, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467162, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467164, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467205, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467207, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467244, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467246, "dur": 132, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467382, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467384, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467426, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467428, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467467, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467470, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467510, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467539, "dur": 46, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467590, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467592, "dur": 59, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467655, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467684, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467711, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467746, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467784, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467786, "dur": 59, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467850, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467872, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467959, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881467961, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468004, "dur": 122, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468130, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468167, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468169, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468218, "dur": 3, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468222, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468269, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468297, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468332, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468350, "dur": 51, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468406, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468445, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468447, "dur": 23, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468474, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468491, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468518, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468541, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468587, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468620, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468622, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468665, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468668, "dur": 200, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468879, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468885, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468915, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468945, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468947, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881468970, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469053, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469076, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469129, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469149, "dur": 221, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469373, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469436, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469470, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469472, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469506, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469542, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469562, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469600, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469635, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469636, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469671, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469701, "dur": 143, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469852, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469856, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469895, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469897, "dur": 47, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881469947, "dur": 122, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470073, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470095, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470115, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470161, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470187, "dur": 179, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470368, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470402, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470461, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470490, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470515, "dur": 208, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470724, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470727, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470748, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881470750, "dur": 59427, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881530183, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881530186, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881530229, "dur": 75, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881530305, "dur": 16642, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881546959, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881546964, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547055, "dur": 7, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547063, "dur": 33, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547098, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547100, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547132, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547134, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547168, "dur": 27, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547198, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547200, "dur": 29, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547231, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547233, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547262, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547264, "dur": 27, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547293, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547295, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547324, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547325, "dur": 29, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547356, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547358, "dur": 26, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547387, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547389, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547425, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547427, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547457, "dur": 179, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547643, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547647, "dur": 60, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547711, "dur": 3, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881547715, "dur": 336, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881548055, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881548057, "dur": 77, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881548139, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881548141, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881548203, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881548206, "dur": 38, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881548248, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881548306, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881548307, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881548346, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881548348, "dur": 104043, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881652397, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881652400, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881652450, "dur": 707, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881653159, "dur": 18416, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881671583, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881671586, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881671613, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881671616, "dur": 149064, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881820686, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881820689, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881820749, "dur": 42, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299881820793, "dur": 264848, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882085650, "dur": 16, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882085668, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882085731, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882085735, "dur": 127, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882085867, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882085871, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882085894, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882085897, "dur": 78048, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882163952, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882163955, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882163993, "dur": 29, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882164024, "dur": 6204, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882170236, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882170240, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882170266, "dur": 20, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882170288, "dur": 13781, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882184074, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882184077, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882184126, "dur": 16, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882184146, "dur": 1710, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882185862, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882185865, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882185914, "dur": 18, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882185933, "dur": 4554, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882190496, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882190499, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882190545, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882190549, "dur": 1305, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882191859, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882191861, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882191909, "dur": 37, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882191947, "dur": 38022, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882229977, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882229979, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882230016, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882230019, "dur": 982, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882231005, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882231007, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882231038, "dur": 20, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882231059, "dur": 447330, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882678396, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882678400, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882678445, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882678451, "dur": 1331, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882679788, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882679792, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882679847, "dur": 30, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882679879, "dur": 565, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882680450, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 34359738368, "ts": 1753299882680474, "dur": 11220, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21312, "tid": 8739, "ts": 1753299882692448, "dur": 1132, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21312, "tid": 30064771072, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21312, "tid": 30064771072, "ts": 1753299881407122, "dur": 42882, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21312, "tid": 30064771072, "ts": 1753299881450006, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21312, "tid": 30064771072, "ts": 1753299881450007, "dur": 74, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21312, "tid": 8739, "ts": 1753299882693582, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21312, "tid": 25769803776, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21312, "tid": 25769803776, "ts": 1753299881403318, "dur": 1288426, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21312, "tid": 25769803776, "ts": 1753299881403409, "dur": 3678, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21312, "tid": 25769803776, "ts": 1753299882691748, "dur": 68, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21312, "tid": 25769803776, "ts": 1753299882691761, "dur": 18, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21312, "tid": 8739, "ts": 1753299882693589, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753299881418138, "dur": 1671, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753299881419818, "dur": 578, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753299881420509, "dur": 66, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753299881420575, "dur": 486, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753299881422153, "dur": 55, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FDF3153BFA104355.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753299881423158, "dur": 857, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753299881427873, "dur": 213, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753299881421081, "dur": 14944, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753299881436036, "dur": 1243862, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753299882679900, "dur": 377, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753299882680496, "dur": 1823, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753299881421115, "dur": 14930, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881436070, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881436215, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299881436211, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FA85EF908E5A33BF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753299881436290, "dur": 283, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881436579, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299881436578, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3AA9E1B89A839D24.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753299881436725, "dur": 318, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881437053, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299881437052, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753299881437232, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299881437231, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_E2D6CB2A6174372D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753299881437447, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881437675, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881437761, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881437881, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881438010, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881438268, "dur": 289, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881438694, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881440478, "dur": 551, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.Abstractions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299881439740, "dur": 1579, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881441319, "dur": 704, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881442023, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881444877, "dur": 731, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Editor\\SpriteSkin\\TransformExtensions.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753299881443265, "dur": 2344, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881445609, "dur": 1002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881446611, "dur": 1550, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881448161, "dur": 2381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881450542, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881451507, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881453143, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881454663, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881456137, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881457941, "dur": 1397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881459338, "dur": 1468, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881460807, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881462020, "dur": 908, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881462928, "dur": 564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881463492, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881463928, "dur": 173, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753299881464101, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881464302, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299881464512, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299881464262, "dur": 774, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753299881465037, "dur": 249, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881465293, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753299881465440, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881465500, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753299881465932, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299881466143, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299881465659, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753299881466520, "dur": 1647, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881468218, "dur": 67994, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881536214, "dur": 2353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753299881538568, "dur": 1043, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881539617, "dur": 2079, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753299881541697, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881541784, "dur": 1736, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753299881543521, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881545651, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299881545897, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299881543595, "dur": 2561, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753299881546160, "dur": 891, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881547051, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753299881547116, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881547232, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881547605, "dur": 112294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299881659902, "dur": 422940, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299882082961, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299881659901, "dur": 424055, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753299882085184, "dur": 228, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753299882085708, "dur": 84580, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753299882190224, "dur": 488154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299882190223, "dur": 488158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753299882678402, "dur": 1405, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881421312, "dur": 14970, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881436296, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881436284, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_7C296F75F94E46F6.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753299881436522, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881436664, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881436663, "dur": 199, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_41265C97D041BE6E.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753299881436863, "dur": 282, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881437181, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753299881437378, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881437462, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881437554, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881437617, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881437744, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881437865, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881437947, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881438010, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881438070, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881438146, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881438464, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881438525, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881438583, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881438683, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881438756, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881438812, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881438897, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881439039, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881439461, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881439631, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881439695, "dur": 241, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881439941, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881440004, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881440080, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881440301, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881440540, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881440600, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881440652, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881440706, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881440762, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881440990, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881441195, "dur": 230, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881441429, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881441491, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881441546, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881441610, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881441897, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881441987, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881442129, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881442196, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881442283, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881442345, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881442404, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881442495, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881442735, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881442788, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881443076, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881443335, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881443400, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881443660, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881443890, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881443948, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881444009, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881444110, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881444175, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881444257, "dur": 293, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881444557, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881444617, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881444922, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881445026, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881445131, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881445442, "dur": 326, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881446010, "dur": 226, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnityTestTimeoutException.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881446237, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881446305, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\ConditionalIgnoreAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881446359, "dur": 773, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestEnumerator.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881447134, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881447213, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityCombinatorialStrategy.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881447319, "dur": 585, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnitySetUpAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881447905, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTearDownAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881447981, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTestAttribute.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881448036, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\BaseDelegator.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881448187, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableApplyChangesToContextCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881448245, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRepeatedTestCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881448296, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRetryTestCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881448351, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableSetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881448471, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881448536, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestState.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881448587, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\ImmediateEnumerableCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881448642, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881448753, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\TestActionCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881448904, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ConstructDelegator.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881448960, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\AssemblyNameFilter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881449012, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\CategoryFilterExtended.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881449167, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\FullNameFilter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881449229, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IAsyncTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881449293, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881449585, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\FailCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881449641, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\IEnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881449704, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881449848, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\RestoreTestContextAfterDomainReload.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881449970, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityLogCheckDelegatingCommand.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881450021, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestAssemblyRunner.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881450075, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestExecutionContext.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881450176, "dur": 205, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItemDataHolder.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881450382, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\WorkItemFactory.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881450549, "dur": 304, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\UnityTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881451000, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\ITestRunnerListener.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881451149, "dur": 327, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Messages\\IEditModeTestYieldInstruction.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881451478, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsController.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881451541, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsControllerSettings.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881451603, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\IRemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881451756, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\PlayerConnectionMessageIds.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881451961, "dur": 411, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataWithTestData.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881452373, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RuntimeTestRunnerFilter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881452430, "dur": 290, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\SynchronousFilter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881452789, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881452981, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyWrapper.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881453045, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881453099, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyWrapper.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881453201, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\PlayerTestAssemblyProvider.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881453404, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AttributeHelper.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881453520, "dur": 426, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ColorEqualityComparer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881453947, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\CoroutineRunner.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881454250, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\IMonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881454516, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\QuaternionEqualityComparer.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881454640, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\StacktraceFilter.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881454742, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackListener.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881455075, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector4ComparerWithEqualsOperator.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753299881437283, "dur": 18009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753299881455292, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881455619, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881455739, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753299881455970, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881456089, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881456228, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881456281, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881456383, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881456532, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881456671, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881457046, "dur": 295, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881457442, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881457549, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881457686, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881457833, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881457985, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881458049, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881458112, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881458248, "dur": 254, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881458503, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881458636, "dur": 257, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881458895, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881459253, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881459538, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881459676, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881460080, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881460303, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881460432, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881460486, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881460589, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881460876, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881461104, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881461233, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881461292, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881461572, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881461666, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881461852, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881461965, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881462023, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881462140, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881462339, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881462536, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881462640, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881462715, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881462780, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881462925, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881462986, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881455836, "dur": 7488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753299881463325, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881463469, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753299881463552, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753299881463774, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881463901, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753299881464033, "dur": 435, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881464473, "dur": 534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753299881465008, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881465163, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753299881465339, "dur": 508, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753299881465848, "dur": 833, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881466691, "dur": 727, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1753299881467419, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881467477, "dur": 177, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881468274, "dur": 61971, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 2, "ts": 1753299881536211, "dur": 2429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753299881538642, "dur": 830, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881540938, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881541816, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881542120, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881539478, "dur": 2884, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753299881542362, "dur": 303, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881543601, "dur": 174, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881543807, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881544281, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-process-l1-1-0.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881544635, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Server.IISIntegration.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881545122, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881545822, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753299881542673, "dur": 4035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753299881546709, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881546985, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881547068, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Editor.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753299881547144, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881547218, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881547301, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881547364, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881547513, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881547574, "dur": 533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881548111, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753299881548186, "dur": 1131755, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881421268, "dur": 14917, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881436278, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753299881436271, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_939D9295B43D54DB.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753299881436375, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A1665A0073A9D8C8.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753299881436426, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881436555, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753299881436554, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_D0034867E905C483.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753299881436711, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881436916, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881437148, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881437246, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881437369, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753299881437566, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753299881437700, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881437879, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881437953, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881438132, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881438223, "dur": 338, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881438597, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881438682, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881438749, "dur": 2041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881440790, "dur": 1760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881442550, "dur": 2307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881444857, "dur": 625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881445482, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881446714, "dur": 2592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881449306, "dur": 2517, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881451824, "dur": 1959, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881453783, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881454898, "dur": 1549, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881456448, "dur": 1829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881458278, "dur": 1539, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881459817, "dur": 1449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881461267, "dur": 758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881462026, "dur": 871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881462897, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881463478, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881463923, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753299881464352, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881464420, "dur": 900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753299881465320, "dur": 767, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881466123, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753299881466363, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881466663, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753299881466428, "dur": 800, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753299881467228, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881467445, "dur": 190, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881467636, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753299881467754, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881467811, "dur": 436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753299881468248, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881468393, "dur": 67817, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881536228, "dur": 2002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753299881538231, "dur": 2102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881542352, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753299881540336, "dur": 2778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753299881543115, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881543844, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753299881545248, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753299881545899, "dur": 473, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753299881546948, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753299881543238, "dur": 3906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753299881547145, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881547392, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753299881547610, "dur": 1132290, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881421209, "dur": 14860, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881436087, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881436178, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1753299881436076, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_C0FCC60981132629.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753299881436249, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881436367, "dur": 276, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881436364, "dur": 281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_F782C228CEDFF0CD.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753299881436901, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3F15606BE1109C0F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753299881436959, "dur": 319, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881436958, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753299881437281, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881437371, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753299881437438, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753299881437525, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881437868, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881437956, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881438083, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881438152, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881438208, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881438305, "dur": 296, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881438608, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881438667, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881438765, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881438908, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881439006, "dur": 282, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881439289, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881439378, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881439435, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881439688, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881439927, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881440112, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881440187, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881440345, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881440397, "dur": 239, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881440637, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881440699, "dur": 329, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881441044, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881441144, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881441340, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881441598, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881441649, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881441920, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881442080, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881442222, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881442286, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881442453, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881442553, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881442608, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881442659, "dur": 457, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881443122, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881443180, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881443597, "dur": 163, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881443787, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881443890, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881444363, "dur": 279, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881444643, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881444707, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881444759, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881444820, "dur": 280, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881445107, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881445172, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881445226, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881445277, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881445381, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881445491, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881445723, "dur": 746, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881446470, "dur": 489, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventSystem.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881446960, "dur": 304, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTrigger.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881447265, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTriggerType.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881447326, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\ExecuteEvents.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881447384, "dur": 468, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881447853, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInputModule.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881447980, "dur": 228, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\PointerInputModule.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881448209, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\StandaloneInputModule.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881448268, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\TouchInputModule.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881448325, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\MoveDirection.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881448465, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycasterManager.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881448573, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\Physics2DRaycaster.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881448716, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelEventHandler.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881448829, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelRaycaster.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881448883, "dur": 331, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881449288, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\CanvasUpdateRegistry.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881449434, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ColorBlock.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881449489, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\ClipperRegistry.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881449686, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881449850, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\DefaultControls.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881449967, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Dropdown.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881450107, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontData.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881450276, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRaycaster.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881450511, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRebuildTracker.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881450718, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRegistry.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881450815, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Image.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881450958, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\InputField.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881451370, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\VerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881451585, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Mask.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881451693, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskUtilities.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881451750, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaterialModifiers\\IMaterialModifier.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881452044, "dur": 266, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Slider.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881452311, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpecializedCollections\\IndexedSet.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881452519, "dur": 688, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Toggle.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881453232, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\ReflectionMethodsCache.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881453477, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\IMeshModifier.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753299881437624, "dur": 16136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753299881453761, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881453875, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881455115, "dur": 1729, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881456845, "dur": 1822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881458668, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881459792, "dur": 1336, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881461129, "dur": 1671, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881462801, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881463453, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881463905, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753299881464135, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881464193, "dur": 865, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753299881465059, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881465291, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753299881465600, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881466125, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881465664, "dur": 709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753299881466374, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881466548, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881466719, "dur": 928, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881467647, "dur": 68601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881536401, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881538357, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\WindowsBase.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881538585, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881536250, "dur": 3122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753299881539373, "dur": 2368, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881542855, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881543115, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881544126, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881544492, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881544932, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881541750, "dur": 4125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753299881545876, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881546038, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881546234, "dur": 703, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881546948, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881547138, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881547261, "dur": 176, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753299881547441, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881547593, "dur": 587, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753299881548180, "dur": 1131726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881421359, "dur": 14909, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881436298, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753299881436277, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7F06B267F1BFA456.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753299881436465, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753299881436464, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_116A6FADE34C1D2F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753299881436571, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881436646, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753299881436645, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D0DC9EA7252E13B4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753299881436706, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881436811, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753299881436809, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_51DC255B330D5605.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753299881436938, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881437392, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753299881437577, "dur": 146, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753299881437730, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881437832, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881437947, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881438424, "dur": 185, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881438636, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881438699, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881440294, "dur": 426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881440721, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881442153, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881443294, "dur": 1738, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881447099, "dur": 801, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\EditablePath\\Snapping.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753299881445032, "dur": 3367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881448400, "dur": 2151, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881450551, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881453605, "dur": 668, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Time\\Cooldown.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753299881452495, "dur": 1974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881454469, "dur": 1616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881456086, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881457745, "dur": 1351, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881459097, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881460390, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881462111, "dur": 1004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881463115, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881463482, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881463946, "dur": 465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753299881464412, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881465197, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Editor\\PlasticSCM\\UI\\Avatar\\AvatarImages.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753299881464497, "dur": 1354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753299881465851, "dur": 594, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881466451, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.PlasticSCM.Editor.ref.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753299881466450, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.PlasticSCM.Editor.ref.dll_257AEB342BE77856.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753299881466511, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881466693, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753299881466862, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881466931, "dur": 462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753299881467393, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881467492, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881467635, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753299881467751, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753299881468059, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881468399, "dur": 69088, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881539871, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753299881537488, "dur": 2513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753299881540002, "dur": 383, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881540407, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753299881540393, "dur": 3580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753299881543974, "dur": 696, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881544825, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753299881545220, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753299881545897, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-multibyte-l1-1-0.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753299881546950, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753299881544677, "dur": 3059, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753299881547737, "dur": 452, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881548200, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753299881548254, "dur": 1131685, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881421184, "dur": 14877, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881436071, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881436312, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881436295, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3AC0D0C527F5439B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753299881436445, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881436612, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881436610, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_D441A9CFEC32007A.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753299881436683, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881436741, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881436740, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0F9F7C5D1B98DB2C.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753299881436865, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881436851, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_31272CE89BD91E91.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753299881436923, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881437180, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881437261, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881437327, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881437417, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881437504, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881437558, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1753299881437643, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881437844, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881438040, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881438198, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881438278, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881438372, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881438594, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5521740788080646575.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753299881438753, "dur": 1401, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881440155, "dur": 1472, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881441628, "dur": 2116, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881444301, "dur": 530, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Editor\\SkinningModule\\SkeletonTool\\SkeletonTool.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753299881446406, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Editor\\SkinningModule\\Selection\\SkeletonSelection.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753299881443744, "dur": 3297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881447354, "dur": 567, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\ViewModels\\InspectorViewModel.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753299881447041, "dur": 2837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881449879, "dur": 2857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881452736, "dur": 1970, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881454706, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881456269, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881457422, "dur": 898, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881458320, "dur": 1089, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881459410, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881460709, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881461685, "dur": 891, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881462672, "dur": 62, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881462734, "dur": 749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881463483, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881463919, "dur": 363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753299881464283, "dur": 1695, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881465988, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753299881466617, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881466786, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753299881466877, "dur": 311, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881467193, "dur": 1146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753299881468339, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881468475, "dur": 67759, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881536235, "dur": 2285, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753299881538521, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881538628, "dur": 1650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753299881540278, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881540546, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881540692, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881540877, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881541096, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881541864, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881542149, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-errorhandling-l1-1-0.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881542431, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Cryptography.KeyDerivation.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881542690, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Protocols.Json.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881544246, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881544458, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881540411, "dur": 4310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753299881544722, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881545898, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753299881544841, "dur": 2749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753299881547591, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881547757, "dur": 340, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753299881548105, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753299881548228, "dur": 1131696, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881421230, "dur": 14847, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881436094, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881436175, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1753299881436084, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A3A3243B74248B90.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753299881436230, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881436328, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881436326, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_B5C2173E9AD0B5D6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753299881436442, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881436618, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881436617, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_E5A3FD65734E3273.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753299881436679, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881436735, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881436733, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.XRModule.dll_6B34E6EF9E430549.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753299881436797, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881436855, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881436854, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_64C5CD55CF2E68A6.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753299881436909, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881437061, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881437223, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881437222, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_62273FC84B3E0F2D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753299881437293, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881437418, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881437660, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881437779, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881437870, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881437928, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881438106, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881438330, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881438548, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881438710, "dur": 1646, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881440357, "dur": 1570, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881441928, "dur": 2609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881444538, "dur": 621, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881445203, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\LightBatchingDebugger\\LightBatchingDebugger.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753299881445159, "dur": 1509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881447114, "dur": 756, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Data\\FieldDependency.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753299881446669, "dur": 3696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881450365, "dur": 1626, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881451991, "dur": 2025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881454016, "dur": 2649, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881456665, "dur": 1307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881457972, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881459264, "dur": 984, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881460248, "dur": 1488, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881461736, "dur": 1117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881462853, "dur": 616, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881463470, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881463908, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753299881464179, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881464133, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753299881465123, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881465282, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881465554, "dur": 666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753299881466221, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881466487, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753299881466602, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881466669, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881466725, "dur": 921, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881467647, "dur": 68592, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881536242, "dur": 2912, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753299881539155, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881540826, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Diagnostics.HealthChecks.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881541165, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881541740, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Json.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881539292, "dur": 3722, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753299881543018, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881543253, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881543410, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881545066, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Caching.Memory.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881543225, "dur": 3665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753299881546891, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881547138, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881547204, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881547318, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881547381, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881547431, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753299881547569, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753299881547664, "dur": 1132238, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881421257, "dur": 14883, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881436192, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881436179, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DE98305BD6D68C77.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753299881436298, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881436413, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881436411, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3B0CE8B1A0A2F90C.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753299881436527, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881436602, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881436600, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_277964AD70D626E0.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753299881436663, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881436724, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881436722, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E81E4251EABAE467.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753299881436836, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881436835, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_46FFF6FD196ECAB7.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753299881436901, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881437049, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881437048, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753299881437124, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881437273, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881437336, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881437405, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881437535, "dur": 117, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 8, "ts": 1753299881437654, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881437757, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881437856, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881437924, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881437980, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881438493, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881438660, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881440235, "dur": 769, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Hosting.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881438766, "dur": 2707, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881441474, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881442949, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881444414, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881447110, "dur": 812, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Plugin\\Changelogs\\Changelog_1_0_4.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753299881445537, "dur": 2766, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881448303, "dur": 2263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881450567, "dur": 2774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881453341, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881454644, "dur": 1612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881456256, "dur": 1045, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881457302, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881458443, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881460061, "dur": 1124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881461185, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881462239, "dur": 966, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881463206, "dur": 265, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881463472, "dur": 434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881463907, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753299881464178, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881464136, "dur": 799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753299881464936, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881465123, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881465180, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753299881465415, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881465882, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881466126, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881465482, "dur": 765, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753299881466248, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881466480, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881466543, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881466698, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881466768, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881467645, "dur": 2808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881470454, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753299881470557, "dur": 68036, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881540418, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881541057, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881538595, "dur": 2755, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753299881541351, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881542637, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881542874, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881543563, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.TagHelpers.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881544636, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881545310, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753299881541623, "dur": 3792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753299881545415, "dur": 565, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881546022, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881546159, "dur": 680, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881546848, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881546915, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881546979, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881547093, "dur": 175, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1753299881547272, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881547330, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881547450, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881547508, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753299881547660, "dur": 1132227, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881421302, "dur": 14910, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881436386, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881436385, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_85F6C15D73A578BD.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753299881436514, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881436662, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881436661, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAssetBundleModule.dll_EC1D9C7602E10CB1.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753299881436777, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881436952, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881436951, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753299881437483, "dur": 65, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753299881437681, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881437942, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881438065, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881438154, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881438548, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881438617, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/16942202937359522267.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753299881438734, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881438881, "dur": 2112, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881440994, "dur": 1985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881442979, "dur": 1360, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881445321, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\ShaderGraph\\UniversalProperties.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753299881444340, "dur": 1856, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881446197, "dur": 2190, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881448388, "dur": 2069, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881451100, "dur": 589, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Editor\\BurstDisassembler.Core.ARM64.info.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753299881450458, "dur": 2063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881453789, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector4\\Vector4Distance.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753299881452521, "dur": 1783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881454304, "dur": 1829, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881456134, "dur": 1887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881458022, "dur": 762, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881458784, "dur": 1588, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881460372, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881462031, "dur": 935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881462966, "dur": 469, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881463438, "dur": 473, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881463920, "dur": 320, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753299881464241, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881464323, "dur": 518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753299881464842, "dur": 929, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881465776, "dur": 557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753299881466333, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881466427, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881466503, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipelines.Universal.Shaders.ref.dll_9468B054363B0720.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753299881466751, "dur": 887, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881467638, "dur": 1872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881469511, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753299881469598, "dur": 234, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753299881469832, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881469887, "dur": 66346, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881536243, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881536314, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881536430, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881537119, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881537173, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881537449, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881537619, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881537943, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881538675, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881540202, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881540530, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881536234, "dur": 4738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753299881540973, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881542399, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\aspnetcorev2_inprocess.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881543414, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Text.Json.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881541108, "dur": 3669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753299881544778, "dur": 1613, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881546407, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881546720, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881546862, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881547097, "dur": 368, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753299881547468, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753299881547616, "dur": 1132294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881421351, "dur": 14995, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881436360, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753299881436347, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_FA135A462A75797F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753299881436697, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_8837211AE3CE1746.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753299881436856, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753299881436855, "dur": 237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_9AAEFD6A746FD785.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753299881437169, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881437269, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881437345, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1753299881437536, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881437680, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881437781, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881438035, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881438099, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881438230, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881438296, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881438634, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6210570011482043853.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753299881438734, "dur": 1890, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881440624, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881442075, "dur": 1374, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881443449, "dur": 2341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881446445, "dur": 528, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\SetVariableUnitOption.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753299881445790, "dur": 2395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881448186, "dur": 1872, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881450058, "dur": 1506, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881451564, "dur": 1648, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881453212, "dur": 1837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881455050, "dur": 1300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881456350, "dur": 1200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881457550, "dur": 1521, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881459072, "dur": 1682, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881460755, "dur": 1092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881461847, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881462735, "dur": 694, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881463471, "dur": 649, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881464121, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753299881464369, "dur": 726, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881465099, "dur": 501, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753299881465600, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881466141, "dur": 292, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753299881465834, "dur": 1087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753299881466922, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881467107, "dur": 530, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881467637, "dur": 880, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881468518, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753299881468619, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881468677, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753299881469315, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881469523, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753299881469677, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753299881470053, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881470216, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753299881470368, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881470450, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753299881470537, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753299881470835, "dur": 181607, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753299881660204, "dur": 11028, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753299881659895, "dur": 11401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753299881671491, "dur": 56, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299881671563, "dur": 149152, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753299881824855, "dur": 60755, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753299881885611, "dur": 197197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753299881824854, "dur": 259111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753299882085596, "dur": 290, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753299882085900, "dur": 78074, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753299882183954, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1753299882183953, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1753299882184100, "dur": 1798, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 10, "ts": 1753299882185902, "dur": 494028, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881421374, "dur": 14903, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881436299, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753299881436279, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4012322E9A5614BC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753299881436403, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753299881436402, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_0DA2B5FA22101DBE.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753299881436595, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753299881436594, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_F44FCBA18E1E2303.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753299881436737, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753299881436736, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_065B772FA1729652.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753299881436854, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881437128, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881437211, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881437271, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881437329, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881437404, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881437465, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881437578, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881437955, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881438139, "dur": 257, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881438437, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881438498, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11344994280883157806.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753299881438581, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881438675, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881438732, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881440411, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\SettingsProvider\\EditorPreferencesProvider.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753299881440321, "dur": 1746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881442067, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881444066, "dur": 677, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Editor\\SpriteLib\\SpriteLibraryAssetInspector.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753299881443309, "dur": 2580, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881445948, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Control\\SelectOnEnumDescriptor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753299881445890, "dur": 2124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881448015, "dur": 1678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881449693, "dur": 1672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881452610, "dur": 675, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\ForwardRendererData.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753299881451365, "dur": 2248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881453613, "dur": 1938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881455551, "dur": 1166, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881456718, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881458019, "dur": 1701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881459720, "dur": 1616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881461336, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881462327, "dur": 769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881463096, "dur": 363, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881463461, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881463913, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753299881464027, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881464478, "dur": 610, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753299881465196, "dur": 359, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753299881464101, "dur": 1636, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753299881465737, "dur": 618, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881466367, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881466472, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881466538, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881466598, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881466722, "dur": 933, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881467657, "dur": 68559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881537527, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753299881536217, "dur": 2506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753299881538723, "dur": 1616, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881541199, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Components.Web.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753299881541555, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753299881540345, "dur": 2661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753299881543006, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881543086, "dur": 2248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753299881545335, "dur": 1308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881546652, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881546717, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881546786, "dur": 672, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881547598, "dur": 705, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753299881548345, "dur": 1131583, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881421411, "dur": 14966, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881436386, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753299881436378, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D218FBB45FD12FE4.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753299881436449, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881436576, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753299881436574, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_758CA54D648AA1BC.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753299881436640, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881436713, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753299881436712, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_B8A7170076CF7080.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753299881436829, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_D3933C87E9EDE72F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753299881436881, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881436944, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753299881436943, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7880F7755F74374F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753299881437124, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881437251, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753299881437309, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881437555, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1753299881437634, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753299881438038, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881438495, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881440477, "dur": 677, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753299881438722, "dur": 2611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881441334, "dur": 994, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881443617, "dur": 601, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.aseprite@1.1.4\\Editor\\AssetGeneration\\PrefabGeneration.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753299881442328, "dur": 2605, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881444933, "dur": 1647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881446580, "dur": 1655, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881448235, "dur": 1883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881450118, "dur": 6201, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881456320, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881457589, "dur": 1365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881458955, "dur": 1287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881460243, "dur": 1088, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881461331, "dur": 865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881462196, "dur": 945, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881463142, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881463479, "dur": 640, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881464121, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753299881464334, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881464479, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753299881464413, "dur": 429, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753299881464842, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881464955, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753299881465520, "dur": 336, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881465868, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881465934, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881465989, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753299881466234, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881466394, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753299881467393, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881467792, "dur": 68423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881537617, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753299881536216, "dur": 2332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753299881538549, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881538956, "dur": 889, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753299881540095, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753299881540226, "dur": 1344, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753299881538670, "dur": 4492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753299881543163, "dur": 1493, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881546947, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753299881544661, "dur": 2367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753299881547034, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881547466, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753299881547650, "dur": 1132254, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881421437, "dur": 14899, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881436342, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_CBFD31FC7A6EE8BC.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753299881436401, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881436476, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753299881436475, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F7F95DB68C78C481.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753299881436696, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753299881436695, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_1CE9B2E479BEC5CA.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753299881436942, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881437192, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881437287, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881437402, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753299881437615, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753299881437679, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881437872, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881438064, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881438200, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881438581, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881438719, "dur": 1706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881440425, "dur": 1585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881442010, "dur": 1160, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881443170, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881444587, "dur": 795, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\Deprecated.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753299881444439, "dur": 1490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881446287, "dur": 705, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\FlowCanvas.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753299881445929, "dur": 2359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881448288, "dur": 2248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881450536, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881451944, "dur": 1478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881453423, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881454561, "dur": 1678, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881456239, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881457801, "dur": 1814, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881459616, "dur": 1578, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881461195, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881462698, "dur": 745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881463443, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881463902, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753299881464110, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753299881464209, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753299881464082, "dur": 887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753299881464970, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881465120, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753299881465539, "dur": 357, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753299881466012, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753299881465300, "dur": 1051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753299881466352, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881466599, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753299881466901, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753299881466734, "dur": 712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753299881467446, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881467620, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881467696, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753299881467887, "dur": 484, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753299881468372, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881468527, "dur": 67680, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881536210, "dur": 2146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753299881538357, "dur": 1896, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881540259, "dur": 2074, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753299881542334, "dur": 3389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881546949, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753299881547625, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753299881547763, "dur": 287, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753299881545730, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753299881548093, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753299881548207, "dur": 1131710, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881421453, "dur": 14823, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881436287, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299881436277, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_38FFBE07FFE62337.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753299881436718, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299881436717, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_609DF0C9F4BC7F1C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753299881436849, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299881436848, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F72D29FA518CE2F1.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753299881436940, "dur": 208, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881437166, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881437262, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881437559, "dur": 107, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 14, "ts": 1753299881437883, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881438191, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881438597, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881438695, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881438774, "dur": 1531, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881440514, "dur": 689, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\Messaging\\Message.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753299881440306, "dur": 2341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881442648, "dur": 1131, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881444414, "dur": 699, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Editor\\SkinningModule\\OutlineGenerator\\IOutlineGenerator.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753299881443780, "dur": 2364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881446425, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Interface\\IConditional.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753299881446144, "dur": 2478, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881448622, "dur": 1838, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881450461, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881452885, "dur": 670, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Runtime\\BufferManager.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753299881451899, "dur": 2085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881453984, "dur": 1259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881455244, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881456191, "dur": 1140, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881457332, "dur": 2041, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881459374, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881460672, "dur": 1680, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881462352, "dur": 693, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881463045, "dur": 429, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881463474, "dur": 431, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881463906, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753299881464061, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881464303, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299881464241, "dur": 592, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753299881464834, "dur": 680, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881465576, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753299881465770, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753299881465956, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881466052, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753299881466455, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881466554, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753299881466721, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753299881467079, "dur": 408, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881467492, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881467549, "dur": 88, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881467637, "dur": 1359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881468997, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753299881469103, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881469186, "dur": 330, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753299881469517, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881469720, "dur": 66516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881536708, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299881536896, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299881537304, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299881537454, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299881537738, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299881538955, "dur": 890, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299881536237, "dur": 4625, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753299881540863, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881540971, "dur": 1975, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753299881542947, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881543015, "dur": 1580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753299881544595, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881544693, "dur": 2097, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753299881546790, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881546918, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881546980, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881547056, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Psdimporter.Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299881547161, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881547315, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299881547607, "dur": 636354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753299882183964, "dur": 46016, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299882183963, "dur": 46019, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299882229999, "dur": 1056, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753299882231059, "dur": 448836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881421525, "dur": 14759, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881436292, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753299881436286, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_826CA845B2B614E1.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753299881436449, "dur": 256, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881436750, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881436891, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5AAB6A659692F205.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753299881436942, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881437117, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753299881437115, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_882D400E4D49662A.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753299881437174, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881437352, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881437569, "dur": 144, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753299881437715, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881437826, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881437923, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881438286, "dur": 341, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881438717, "dur": 1790, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881440507, "dur": 760, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881442367, "dur": 777, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\CustomEditors\\CustomTimelineEditorCache.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753299881441267, "dur": 2253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881443520, "dur": 1792, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881445313, "dur": 2136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881447449, "dur": 2507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881449956, "dur": 1763, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881451719, "dur": 2211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881453931, "dur": 1783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881455719, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881455778, "dur": 2285, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881458063, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881459156, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881460719, "dur": 1164, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881461883, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881462809, "dur": 618, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881463475, "dur": 423, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881463899, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753299881464111, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753299881464209, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753299881464387, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753299881464060, "dur": 1048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753299881465109, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881465266, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881465362, "dur": 266, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753299881466008, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753299881465667, "dur": 700, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753299881466368, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881466545, "dur": 155, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753299881467037, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753299881467132, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753299881466733, "dur": 687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753299881467421, "dur": 313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881467796, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753299881467978, "dur": 439, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753299881468418, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881468573, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881468633, "dur": 67596, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881537818, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753299881536236, "dur": 2311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753299881538547, "dur": 566, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881539120, "dur": 2010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753299881541130, "dur": 1155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881542291, "dur": 1887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753299881544178, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881544246, "dur": 2048, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753299881546298, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881546551, "dur": 351, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881547046, "dur": 564, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753299881547613, "dur": 1132278, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881421528, "dur": 14737, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881436284, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753299881436274, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_71CF6729598465FE.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753299881436373, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881436433, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_A964AEB46C39AB32.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753299881436609, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881436691, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753299881436690, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FDF3153BFA104355.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753299881436769, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881436902, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FDF3153BFA104355.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753299881436965, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753299881436964, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1B6116B393E72286.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753299881437489, "dur": 115, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753299881437697, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881437990, "dur": 569, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881438598, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881438741, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881439814, "dur": 920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881442340, "dur": 610, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Recording\\TimelineRecording.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753299881443004, "dur": 679, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753299881440734, "dur": 3208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881445247, "dur": 553, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Editor\\SkinningModule\\Cache\\BaseObject.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753299881443943, "dur": 1857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881447289, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Control\\SelectOnStringDescriptor.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753299881445800, "dur": 2175, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881447975, "dur": 2002, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881449978, "dur": 1991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881451969, "dur": 2055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881454025, "dur": 1894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881455920, "dur": 1098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881457019, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881458301, "dur": 1312, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881459614, "dur": 859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881460473, "dur": 1424, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881461946, "dur": 623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881462635, "dur": 58, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881462693, "dur": 793, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881463487, "dur": 464, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881463957, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753299881464197, "dur": 244, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753299881464442, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881464618, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753299881464501, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753299881465022, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881465220, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753299881465539, "dur": 357, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753299881465982, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753299881465370, "dur": 1206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753299881466576, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881466979, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881467034, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753299881467194, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881467253, "dur": 1551, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753299881468804, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881468913, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753299881468991, "dur": 426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753299881469417, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881469506, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753299881469587, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753299881469797, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881469890, "dur": 66336, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881536333, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753299881537526, "dur": 361, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753299881537972, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753299881536228, "dur": 3455, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753299881539683, "dur": 643, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881540334, "dur": 2265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753299881542600, "dur": 3553, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881546166, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881546785, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881546925, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881546997, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881547070, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Animation.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753299881547166, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881547337, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881547595, "dur": 605, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881548203, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753299881548263, "dur": 1131656, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881421583, "dur": 14757, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881436348, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753299881436340, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6FDD4B7A6EC84268.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753299881436468, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753299881436467, "dur": 175, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_39D3561411FEB126.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753299881436642, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881436778, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881436923, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753299881436922, "dur": 161, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_C8504B54661C208D.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753299881437336, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881437550, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753299881437709, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881437937, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881438467, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881438729, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881440228, "dur": 834, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-heap-l1-1-0.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753299881439980, "dur": 1913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881441893, "dur": 2303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881444196, "dur": 1912, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881446109, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881446932, "dur": 896, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\Slots\\BooleanSlotControlView.cs"}}, {"pid": 12345, "tid": 17, "ts": 1753299881446931, "dur": 2951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881449882, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881451682, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881453214, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881454572, "dur": 1448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881456020, "dur": 2136, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881458156, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881459577, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881460705, "dur": 1694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881462399, "dur": 384, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881462783, "dur": 658, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881463442, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881463910, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753299881464302, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753299881464479, "dur": 568, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753299881464257, "dur": 1720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753299881465978, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881466425, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881466480, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.Common.Path.Editor.ref.dll_011CCB039EE5F887.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753299881466712, "dur": 78, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881466791, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753299881466935, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881466990, "dur": 475, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753299881467466, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881467565, "dur": 1126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881468696, "dur": 67548, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881536843, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753299881537667, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753299881538947, "dur": 2673, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753299881536247, "dur": 5781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753299881542029, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881542150, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753299881544414, "dur": 384, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881545824, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753299881544803, "dur": 2279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753299881547083, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299881547530, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.Shaders.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1753299881547607, "dur": 642624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753299882190233, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1753299882190232, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1753299882190495, "dur": 1414, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 17, "ts": 1753299882191914, "dur": 487999, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881421623, "dur": 14686, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881436326, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753299881436311, "dur": 151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_4245840F5C4641AB.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753299881436462, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881436737, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881436930, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753299881436929, "dur": 163, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_9F8263619ADFDEFD.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753299881437134, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881437242, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881437420, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881437487, "dur": 222, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753299881437774, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881438215, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881438583, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881438685, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881438780, "dur": 1228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881440008, "dur": 801, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881440809, "dur": 1405, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881442456, "dur": 630, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PsdFile\\Compression\\ZipPredict16Image.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753299881442214, "dur": 2139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881444353, "dur": 880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881445233, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\StateUnit.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753299881446436, "dur": 508, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\OnEnterState.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753299881445233, "dur": 2658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881447891, "dur": 1958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881451469, "dur": 667, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Interfaces\\IMayRequireBitangent.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753299881449850, "dur": 3027, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881452877, "dur": 1722, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881454599, "dur": 1126, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881455767, "dur": 861, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881456629, "dur": 1764, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881458393, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881459697, "dur": 1269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881460967, "dur": 787, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881461754, "dur": 1139, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881462894, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881463487, "dur": 568, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881464060, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753299881464209, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881464409, "dur": 538, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753299881464947, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881465215, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753299881465365, "dur": 127, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753299881465493, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881465549, "dur": 699, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753299881466249, "dur": 496, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881466753, "dur": 860, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881467634, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753299881467797, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753299881468200, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881468347, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753299881468464, "dur": 404, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753299881468869, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881468986, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753299881469103, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753299881469344, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881469447, "dur": 66773, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881538458, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753299881538956, "dur": 892, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753299881536224, "dur": 3762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753299881539987, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881540679, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753299881540051, "dur": 2417, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753299881542468, "dur": 2640, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881546253, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753299881545113, "dur": 2185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753299881547298, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881547563, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753299881547617, "dur": 1132276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881421657, "dur": 14594, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881436252, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_49D0198E18ABFDE2.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753299881436310, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881436621, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753299881436620, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_0E723EAE4164004E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753299881436935, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881437099, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881437195, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881437413, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881437562, "dur": 78, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 19, "ts": 1753299881437680, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881437747, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881437970, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881438048, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881438217, "dur": 292, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881438544, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881438627, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881438697, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881438750, "dur": 1758, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881440509, "dur": 1614, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881442123, "dur": 1972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881445021, "dur": 800, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\VFXGraph\\VFXURPLitQuadStripOutput.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753299881445858, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\VFXGraph\\VFXURPBinder.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753299881444096, "dur": 2324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881446476, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\BuiltInFields.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753299881447286, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\TargetResources\\StructFields.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753299881446421, "dur": 2746, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881449167, "dur": 1689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881451576, "dur": 700, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Passes\\HDRDebugViewPass.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753299881450857, "dur": 1878, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881452735, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881453699, "dur": 1611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881455310, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881456876, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881458119, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881459482, "dur": 1370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881460852, "dur": 1606, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881462712, "dur": 768, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881463480, "dur": 458, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881463939, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753299881464557, "dur": 1385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881465946, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753299881466540, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881466670, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881466753, "dur": 886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881467640, "dur": 1886, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881469527, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753299881469645, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753299881469864, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881469955, "dur": 66269, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881536225, "dur": 2329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753299881538554, "dur": 483, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881539041, "dur": 2014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753299881541056, "dur": 2172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881543234, "dur": 2297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753299881545532, "dur": 1419, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881547005, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753299881547078, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.InternalAPIEditorBridge.001.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753299881547533, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.pdb"}}, {"pid": 12345, "tid": 19, "ts": 1753299881547619, "dur": 1132270, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881421689, "dur": 14563, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881436266, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881436253, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_77AE899CA5593464.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753299881436371, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881436462, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881436460, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E586C88FEB060A87.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753299881436555, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_55536420766141FA.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753299881436719, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881436718, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_387623EB1508B7EB.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753299881436811, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881436967, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881437245, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881437413, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881437521, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881437659, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881437720, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881437793, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881437852, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881438004, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881438499, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881438695, "dur": 1105, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881439800, "dur": 1796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881441597, "dur": 696, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881442294, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881443635, "dur": 1487, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881445122, "dur": 888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881446010, "dur": 1216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881447226, "dur": 1724, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881448951, "dur": 2067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881451018, "dur": 2056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881453075, "dur": 1225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881454301, "dur": 2146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881456447, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881458036, "dur": 1876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881459913, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881460952, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881462250, "dur": 938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881463188, "dur": 266, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881463454, "dur": 445, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881463901, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753299881464028, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881464384, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881464674, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881465081, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Converters\\fsArrayConverter.cs"}}, {"pid": 12345, "tid": 20, "ts": 1753299881465882, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Serialization\\ISerializationDependency.cs"}}, {"pid": 12345, "tid": 20, "ts": 1753299881464172, "dur": 2087, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753299881466260, "dur": 446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881466713, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881466780, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753299881466913, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881466970, "dur": 1303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753299881468274, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881468451, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881468511, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753299881468606, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881468681, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753299881468911, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881469011, "dur": 67220, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881536483, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881536601, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881536738, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881536940, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881537603, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881537884, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881538030, "dur": 290, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881538427, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881536232, "dur": 3943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753299881540176, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881540332, "dur": 1801, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753299881542133, "dur": 1339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881543477, "dur": 2293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753299881545771, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881547768, "dur": 287, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753299881545930, "dur": 2289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753299881548220, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753299881548335, "dur": 1131586, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753299882687452, "dur": 3873, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21312, "tid": 8739, "ts": 1753299882693657, "dur": 660, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21312, "tid": 8739, "ts": 1753299882694360, "dur": 45961, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21312, "tid": 8739, "ts": 1753299882692431, "dur": 47933, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}