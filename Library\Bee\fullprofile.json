{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21312, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21312, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21312, "tid": 8381, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21312, "tid": 8381, "ts": 1753279531396278, "dur": 12, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21312, "tid": 8381, "ts": 1753279531396301, "dur": 3, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21312, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21312, "tid": 1, "ts": 1753279526506787, "dur": 1970, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21312, "tid": 1, "ts": 1753279526508764, "dur": 22682, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21312, "tid": 1, "ts": 1753279526531448, "dur": 77341, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21312, "tid": 8381, "ts": 1753279531396306, "dur": 5, "ph": "X", "name": "", "args": {}}, {"pid": 21312, "tid": 47244640256, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526506731, "dur": 16183, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526522917, "dur": 4872872, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526522930, "dur": 118, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526523054, "dur": 229, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526523292, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526523295, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526523355, "dur": 9, "ph": "X", "name": "ProcessMessages 41", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526523366, "dur": 6229, "ph": "X", "name": "ReadAsync 41", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526529601, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526529603, "dur": 237, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526529846, "dur": 15, "ph": "X", "name": "ProcessMessages 20481", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526529862, "dur": 59, "ph": "X", "name": "ReadAsync 20481", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526529924, "dur": 2, "ph": "X", "name": "ProcessMessages 1708", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526529927, "dur": 31, "ph": "X", "name": "ReadAsync 1708", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526529961, "dur": 44, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530008, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530011, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530077, "dur": 2, "ph": "X", "name": "ProcessMessages 1621", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530081, "dur": 55, "ph": "X", "name": "ReadAsync 1621", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530140, "dur": 2, "ph": "X", "name": "ProcessMessages 1577", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530144, "dur": 55, "ph": "X", "name": "ReadAsync 1577", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530201, "dur": 1, "ph": "X", "name": "ProcessMessages 1773", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530203, "dur": 47, "ph": "X", "name": "ReadAsync 1773", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530253, "dur": 1, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530254, "dur": 47, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530304, "dur": 1, "ph": "X", "name": "ProcessMessages 1269", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530306, "dur": 45, "ph": "X", "name": "ReadAsync 1269", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530354, "dur": 1, "ph": "X", "name": "ProcessMessages 1298", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530356, "dur": 47, "ph": "X", "name": "ReadAsync 1298", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530405, "dur": 1, "ph": "X", "name": "ProcessMessages 1211", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530407, "dur": 109, "ph": "X", "name": "ReadAsync 1211", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530520, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530523, "dur": 66, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530592, "dur": 2, "ph": "X", "name": "ProcessMessages 1146", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530595, "dur": 62, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530661, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530663, "dur": 42, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530709, "dur": 51, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530763, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530766, "dur": 50, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530818, "dur": 1, "ph": "X", "name": "ProcessMessages 1284", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530820, "dur": 70, "ph": "X", "name": "ReadAsync 1284", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530895, "dur": 2, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530898, "dur": 70, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530972, "dur": 2, "ph": "X", "name": "ProcessMessages 1045", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526530975, "dur": 67, "ph": "X", "name": "ReadAsync 1045", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531045, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531071, "dur": 75, "ph": "X", "name": "ReadAsync 250", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531150, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531227, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531229, "dur": 24, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531256, "dur": 22, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531280, "dur": 20, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531303, "dur": 19, "ph": "X", "name": "ReadAsync 149", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531324, "dur": 22, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531349, "dur": 22, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531374, "dur": 18, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531394, "dur": 39, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531443, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531470, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531471, "dur": 25, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531499, "dur": 21, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531523, "dur": 21, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531545, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531547, "dur": 22, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531572, "dur": 20, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531596, "dur": 22, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531621, "dur": 21, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531645, "dur": 26, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531672, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531673, "dur": 20, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531696, "dur": 28, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531727, "dur": 24, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531753, "dur": 18, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531774, "dur": 20, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531798, "dur": 22, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531823, "dur": 19, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531845, "dur": 18, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531866, "dur": 65, "ph": "X", "name": "ReadAsync 289", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531932, "dur": 1, "ph": "X", "name": "ProcessMessages 1501", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526531934, "dur": 117, "ph": "X", "name": "ReadAsync 1501", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532055, "dur": 2, "ph": "X", "name": "ProcessMessages 594", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532058, "dur": 45, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532105, "dur": 2, "ph": "X", "name": "ProcessMessages 3148", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532108, "dur": 24, "ph": "X", "name": "ReadAsync 3148", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532134, "dur": 209, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532364, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532366, "dur": 150, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532520, "dur": 4, "ph": "X", "name": "ProcessMessages 5393", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532525, "dur": 35, "ph": "X", "name": "ReadAsync 5393", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532561, "dur": 1, "ph": "X", "name": "ProcessMessages 2219", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532563, "dur": 17, "ph": "X", "name": "ReadAsync 2219", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532582, "dur": 22, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532605, "dur": 7, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532613, "dur": 42, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532684, "dur": 1, "ph": "X", "name": "ProcessMessages 897", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532685, "dur": 29, "ph": "X", "name": "ReadAsync 897", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532727, "dur": 1, "ph": "X", "name": "ProcessMessages 1454", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532728, "dur": 83, "ph": "X", "name": "ReadAsync 1454", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532824, "dur": 1, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532825, "dur": 47, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532874, "dur": 2, "ph": "X", "name": "ProcessMessages 3007", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532881, "dur": 43, "ph": "X", "name": "ReadAsync 3007", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532927, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532929, "dur": 45, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526532976, "dur": 86, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533064, "dur": 1, "ph": "X", "name": "ProcessMessages 2178", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533066, "dur": 41, "ph": "X", "name": "ReadAsync 2178", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533109, "dur": 1, "ph": "X", "name": "ProcessMessages 1369", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533111, "dur": 22, "ph": "X", "name": "ReadAsync 1369", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533154, "dur": 25, "ph": "X", "name": "ReadAsync 946", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533182, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533252, "dur": 1, "ph": "X", "name": "ProcessMessages 948", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533253, "dur": 26, "ph": "X", "name": "ReadAsync 948", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533339, "dur": 1, "ph": "X", "name": "ProcessMessages 1445", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533341, "dur": 48, "ph": "X", "name": "ReadAsync 1445", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533390, "dur": 1, "ph": "X", "name": "ProcessMessages 2181", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533391, "dur": 23, "ph": "X", "name": "ReadAsync 2181", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533427, "dur": 1, "ph": "X", "name": "ProcessMessages 1139", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533428, "dur": 21, "ph": "X", "name": "ReadAsync 1139", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533451, "dur": 1, "ph": "X", "name": "ProcessMessages 824", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533452, "dur": 177, "ph": "X", "name": "ReadAsync 824", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533632, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533677, "dur": 1, "ph": "X", "name": "ProcessMessages 1042", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533678, "dur": 22, "ph": "X", "name": "ReadAsync 1042", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533715, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533722, "dur": 26, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533749, "dur": 1, "ph": "X", "name": "ProcessMessages 980", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533751, "dur": 20, "ph": "X", "name": "ReadAsync 980", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533786, "dur": 22, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533811, "dur": 29, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533843, "dur": 33, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533878, "dur": 18, "ph": "X", "name": "ReadAsync 998", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533898, "dur": 16, "ph": "X", "name": "ProcessMessages 286", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533914, "dur": 39, "ph": "X", "name": "ReadAsync 286", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533955, "dur": 13, "ph": "X", "name": "ProcessMessages 1793", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533970, "dur": 19, "ph": "X", "name": "ReadAsync 1793", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526533992, "dur": 39, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534033, "dur": 23, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534070, "dur": 1, "ph": "X", "name": "ProcessMessages 1025", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534072, "dur": 19, "ph": "X", "name": "ReadAsync 1025", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534093, "dur": 32, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534127, "dur": 39, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534168, "dur": 1, "ph": "X", "name": "ProcessMessages 1403", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534169, "dur": 23, "ph": "X", "name": "ReadAsync 1403", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534195, "dur": 20, "ph": "X", "name": "ReadAsync 689", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534226, "dur": 28, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534267, "dur": 1, "ph": "X", "name": "ProcessMessages 900", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534268, "dur": 54, "ph": "X", "name": "ReadAsync 900", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534325, "dur": 21, "ph": "X", "name": "ReadAsync 1147", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534370, "dur": 1, "ph": "X", "name": "ProcessMessages 798", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534371, "dur": 35, "ph": "X", "name": "ReadAsync 798", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534408, "dur": 1, "ph": "X", "name": "ProcessMessages 1434", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534410, "dur": 44, "ph": "X", "name": "ReadAsync 1434", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534456, "dur": 21, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534479, "dur": 13, "ph": "X", "name": "ProcessMessages 846", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534493, "dur": 47, "ph": "X", "name": "ReadAsync 846", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534541, "dur": 1, "ph": "X", "name": "ProcessMessages 1566", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534543, "dur": 34, "ph": "X", "name": "ReadAsync 1566", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534578, "dur": 1, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534580, "dur": 33, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534616, "dur": 23, "ph": "X", "name": "ReadAsync 723", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534645, "dur": 35, "ph": "X", "name": "ReadAsync 551", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534683, "dur": 38, "ph": "X", "name": "ReadAsync 821", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534723, "dur": 1, "ph": "X", "name": "ProcessMessages 1235", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534724, "dur": 37, "ph": "X", "name": "ReadAsync 1235", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534764, "dur": 47, "ph": "X", "name": "ReadAsync 940", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534812, "dur": 1, "ph": "X", "name": "ProcessMessages 942", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534814, "dur": 25, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534842, "dur": 47, "ph": "X", "name": "ReadAsync 1111", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534892, "dur": 1, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534894, "dur": 28, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534923, "dur": 1, "ph": "X", "name": "ProcessMessages 1359", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534924, "dur": 27, "ph": "X", "name": "ReadAsync 1359", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534957, "dur": 20, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526534980, "dur": 34, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535020, "dur": 1, "ph": "X", "name": "ProcessMessages 670", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535022, "dur": 22, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535046, "dur": 36, "ph": "X", "name": "ReadAsync 864", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535084, "dur": 1, "ph": "X", "name": "ProcessMessages 573", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535085, "dur": 23, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535122, "dur": 31, "ph": "X", "name": "ReadAsync 886", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535158, "dur": 1, "ph": "X", "name": "ProcessMessages 1133", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535159, "dur": 15, "ph": "X", "name": "ReadAsync 1133", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535177, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535211, "dur": 21, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535235, "dur": 43, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535280, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535322, "dur": 1, "ph": "X", "name": "ProcessMessages 1665", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535324, "dur": 20, "ph": "X", "name": "ReadAsync 1665", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535352, "dur": 20, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535375, "dur": 43, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535440, "dur": 1, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535441, "dur": 21, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535464, "dur": 5, "ph": "X", "name": "ProcessMessages 918", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535470, "dur": 24, "ph": "X", "name": "ReadAsync 918", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535496, "dur": 19, "ph": "X", "name": "ProcessMessages 655", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535516, "dur": 28, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535545, "dur": 1, "ph": "X", "name": "ProcessMessages 1172", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535547, "dur": 36, "ph": "X", "name": "ReadAsync 1172", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535585, "dur": 40, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535629, "dur": 21, "ph": "X", "name": "ReadAsync 915", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535661, "dur": 1, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535676, "dur": 26, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535722, "dur": 1, "ph": "X", "name": "ProcessMessages 1079", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526535918, "dur": 110, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536030, "dur": 3, "ph": "X", "name": "ProcessMessages 6112", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536034, "dur": 56, "ph": "X", "name": "ReadAsync 6112", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536092, "dur": 2, "ph": "X", "name": "ProcessMessages 1912", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536094, "dur": 60, "ph": "X", "name": "ReadAsync 1912", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536156, "dur": 1, "ph": "X", "name": "ProcessMessages 2050", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536158, "dur": 38, "ph": "X", "name": "ReadAsync 2050", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536198, "dur": 49, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536250, "dur": 48, "ph": "X", "name": "ReadAsync 1270", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536300, "dur": 1, "ph": "X", "name": "ProcessMessages 1740", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536301, "dur": 21, "ph": "X", "name": "ReadAsync 1740", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536343, "dur": 1, "ph": "X", "name": "ProcessMessages 991", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536345, "dur": 18, "ph": "X", "name": "ReadAsync 991", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536366, "dur": 24, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536393, "dur": 27, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536423, "dur": 46, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536470, "dur": 1, "ph": "X", "name": "ProcessMessages 930", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536471, "dur": 22, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536510, "dur": 1, "ph": "X", "name": "ProcessMessages 811", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536511, "dur": 22, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536534, "dur": 16, "ph": "X", "name": "ProcessMessages 990", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536552, "dur": 24, "ph": "X", "name": "ReadAsync 990", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536603, "dur": 1, "ph": "X", "name": "ProcessMessages 1060", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536604, "dur": 24, "ph": "X", "name": "ReadAsync 1060", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536630, "dur": 9, "ph": "X", "name": "ProcessMessages 1212", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536640, "dur": 60, "ph": "X", "name": "ReadAsync 1212", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536702, "dur": 1, "ph": "X", "name": "ProcessMessages 906", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536703, "dur": 45, "ph": "X", "name": "ReadAsync 906", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536750, "dur": 1, "ph": "X", "name": "ProcessMessages 1525", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536751, "dur": 90, "ph": "X", "name": "ReadAsync 1525", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536858, "dur": 2, "ph": "X", "name": "ProcessMessages 1198", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536867, "dur": 84, "ph": "X", "name": "ReadAsync 1198", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536954, "dur": 3, "ph": "X", "name": "ProcessMessages 3480", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526536958, "dur": 65, "ph": "X", "name": "ReadAsync 3480", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537024, "dur": 1, "ph": "X", "name": "ProcessMessages 1522", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537026, "dur": 54, "ph": "X", "name": "ReadAsync 1522", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537094, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537097, "dur": 64, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537169, "dur": 1, "ph": "X", "name": "ProcessMessages 1346", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537172, "dur": 53, "ph": "X", "name": "ReadAsync 1346", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537228, "dur": 1, "ph": "X", "name": "ProcessMessages 1056", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537230, "dur": 87, "ph": "X", "name": "ReadAsync 1056", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537321, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537324, "dur": 71, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537399, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537431, "dur": 85, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537517, "dur": 1, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537520, "dur": 63, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537586, "dur": 2, "ph": "X", "name": "ProcessMessages 1067", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537589, "dur": 48, "ph": "X", "name": "ReadAsync 1067", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537642, "dur": 54, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537717, "dur": 1, "ph": "X", "name": "ProcessMessages 814", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537720, "dur": 32, "ph": "X", "name": "ReadAsync 814", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537763, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537765, "dur": 27, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537801, "dur": 69, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537873, "dur": 1, "ph": "X", "name": "ProcessMessages 592", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537875, "dur": 38, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537916, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537917, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537961, "dur": 1, "ph": "X", "name": "ProcessMessages 602", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526537962, "dur": 48, "ph": "X", "name": "ReadAsync 602", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538014, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538016, "dur": 59, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538103, "dur": 3, "ph": "X", "name": "ProcessMessages 239", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538109, "dur": 71, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538210, "dur": 2, "ph": "X", "name": "ProcessMessages 831", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538212, "dur": 72, "ph": "X", "name": "ReadAsync 831", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538289, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538328, "dur": 24, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538354, "dur": 1, "ph": "X", "name": "ProcessMessages 552", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538357, "dur": 43, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538403, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538455, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538463, "dur": 29, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538494, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538508, "dur": 24, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538535, "dur": 39, "ph": "X", "name": "ReadAsync 219", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538575, "dur": 1, "ph": "X", "name": "ProcessMessages 659", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538577, "dur": 29, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538650, "dur": 28, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538679, "dur": 1, "ph": "X", "name": "ProcessMessages 994", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538681, "dur": 18, "ph": "X", "name": "ReadAsync 994", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538701, "dur": 46, "ph": "X", "name": "ReadAsync 66", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538750, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538773, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538774, "dur": 54, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538858, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538860, "dur": 32, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538894, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538896, "dur": 26, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538952, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538954, "dur": 24, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526538981, "dur": 21, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539031, "dur": 17, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539051, "dur": 152, "ph": "X", "name": "ReadAsync 225", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539205, "dur": 1, "ph": "X", "name": "ProcessMessages 1295", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539208, "dur": 37, "ph": "X", "name": "ReadAsync 1295", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539247, "dur": 1, "ph": "X", "name": "ProcessMessages 1249", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539249, "dur": 33, "ph": "X", "name": "ReadAsync 1249", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539292, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539328, "dur": 1, "ph": "X", "name": "ProcessMessages 1094", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539330, "dur": 47, "ph": "X", "name": "ReadAsync 1094", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539380, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539422, "dur": 1, "ph": "X", "name": "ProcessMessages 934", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539424, "dur": 23, "ph": "X", "name": "ReadAsync 934", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539468, "dur": 27, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539514, "dur": 18, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539536, "dur": 39, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539577, "dur": 211, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539790, "dur": 2, "ph": "X", "name": "ProcessMessages 3034", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539793, "dur": 19, "ph": "X", "name": "ReadAsync 3034", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539815, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539858, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539860, "dur": 17, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539880, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539922, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539961, "dur": 1, "ph": "X", "name": "ProcessMessages 995", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539963, "dur": 16, "ph": "X", "name": "ReadAsync 995", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526539990, "dur": 27, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540018, "dur": 28, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540049, "dur": 18, "ph": "X", "name": "ReadAsync 805", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540070, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540110, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540149, "dur": 25, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540184, "dur": 20, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540207, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540259, "dur": 1, "ph": "X", "name": "ProcessMessages 1047", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540261, "dur": 37, "ph": "X", "name": "ReadAsync 1047", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540301, "dur": 72, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540376, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540411, "dur": 1, "ph": "X", "name": "ProcessMessages 808", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540412, "dur": 47, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540462, "dur": 25, "ph": "X", "name": "ReadAsync 899", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540500, "dur": 1, "ph": "X", "name": "ProcessMessages 873", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540503, "dur": 33, "ph": "X", "name": "ReadAsync 873", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540539, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540596, "dur": 17, "ph": "X", "name": "ReadAsync 971", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540636, "dur": 23, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540662, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540699, "dur": 21, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540754, "dur": 36, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540793, "dur": 34, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540830, "dur": 18, "ph": "X", "name": "ReadAsync 748", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540857, "dur": 27, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540893, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540919, "dur": 1, "ph": "X", "name": "ProcessMessages 947", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540920, "dur": 16, "ph": "X", "name": "ReadAsync 947", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540938, "dur": 41, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526540981, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541024, "dur": 19, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541065, "dur": 21, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541088, "dur": 43, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541133, "dur": 1, "ph": "X", "name": "ProcessMessages 957", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541134, "dur": 51, "ph": "X", "name": "ReadAsync 957", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541187, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541226, "dur": 1, "ph": "X", "name": "ProcessMessages 1066", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541228, "dur": 17, "ph": "X", "name": "ReadAsync 1066", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541247, "dur": 38, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541287, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541328, "dur": 24, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541362, "dur": 17, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541382, "dur": 27, "ph": "X", "name": "ReadAsync 169", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541411, "dur": 17, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541435, "dur": 57, "ph": "X", "name": "ReadAsync 258", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541494, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541543, "dur": 1, "ph": "X", "name": "ProcessMessages 1102", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541545, "dur": 20, "ph": "X", "name": "ReadAsync 1102", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541567, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541602, "dur": 20, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541639, "dur": 20, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541662, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541708, "dur": 1, "ph": "X", "name": "ProcessMessages 1057", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541732, "dur": 31, "ph": "X", "name": "ReadAsync 1057", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541766, "dur": 23, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541807, "dur": 29, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541881, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541928, "dur": 1, "ph": "X", "name": "ProcessMessages 1914", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541929, "dur": 25, "ph": "X", "name": "ReadAsync 1914", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541969, "dur": 1, "ph": "X", "name": "ProcessMessages 1051", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541971, "dur": 23, "ph": "X", "name": "ReadAsync 1051", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541997, "dur": 1, "ph": "X", "name": "ProcessMessages 261", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526541999, "dur": 59, "ph": "X", "name": "ReadAsync 261", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542060, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542103, "dur": 1, "ph": "X", "name": "ProcessMessages 1620", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542105, "dur": 38, "ph": "X", "name": "ReadAsync 1620", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542145, "dur": 42, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542190, "dur": 21, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542214, "dur": 31, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542267, "dur": 56, "ph": "X", "name": "ReadAsync 316", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542325, "dur": 19, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542369, "dur": 20, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542391, "dur": 1, "ph": "X", "name": "ProcessMessages 722", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542392, "dur": 48, "ph": "X", "name": "ReadAsync 722", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542442, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542483, "dur": 20, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542532, "dur": 1, "ph": "X", "name": "ProcessMessages 653", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542533, "dur": 16, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542552, "dur": 40, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542599, "dur": 19, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542650, "dur": 22, "ph": "X", "name": "ReadAsync 485", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542682, "dur": 49, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542733, "dur": 1, "ph": "X", "name": "ProcessMessages 674", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542760, "dur": 21, "ph": "X", "name": "ReadAsync 674", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542784, "dur": 38, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542825, "dur": 29, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542857, "dur": 40, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542899, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542936, "dur": 1, "ph": "X", "name": "ProcessMessages 932", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542938, "dur": 17, "ph": "X", "name": "ReadAsync 932", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526542957, "dur": 20, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543006, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543030, "dur": 38, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543071, "dur": 19, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543111, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543157, "dur": 1, "ph": "X", "name": "ProcessMessages 1160", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543158, "dur": 16, "ph": "X", "name": "ReadAsync 1160", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543177, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543199, "dur": 39, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543241, "dur": 22, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543275, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543299, "dur": 1, "ph": "X", "name": "ProcessMessages 1042", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543323, "dur": 17, "ph": "X", "name": "ReadAsync 1042", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543343, "dur": 45, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543395, "dur": 1, "ph": "X", "name": "ProcessMessages 826", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543397, "dur": 22, "ph": "X", "name": "ReadAsync 826", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543439, "dur": 23, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543463, "dur": 1, "ph": "X", "name": "ProcessMessages 1043", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543464, "dur": 18, "ph": "X", "name": "ReadAsync 1043", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543485, "dur": 55, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543542, "dur": 96, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543641, "dur": 30, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543683, "dur": 19, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543705, "dur": 42, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543762, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543807, "dur": 1, "ph": "X", "name": "ProcessMessages 1041", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543809, "dur": 97, "ph": "X", "name": "ReadAsync 1041", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543923, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526543925, "dur": 57, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544004, "dur": 1, "ph": "X", "name": "ProcessMessages 1640", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544006, "dur": 29, "ph": "X", "name": "ReadAsync 1640", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544037, "dur": 1, "ph": "X", "name": "ProcessMessages 1383", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544062, "dur": 23, "ph": "X", "name": "ReadAsync 1383", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544088, "dur": 17, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544126, "dur": 16, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544145, "dur": 52, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544199, "dur": 16, "ph": "X", "name": "ReadAsync 220", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544217, "dur": 29, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544247, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544315, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544402, "dur": 2, "ph": "X", "name": "ProcessMessages 787", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544406, "dur": 59, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544467, "dur": 1, "ph": "X", "name": "ProcessMessages 1126", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544468, "dur": 82, "ph": "X", "name": "ReadAsync 1126", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544610, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544614, "dur": 78, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544695, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544697, "dur": 284, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526544987, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526545053, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526545055, "dur": 710, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526545770, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526545773, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526545858, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526545860, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526545900, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526545931, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546010, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546012, "dur": 121, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546136, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546138, "dur": 98, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546238, "dur": 1, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546240, "dur": 64, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546306, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546309, "dur": 142, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546454, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546457, "dur": 66, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546526, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546529, "dur": 30, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546585, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546588, "dur": 44, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546686, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546689, "dur": 69, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546760, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546762, "dur": 26, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546791, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546793, "dur": 22, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546841, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546844, "dur": 92, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546940, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546942, "dur": 30, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546975, "dur": 2, "ph": "X", "name": "ProcessMessages 256", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526546977, "dur": 58, "ph": "X", "name": "ReadAsync 256", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547039, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547041, "dur": 28, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547109, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547112, "dur": 51, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547170, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547173, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547206, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547208, "dur": 71, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547282, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547284, "dur": 28, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547316, "dur": 26, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547394, "dur": 41, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547480, "dur": 3, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547484, "dur": 29, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547562, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547564, "dur": 35, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547630, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547633, "dur": 42, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547678, "dur": 1, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547680, "dur": 28, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547710, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547712, "dur": 55, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547770, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547772, "dur": 35, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547809, "dur": 31, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547841, "dur": 34, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547878, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547881, "dur": 45, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547927, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547929, "dur": 21, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547952, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526547954, "dur": 24, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548011, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548013, "dur": 26, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548040, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548043, "dur": 71, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548116, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548118, "dur": 42, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548167, "dur": 2, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548170, "dur": 29, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548201, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548203, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548264, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548266, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548298, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548300, "dur": 63, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548366, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548368, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526548395, "dur": 45100, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526593506, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526593510, "dur": 150, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526593663, "dur": 16, "ph": "X", "name": "ProcessMessages 3116", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526593680, "dur": 47576, "ph": "X", "name": "ReadAsync 3116", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526641268, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526641273, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526641338, "dur": 30, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526641373, "dur": 8611, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526649996, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650001, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650063, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650066, "dur": 425, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650495, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650497, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650543, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650545, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650592, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650595, "dur": 101, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650698, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650700, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650731, "dur": 142, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650879, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650915, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526650917, "dur": 218, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526651139, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526651174, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526651176, "dur": 464, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526651643, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526651645, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526651681, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526651682, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526651711, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526651713, "dur": 323, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526652040, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526652042, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526652071, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526652073, "dur": 328, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526652404, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526652405, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526652438, "dur": 802, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653244, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653283, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653285, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653315, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653317, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653372, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653405, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653407, "dur": 495, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653907, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653908, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653940, "dur": 23, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653966, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526653968, "dur": 156, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526654127, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526654153, "dur": 244, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526654401, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526654433, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526654435, "dur": 411, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526654849, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526654851, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526654883, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526654885, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526654913, "dur": 326, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655242, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655244, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655279, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655281, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655314, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655315, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655342, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655344, "dur": 363, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655710, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655712, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655752, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655754, "dur": 140, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655899, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655935, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526655937, "dur": 376, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526656316, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526656353, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526656355, "dur": 613, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526656971, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526656973, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526657005, "dur": 412, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526657420, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526657421, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526657453, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526657455, "dur": 656, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658115, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658149, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658151, "dur": 335, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658490, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658520, "dur": 109, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658632, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658634, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658669, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658671, "dur": 27, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658701, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658730, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658731, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658758, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658760, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658821, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658822, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658858, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658860, "dur": 31, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658895, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658930, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658932, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658962, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658964, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526658998, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659022, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659052, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659100, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659102, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659131, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659133, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659177, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659208, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659210, "dur": 24, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659236, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659262, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659264, "dur": 39, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659305, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659306, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659335, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659338, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659368, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659396, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659397, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659422, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659447, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659448, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659473, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659475, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659501, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659502, "dur": 23, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659527, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659529, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659570, "dur": 5, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659577, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659615, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659618, "dur": 34, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659654, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659655, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659686, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659688, "dur": 28, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659719, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659721, "dur": 32, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659756, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659761, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659792, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659794, "dur": 60, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659859, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659861, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659895, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526659897, "dur": 150, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660053, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660074, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660104, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660106, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660143, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660145, "dur": 196, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660345, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660347, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660373, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660408, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660410, "dur": 101, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660514, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660516, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660561, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660563, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660592, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660615, "dur": 114, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660743, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660781, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660782, "dur": 27, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526660813, "dur": 272647, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526933471, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526933475, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526933526, "dur": 912, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526934441, "dur": 65333, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526999781, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526999784, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526999821, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279526999824, "dur": 487722, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527487556, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527487560, "dur": 62, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527487626, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527487630, "dur": 395119, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527882757, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527882760, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527882783, "dur": 17, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527882802, "dur": 18367, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527901178, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527901182, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527901205, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527901209, "dur": 1677, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527902893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527902896, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527902912, "dur": 21, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279527902935, "dur": 582774, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279528485718, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279528485721, "dur": 86, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279528485812, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279528485816, "dur": 2085, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279528487908, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279528487910, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279528487955, "dur": 22, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279528487978, "dur": 2511277, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279530999263, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279530999265, "dur": 251, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279530999520, "dur": 32, "ph": "X", "name": "ProcessMessages 367", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279530999555, "dur": 176328, "ph": "X", "name": "ReadAsync 367", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531175891, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531175894, "dur": 181, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531176079, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531176083, "dur": 65528, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531241619, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531241622, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531241648, "dur": 19, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531241668, "dur": 16528, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531258204, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531258207, "dur": 225, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531258437, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531258441, "dur": 1884, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531260330, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531260332, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531260366, "dur": 23, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531260390, "dur": 120934, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531381333, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531381336, "dur": 159, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531381500, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531381504, "dur": 2891, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531384400, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531384402, "dur": 174, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531384580, "dur": 23, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531384605, "dur": 1115, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531385724, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 47244640256, "ts": 1753279531385750, "dur": 10028, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21312, "tid": 8381, "ts": 1753279531396312, "dur": 854, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21312, "tid": 42949672960, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21312, "tid": 42949672960, "ts": 1753279526506672, "dur": 102138, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21312, "tid": 42949672960, "ts": 1753279526608811, "dur": 1, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21312, "tid": 42949672960, "ts": 1753279526608813, "dur": 73, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21312, "tid": 8381, "ts": 1753279531397168, "dur": 9, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21312, "tid": 38654705664, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21312, "tid": 38654705664, "ts": 1753279526503463, "dur": 4892356, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21312, "tid": 38654705664, "ts": 1753279526503721, "dur": 2630, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21312, "tid": 38654705664, "ts": 1753279531395822, "dur": 44, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21312, "tid": 38654705664, "ts": 1753279531395835, "dur": 15, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21312, "tid": 8381, "ts": 1753279531397177, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753279526523332, "dur": 55, "ph": "X", "name": "IPC_Client_InitializeAndConnectToParent", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753279526523410, "dur": 2261, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753279526525682, "dur": 648, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753279526526498, "dur": 67, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753279526526566, "dur": 512, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753279526527820, "dur": 2561, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_758CA54D648AA1BC.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753279526531380, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753279526531493, "dur": 114, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753279526531707, "dur": 81, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753279526532943, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753279526536493, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753279526537450, "dur": 52, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1753279526542637, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753279526544893, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1753279526527098, "dur": 18048, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753279526545157, "dur": 4839901, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753279531385061, "dur": 482, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753279531385608, "dur": 144, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753279531385753, "dur": 77, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753279531385830, "dur": 198, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753279531386230, "dur": 1777, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753279526527196, "dur": 17971, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526545190, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526545544, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753279526545601, "dur": 954, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1753279526545540, "dur": 1015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6FDD4B7A6EC84268.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753279526546699, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526546906, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753279526546905, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F72D29FA518CE2F1.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753279526547015, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526547078, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753279526547076, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7880F7755F74374F.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753279526547146, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526547214, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526547272, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753279526547458, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526547641, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526547749, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753279526547806, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526547868, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753279526547987, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526548096, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526548267, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526548365, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526548424, "dur": 105, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753279526548660, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526548755, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526548854, "dur": 1688, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526550543, "dur": 2334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526552878, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526554012, "dur": 1663, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526557528, "dur": 917, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\EditablePath\\EditablePathController.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753279526555676, "dur": 2770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526558446, "dur": 1489, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526559936, "dur": 1611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526561547, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526563075, "dur": 512, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Codebase\\SetMember.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753279526562806, "dur": 1868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526564675, "dur": 2321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526566997, "dur": 2024, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526569021, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526570202, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526571763, "dur": 851, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526572614, "dur": 1042, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526573656, "dur": 1058, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526574715, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526575172, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753279526575298, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526575402, "dur": 632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753279526576035, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526576192, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753279526576306, "dur": 968, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526577280, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753279526577823, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526578130, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753279526578326, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753279526578495, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526578769, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753279526578653, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753279526579202, "dur": 974, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526580234, "dur": 67858, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526648093, "dur": 2207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753279526650301, "dur": 780, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526651345, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753279526652157, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Rewrite.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753279526651088, "dur": 2537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753279526653626, "dur": 1050, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526655221, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753279526655324, "dur": 776, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753279526656260, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Security.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753279526656656, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753279526654685, "dur": 3887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753279526658573, "dur": 1446, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526660025, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526660144, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526660295, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753279526661339, "dur": 4723752, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526527254, "dur": 17951, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526545236, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526545471, "dur": 883, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1753279526545219, "dur": 1136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A3A3243B74248B90.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753279526546356, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526546446, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526546444, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3B0CE8B1A0A2F90C.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753279526546561, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526546626, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526546624, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_BD74DBBEF40E5ADA.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753279526546761, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526546760, "dur": 125, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_387623EB1508B7EB.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753279526546885, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526547018, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526547017, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_9F8263619ADFDEFD.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753279526547092, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526547163, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526547336, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526547481, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753279526547565, "dur": 301, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526547873, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526547939, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526548168, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526548270, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526548380, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526548483, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526548563, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526548646, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526548738, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526548818, "dur": 1971, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526550789, "dur": 2184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526552974, "dur": 1871, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526554845, "dur": 1840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526557587, "dur": 797, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\ExposeDescriptor.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753279526556686, "dur": 2093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526558779, "dur": 2020, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526560799, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526563302, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Vector4\\Vector4Distance.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753279526562008, "dur": 2205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526564213, "dur": 1943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526566157, "dur": 1670, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526567827, "dur": 2370, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526570237, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526571631, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526572607, "dur": 1217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526573824, "dur": 906, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526574735, "dur": 506, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526575249, "dur": 466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753279526575716, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526576028, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526576335, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526575781, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753279526576766, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526576940, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526577016, "dur": 270, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753279526577286, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526577486, "dur": 768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753279526578255, "dur": 271, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526578599, "dur": 304, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753279526579121, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526578947, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753279526579606, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526579797, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526579852, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753279526580049, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526580119, "dur": 698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753279526580824, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526581024, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753279526581176, "dur": 419, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753279526581596, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526581761, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526581822, "dur": 66351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526651002, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526648182, "dur": 3051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753279526651233, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526651714, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526652398, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526653122, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Connections.Abstractions.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526653430, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Json.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526654778, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526654923, "dur": 285, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526655303, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ext.nunit@1.0.6\\net35\\unity-custom\\nunit.framework.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526651452, "dur": 4286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753279526655738, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526656184, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526656848, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526657749, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.DiaSymReader.Native.amd64.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753279526655874, "dur": 3744, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753279526659618, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526659748, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526659878, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526659937, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526660051, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526660133, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526660216, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526660292, "dur": 753, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526661048, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753279526661103, "dur": 4724009, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526527311, "dur": 17940, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526545273, "dur": 244, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526545519, "dur": 739, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1753279526545262, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FA85EF908E5A33BF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753279526546501, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ScreenCaptureModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526546499, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ScreenCaptureModule.dll_116A6FADE34C1D2F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753279526546568, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526546629, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526546628, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3AA9E1B89A839D24.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753279526546696, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526546758, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526546757, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_B8A7170076CF7080.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753279526546817, "dur": 300, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526547126, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.NVIDIAModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526547124, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.NVIDIAModule.dll_882D400E4D49662A.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753279526547189, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526547254, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526547322, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526547377, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526547526, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526547635, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526548193, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526548507, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526548592, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526548675, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753279526548727, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526548783, "dur": 2051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526550834, "dur": 2104, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526552938, "dur": 1769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526554707, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526556212, "dur": 917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526557583, "dur": 873, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Contexts\\TargetFieldContext.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753279526557129, "dur": 2484, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526559613, "dur": 1456, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526561070, "dur": 1701, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526562772, "dur": 1623, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526564395, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526565727, "dur": 1694, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526567422, "dur": 1810, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526569232, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526570327, "dur": 1571, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526571898, "dur": 2377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526574276, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526574720, "dur": 531, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526575260, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753279526575569, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526575635, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753279526575941, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526576199, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753279526576590, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526576675, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753279526577095, "dur": 1465, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526578570, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526578624, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753279526579121, "dur": 227, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526578917, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753279526579655, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526579813, "dur": 68350, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526648171, "dur": 2355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753279526650527, "dur": 1724, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526653840, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526654489, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526654917, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526652258, "dur": 2977, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753279526655241, "dur": 202, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526655524, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526656407, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-fibers-l1-1-0.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526656690, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Abstractions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526658725, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526655450, "dur": 3514, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753279526658965, "dur": 209, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526659188, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526659298, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526659492, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526659564, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526659832, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.Collections.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526659831, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Collections.DocCodeSamples.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753279526659909, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526659971, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526660129, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526660199, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279526660312, "dur": 1241185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753279527901500, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753279527901499, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753279527901714, "dur": 1725, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753279527903447, "dur": 3481635, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526527393, "dur": 17906, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526545319, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753279526545459, "dur": 1009, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1753279526545310, "dur": 1159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_49D0198E18ABFDE2.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753279526546469, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526546571, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753279526546570, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_7591B359D09ADD67.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753279526546639, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526546700, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753279526546699, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_2FC980318CB13784.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753279526546760, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526546823, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteMaskModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753279526546821, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_6555E2061ABDDC70.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753279526546882, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526547012, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753279526547010, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_C8504B54661C208D.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753279526547076, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526547135, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526547200, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526547593, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526547737, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753279526547792, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526547997, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526548144, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526548308, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526548365, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526548487, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526548618, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526548787, "dur": 1923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526550711, "dur": 2577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526554167, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PsdFile\\ImageResources\\UnicodeAlphaNames.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753279526553288, "dur": 2025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526555313, "dur": 1931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526557569, "dur": 961, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\Slots\\ScreenPositionSlotControlView.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753279526557244, "dur": 2289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526559534, "dur": 1416, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526560951, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526562210, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526563621, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526564587, "dur": 1807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526566395, "dur": 2469, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526568865, "dur": 1301, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526570203, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526571388, "dur": 1271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526572659, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526573174, "dur": 1542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526574717, "dur": 455, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526575173, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753279526575283, "dur": 840, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526576252, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753279526576577, "dur": 176, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753279526577055, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Collections\\MergedKeyedCollection.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753279526577686, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Graphs\\Graph.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753279526576126, "dur": 2306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753279526578433, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526578681, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753279526579867, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Graph\\GetScriptGraphs.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753279526578966, "dur": 1405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753279526580372, "dur": 513, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526580955, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753279526581058, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526581112, "dur": 438, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753279526581556, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526581717, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526581793, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753279526581873, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526581953, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753279526582435, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526582663, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753279526582818, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279526582912, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753279526582997, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753279526583300, "dur": 350600, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753279526938750, "dur": 61060, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753279526938546, "dur": 61316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753279527000265, "dur": 3999338, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753279531002949, "dur": 168764, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753279531002948, "dur": 172096, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753279531176162, "dur": 149, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753279531176326, "dur": 65694, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753279531258484, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1753279531258483, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1753279531258606, "dur": 2191, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1753279531260801, "dur": 124268, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526527382, "dur": 17883, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526545294, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526545529, "dur": 993, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1753279526545286, "dur": 1237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_63198FADDC2DE5CF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753279526546524, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526546603, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526546602, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_55536420766141FA.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753279526546666, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526546837, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526546836, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AndroidJNIModule.dll_51DC255B330D5605.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753279526546900, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526547031, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526547311, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753279526547375, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526547444, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526547559, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526547639, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526547765, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526547931, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526548075, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526548183, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526548337, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526548403, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526548490, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526548548, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753279526548601, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526548778, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526550070, "dur": 1450, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526551521, "dur": 2210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526553732, "dur": 2137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526557489, "dur": 952, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\FlowState.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753279526555870, "dur": 2702, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526558572, "dur": 1689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526560261, "dur": 1275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526561536, "dur": 1060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526562963, "dur": 583, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Control\\SwitchOnString.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753279526562596, "dur": 1914, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526564511, "dur": 1709, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526566221, "dur": 2149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526568371, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526569934, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526570995, "dur": 1576, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526572572, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526573903, "dur": 806, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526574709, "dur": 478, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526575190, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753279526575587, "dur": 402, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526576114, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526576180, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526575994, "dur": 826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753279526576820, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526577020, "dur": 308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753279526577329, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526577572, "dur": 409, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526578686, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Editor\\RenderPipeline\\RenderPipelineResourcesEditor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753279526577386, "dur": 1542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753279526578929, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526579142, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753279526579267, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526579353, "dur": 1640, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753279526580994, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526581212, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753279526581306, "dur": 480, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753279526581787, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526581925, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753279526582005, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753279526582236, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526582366, "dur": 65713, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526650142, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526650712, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526648098, "dur": 3172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753279526651271, "dur": 909, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526652214, "dur": 188, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526654982, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526655305, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526655647, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526652190, "dur": 3532, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753279526655723, "dur": 2232, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526659425, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526660097, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526660165, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753279526657969, "dur": 2806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753279526660776, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753279526660958, "dur": 4724160, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526527219, "dur": 17957, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526545190, "dur": 314, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526545536, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526545599, "dur": 778, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1753279526545528, "dur": 851, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_CBFD31FC7A6EE8BC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753279526546379, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526546475, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526546473, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_A964AEB46C39AB32.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753279526546550, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526546617, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526546615, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D293A48779142A71.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753279526546681, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526546746, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526546744, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_8837211AE3CE1746.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753279526546894, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526547091, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526547090, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753279526547170, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526547332, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526547709, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753279526547876, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526548030, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526548166, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526548259, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526548344, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526548465, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753279526548608, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526548736, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526548822, "dur": 1692, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526551089, "dur": 532, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Window\\TimelineWindow_TrackGui.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753279526550514, "dur": 2849, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526553364, "dur": 2357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526555721, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526557596, "dur": 833, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Enumerations\\StructFieldOptions.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753279526557025, "dur": 2923, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526559949, "dur": 1496, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526561445, "dur": 1108, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526562553, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526564119, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526565423, "dur": 1840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526567264, "dur": 2120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526569384, "dur": 1262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526570646, "dur": 1147, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526571840, "dur": 2411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526574252, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526574715, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526575232, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753279526575507, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526575578, "dur": 735, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753279526576314, "dur": 715, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526577098, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526577257, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753279526577428, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526577683, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526577588, "dur": 776, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753279526578365, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526578603, "dur": 418, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753279526579022, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526579526, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526579094, "dur": 584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753279526579678, "dur": 454, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526580137, "dur": 67996, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526648308, "dur": 615, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526649325, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526651001, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526651306, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526648143, "dur": 3414, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753279526651558, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526652157, "dur": 1695, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526653893, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526654459, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.RenderPipelines.Universal.Runtime.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526654923, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Metadata.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526655304, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.WebEncoders.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526655794, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526656089, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526656406, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526656864, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526651688, "dur": 5708, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753279526657397, "dur": 2002, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526659411, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526659637, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526659754, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526659943, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526660067, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526660246, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526660306, "dur": 278276, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279526938593, "dur": 547073, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279526938593, "dur": 548208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753279527487789, "dur": 220, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753279527488090, "dur": 395159, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753279527901493, "dur": 584463, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279527901492, "dur": 584466, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279528485977, "dur": 2404, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753279528488385, "dur": 2896722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526527239, "dur": 17951, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526545220, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526545453, "dur": 1221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1753279526545201, "dur": 1474, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_C0FCC60981132629.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753279526546675, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526546919, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526546918, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_ADDD82C2A90EA988.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753279526547118, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526547199, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526547260, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526547259, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_62273FC84B3E0F2D.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753279526547322, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526547472, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526547924, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526548100, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526548174, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526548363, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526548487, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526548553, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753279526548608, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526548825, "dur": 1366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526550191, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526551319, "dur": 2612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526553932, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526555492, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526557541, "dur": 837, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\NestrerUnitWidget.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753279526556633, "dur": 2559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526559193, "dur": 1795, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526560989, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526562227, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526563880, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526565416, "dur": 1023, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526566439, "dur": 1280, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526567719, "dur": 2071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526569790, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526570722, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526572024, "dur": 1700, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526573724, "dur": 982, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526574707, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526575259, "dur": 323, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753279526575582, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526575638, "dur": 607, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753279526576246, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526576465, "dur": 511, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526577027, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526577219, "dur": 391, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526576405, "dur": 1456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753279526577862, "dur": 718, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526578625, "dur": 59, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526578685, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753279526578941, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526579121, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526579011, "dur": 1675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753279526580687, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526580894, "dur": 67206, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526648374, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526649233, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526649794, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCompression.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526651002, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526651347, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526651462, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526648102, "dur": 3768, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753279526651875, "dur": 1069, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526653838, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Drawing.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526655304, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526652958, "dur": 2738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753279526655697, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526656863, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.ClientFactory.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753279526655791, "dur": 3063, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753279526658855, "dur": 503, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526659385, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526659495, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526659555, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526659685, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526659758, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526659873, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526659993, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526660054, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753279526660394, "dur": 4724657, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526527276, "dur": 17946, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526545245, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526545469, "dur": 1198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1753279526545235, "dur": 1432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DE98305BD6D68C77.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753279526546668, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526546741, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526546739, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestModule.dll_C7611EE8A5C55055.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753279526546870, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526546932, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526546931, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5AAB6A659692F205.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753279526547127, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526547259, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526547257, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_D5253DAEDFB20EED.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753279526547339, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526547394, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526547458, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526547716, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526547773, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526547922, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526548036, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526548538, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3258459190130463912.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753279526548650, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526548806, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526550539, "dur": 2577, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526554289, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.spriteshape@9.0.3\\Editor\\ObjectMenuCreation\\GameObjectCreation.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753279526553117, "dur": 2117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526555235, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526557544, "dur": 855, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Description\\UnitAnalysis.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753279526556701, "dur": 2125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526558826, "dur": 1612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526560439, "dur": 1745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526562185, "dur": 972, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526563158, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526564520, "dur": 1248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526565768, "dur": 1526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526567295, "dur": 2121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526569417, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526570602, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526571834, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526573230, "dur": 1474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526574714, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526574776, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526575201, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753279526575712, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526576112, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526575774, "dur": 933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753279526576708, "dur": 683, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526577574, "dur": 389, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526578384, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526577452, "dur": 1178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753279526578631, "dur": 423, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526579065, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526579243, "dur": 2610, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526581853, "dur": 66294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526648156, "dur": 2753, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753279526650910, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526651146, "dur": 162, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526653502, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526653892, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526651105, "dur": 3247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753279526654353, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526654979, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526655305, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526655715, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-errorhandling-l1-1-0.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526656405, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordaccore.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526657233, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526657294, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526657409, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526657768, "dur": 309, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526654481, "dur": 4077, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753279526658559, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526659422, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753279526658677, "dur": 2364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753279526661042, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753279526661220, "dur": 4723949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526527299, "dur": 17938, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526545261, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526545467, "dur": 1027, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1753279526545249, "dur": 1246, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_939D9295B43D54DB.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753279526546496, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526546666, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526546665, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_0E723EAE4164004E.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753279526546732, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526546792, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526546791, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_065B772FA1729652.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753279526546848, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526546923, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526546922, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_F27749E0A6B65183.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753279526547001, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526547073, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526547138, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526547198, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526547309, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526547371, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526547534, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753279526547607, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526547666, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526547779, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526547907, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753279526547960, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526548026, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526548082, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526548145, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526548206, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526548270, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526548358, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526548412, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526548465, "dur": 133, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.rsp2"}}, {"pid": 12345, "tid": 9, "ts": 1753279526548725, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3043913547040211301.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753279526548784, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526548900, "dur": 1647, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526550548, "dur": 3073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526553622, "dur": 1893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526555516, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526557499, "dur": 956, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\Targets\\BuiltInUnlitSubTarget.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753279526556865, "dur": 3058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526559923, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526561446, "dur": 1611, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526563057, "dur": 1866, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526564923, "dur": 1968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526566891, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526568854, "dur": 2421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526571904, "dur": 575, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.mathematics@1.2.6\\Unity.Mathematics\\double3x4.gen.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753279526571276, "dur": 2437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526573713, "dur": 1011, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526574725, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526575359, "dur": 915, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753279526576274, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526576369, "dur": 667, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753279526577037, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526577260, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753279526578003, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Transactions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526578770, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Window\\TimelineWindow_StateChange.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753279526577480, "dur": 1396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753279526578876, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526579190, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526579246, "dur": 2686, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526581933, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753279526582035, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526582456, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\Converter\\PPv2\\EffectConverters\\MotionBlurConverter.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753279526582106, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753279526582534, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526582615, "dur": 65667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526648290, "dur": 2142, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753279526650433, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526650723, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526651000, "dur": 360, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526652401, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526652924, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526653784, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526653892, "dur": 1050, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526655305, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526650557, "dur": 5121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753279526655679, "dur": 745, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526656863, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526657378, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526658710, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526659426, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526659598, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279526656439, "dur": 3344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753279526659784, "dur": 411, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526660204, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279526660329, "dur": 4598195, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753279531258527, "dur": 123159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279531258526, "dur": 123162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753279531381708, "dur": 3233, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526527437, "dur": 17882, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526545338, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526545474, "dur": 860, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1753279526545333, "dur": 1002, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_71CF6729598465FE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753279526546335, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526546441, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526546440, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_0DA2B5FA22101DBE.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753279526546801, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526546799, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_4423571CA8F0F05E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753279526546923, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526547047, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_4423571CA8F0F05E.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753279526547109, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753279526547220, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526547471, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526547852, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526548149, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 10, "ts": 1753279526548222, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526548313, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526548426, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753279526548565, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526548698, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753279526548802, "dur": 1881, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526551426, "dur": 513, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\TimelineTreeViewGUI.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753279526550683, "dur": 2486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526553170, "dur": 1781, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526554951, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526557506, "dur": 961, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Plugin\\BoltFlowManifest.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753279526556313, "dur": 2323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526558637, "dur": 964, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526559601, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526560997, "dur": 1528, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526562526, "dur": 1876, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526564403, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526565701, "dur": 1654, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526567356, "dur": 1920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526569277, "dur": 1380, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526570657, "dur": 672, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526571329, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526572423, "dur": 1542, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526573965, "dur": 762, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526574727, "dur": 488, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526575224, "dur": 938, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753279526576162, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526576335, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526576256, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753279526576998, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526577198, "dur": 389, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526577593, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753279526577813, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526577913, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526578465, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526578566, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526578747, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526577878, "dur": 1120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753279526578999, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526579203, "dur": 391, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526579599, "dur": 29836, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526609436, "dur": 3046, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526612483, "dur": 38529, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526651055, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526651152, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526651415, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526651848, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526651987, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526652157, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526652544, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authorization.Policy.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526653841, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526651025, "dur": 3369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753279526654395, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526655304, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Microsoft.CSharp.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526655515, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-file-l1-2-0.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526656085, "dur": 194, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.DiagnosticSource.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526656407, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526656913, "dur": 512, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526654513, "dur": 3382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753279526657896, "dur": 757, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526659196, "dur": 202, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526659427, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526659533, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526659834, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526660126, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.Core.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753279526658661, "dur": 2316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753279526660977, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753279526661060, "dur": 4724251, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526527430, "dur": 17879, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526545329, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ContentLoadModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526545461, "dur": 859, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1753279526545315, "dur": 1006, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_77AE899CA5593464.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753279526546322, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526546416, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526546414, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_85F6C15D73A578BD.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753279526546479, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526546554, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526546553, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F7F95DB68C78C481.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753279526546660, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526546723, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526546721, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_41265C97D041BE6E.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753279526546778, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526546910, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526546909, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_64C5CD55CF2E68A6.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753279526547079, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526547176, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526547270, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753279526547339, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526547419, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526547708, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753279526547759, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526547850, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526547946, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526548007, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526548421, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753279526548573, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753279526548625, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526548692, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6841316898876630997.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753279526548744, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526548798, "dur": 1699, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526550497, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526551843, "dur": 1462, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526553305, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526554862, "dur": 1772, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526557568, "dur": 848, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Control\\SelectOnStringDescriptor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753279526556634, "dur": 2349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526559008, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526560445, "dur": 1103, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526561549, "dur": 1347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526562896, "dur": 1831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526564728, "dur": 947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526565675, "dur": 1794, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526567469, "dur": 2397, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526569866, "dur": 1025, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526570892, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526572552, "dur": 1143, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526573695, "dur": 1019, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526574714, "dur": 535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526575250, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753279526575459, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526575535, "dur": 1308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753279526576843, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526577039, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753279526577217, "dur": 373, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526578002, "dur": 400, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Csp.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526578557, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526578765, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Editor\\BurstLoader.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753279526577203, "dur": 1647, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753279526578851, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526579328, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526579491, "dur": 3426, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526582918, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753279526583029, "dur": 65037, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526651146, "dur": 1229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526648075, "dur": 4822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753279526652898, "dur": 1014, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526655168, "dur": 149, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventLog.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526655318, "dur": 427, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventSource.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526656033, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526656407, "dur": 471, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Xml.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526657415, "dur": 1322, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753279526653921, "dur": 5359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753279526659281, "dur": 455, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526659743, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526659802, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526659868, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526660065, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526660289, "dur": 745, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526661036, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753279526661113, "dur": 4723983, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526527462, "dur": 17913, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526545392, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526545514, "dur": 868, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1753279526545384, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7F06B267F1BFA456.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753279526546383, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526546492, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526546491, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E586C88FEB060A87.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753279526546560, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526546650, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TLSModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526546648, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TLSModule.dll_277964AD70D626E0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753279526546775, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526546904, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526546903, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3F15606BE1109C0F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753279526547004, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526547213, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753279526547459, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526547537, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526547590, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526547643, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526547837, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526547888, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526547988, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526548095, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526548230, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526548381, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526548488, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526548621, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526548695, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526548826, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526548878, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526548979, "dur": 286, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526549266, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526549641, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526549858, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526549961, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526550014, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526550067, "dur": 267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526550437, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526550632, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526550887, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526550939, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526550991, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526551193, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Memory.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526551473, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526551778, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526552041, "dur": 442, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526552484, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526552680, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526552836, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526552919, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526553399, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526553453, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526554020, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526554152, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526554211, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526554297, "dur": 195, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526554493, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526554630, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526554838, "dur": 223, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526555067, "dur": 235, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526555303, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526555439, "dur": 485, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\InvalidSignatureException.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526555925, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526556033, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\ILogScope.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526556259, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogEvent.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526556320, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogMatch.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526556423, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\OutOfOrderExpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526556724, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnexpectedLogMessageException.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526556866, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnhandledLogMessageException.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526556968, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnityTestTimeoutException.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526557056, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526557248, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\ConditionalIgnoreAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526557486, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestEnumerator.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526557566, "dur": 905, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526558473, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityCombinatorialStrategy.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526558594, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityPlatformAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526558755, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnitySetUpAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526558834, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTearDownAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526559076, "dur": 290, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTestAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526559367, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\BaseDelegator.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526559479, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandBase.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526559623, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableApplyChangesToContextCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526559712, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRepeatedTestCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526559920, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableRetryTestCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526560001, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableSetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526560089, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526560224, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestState.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526560471, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\ImmediateEnumerableCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526560565, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526560630, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\SetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526560761, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\TestActionCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526560891, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ConstructDelegator.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526560950, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\AssemblyNameFilter.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526561056, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\CategoryFilterExtended.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526561291, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\FullNameFilter.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526561426, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IAsyncTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526561511, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IStateSerializer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526561607, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\OrderedTestSuiteModifier.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526561829, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526561947, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CoroutineTestWorkItem.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526562058, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\DefaultTestWorkItem.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526562178, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\FailCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526562303, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\IEnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526562495, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526562616, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\RestoreTestContextAfterDomainReload.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526562721, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\TestCommandBuilder.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526562828, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityLogCheckDelegatingCommand.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526562927, "dur": 335, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestAssemblyRunner.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526563263, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestExecutionContext.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526563486, "dur": 293, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\TestResultExtensions.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526563780, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\UnityTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526563964, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\PlayerQuitHandler.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526564158, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\PlayModeRunnerCallback.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526564283, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\RemoteTestResultSender.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526564453, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRenderer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526564529, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRendererCallback.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526564586, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\ITestRunnerListener.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526564645, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Messages\\IEditModeTestYieldInstruction.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526564760, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsController.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526564823, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsControllerSettings.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526564877, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\IRemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526564934, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\PlayerConnectionMessageIds.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526565181, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestData.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526565262, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultData.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526565329, "dur": 249, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526565579, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataWithTestData.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526565742, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RuntimeTestRunnerFilter.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526565903, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\SynchronousFilter.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526566034, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestEnumeratorWrapper.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526566143, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestListenerWrapper.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526566245, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestPlatform.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526566344, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526566460, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyWrapper.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526566556, "dur": 322, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526566880, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyWrapper.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526567022, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526567106, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\PlayerTestAssemblyProvider.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526567230, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\ScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526567303, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AttributeHelper.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526567385, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ColorEqualityComparer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526567500, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\CoroutineRunner.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526567568, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\FloatEqualityComparer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526567630, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IOuterUnityTestAction.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526567697, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IPostBuildCleanup.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526567765, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IPrebuildSceneSetup.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526567843, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ITestRunCallback.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526567974, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\IMonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526568046, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\MonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526568123, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\PostBuildCleanupAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526568266, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\PrebuildSceneSetupAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526568389, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\QuaternionEqualityComparer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526568499, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\StacktraceFilter.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526568601, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackAttribute.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526568719, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackListener.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526568831, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Utils.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526568957, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector2ComparerWithEqualsOperator.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526569158, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector2EqualityComparer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526569278, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector3ComparerWithEqualsOperator.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526569390, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector3EqualityComparer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526569462, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector4ComparerWithEqualsOperator.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526569646, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector4EqualityComparer.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526547320, "dur": 22495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753279526569817, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526570024, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526570090, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753279526570207, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526570529, "dur": 415, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526570945, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526571002, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526571059, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526571191, "dur": 187, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526571380, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526571670, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526571815, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526571872, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526572033, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526572095, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526572151, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526572206, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526572411, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526572468, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526572592, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526572842, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526572897, "dur": 258, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526573156, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526573670, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526573731, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526573852, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526574039, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\CommandLineTest\\ExecutionSettings.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526574135, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\CommandLineTest\\ExitCallbacks.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526574226, "dur": 206, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\CommandLineTest\\ExitCallbacksDataHolder.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753279526570401, "dur": 4164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753279526574566, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526574742, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753279526574822, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753279526575046, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526575170, "dur": 334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753279526575544, "dur": 527, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753279526576072, "dur": 843, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526576967, "dur": 359, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753279526577458, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526577374, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753279526577957, "dur": 253, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526578460, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526578769, "dur": 380, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526579308, "dur": 399, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526578215, "dur": 1579, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1753279526579799, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526579860, "dur": 149, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526580762, "dur": 61020, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 12, "ts": 1753279526648076, "dur": 2334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753279526650411, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526651344, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.CookiePolicy.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526650506, "dur": 2073, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753279526652580, "dur": 1200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526654921, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventLog.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526655305, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526653790, "dur": 2350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753279526656141, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526656864, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526657441, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Core.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526658050, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.TraceSource.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526658462, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526659137, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526659427, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753279526656254, "dur": 3822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753279526660076, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526660261, "dur": 340, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753279526660645, "dur": 4724403, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526527502, "dur": 17882, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526545404, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DirectorModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526545561, "dur": 990, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1753279526545392, "dur": 1160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3AC0D0C527F5439B.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753279526546552, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526546898, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526547043, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_D3933C87E9EDE72F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753279526547097, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526547095, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753279526547231, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526547441, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753279526547703, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526547813, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526548052, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526548312, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526548511, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526548708, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526548866, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526548960, "dur": 260, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526549221, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526549406, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526549804, "dur": 386, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526550235, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526550433, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526550493, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526550617, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526550805, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526551025, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526551481, "dur": 232, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526551984, "dur": 478, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526552504, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526552669, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526552826, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526552920, "dur": 133, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526553089, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526553226, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526553400, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526553650, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526553809, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526553926, "dur": 316, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526554247, "dur": 349, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526554656, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526554708, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526554763, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526554821, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526554984, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526555227, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526555278, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526555370, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526555487, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\BaseEventData.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526555639, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\PointerEventData.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526555787, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventHandle.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526555978, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526556030, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventSystem.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526556098, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTrigger.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526556277, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTriggerType.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526556351, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\ExecuteEvents.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526556501, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\PointerInputModule.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526556572, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\StandaloneInputModule.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526556638, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\TouchInputModule.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526556692, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\MoveDirection.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526556750, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycasterManager.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526556856, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\Physics2DRaycaster.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526556994, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\PhysicsRaycaster.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526557046, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycastResult.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526557156, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelRaycaster.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526557291, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\Properties\\AssemblyInfo.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526557489, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\CanvasUpdateRegistry.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526557586, "dur": 826, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\ClipperRegistry.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526558413, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526558649, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Dropdown.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526558895, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontData.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526559010, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRaycaster.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526559162, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IGraphicEnabledDisabled.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526559334, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\AspectRatioFitter.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526559548, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ContentSizeFitter.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526559670, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\GridLayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526559730, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalLayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526559784, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalOrVerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526559841, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ILayoutElement.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526559989, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutElement.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526560050, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526560135, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutRebuilder.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526560188, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutUtility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526560248, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\VerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526560303, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Mask.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526560591, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Scrollbar.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526560687, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ScrollRect.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526560853, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Selectable.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526560939, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SetPropertyUtility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526561022, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Slider.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526561245, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpecializedCollections\\IndexedSet.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526561307, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\SpriteState.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526561410, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Text.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526561718, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Toggle.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526561935, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ToggleGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526562051, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\ReflectionMethodsCache.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526562149, "dur": 269, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\VertexHelper.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526562419, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\BaseMeshEffect.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526562641, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\PositionAsUV1.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526562730, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526547562, "dur": 15286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753279526562849, "dur": 223, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526563100, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526563175, "dur": 1255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526564430, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526565719, "dur": 1400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526567120, "dur": 1883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526569003, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526570479, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526572699, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEditor.TestRunner\\GUI\\GuiHelper.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753279526572047, "dur": 2366, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526574414, "dur": 293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526574708, "dur": 653, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526575362, "dur": 345, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753279526575708, "dur": 472, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526576465, "dur": 515, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526577573, "dur": 289, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526576183, "dur": 1698, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753279526577881, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526578026, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526578137, "dur": 337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526578514, "dur": 263, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526578778, "dur": 2253, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526581032, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753279526581140, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526581200, "dur": 376, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753279526581577, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526581794, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753279526581884, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526581944, "dur": 381, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753279526582326, "dur": 211, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526582568, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526582647, "dur": 68387, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526651057, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526651345, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526652210, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\clrjit.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526651049, "dur": 2594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753279526653643, "dur": 1742, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526656864, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Drawing.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753279526655399, "dur": 2669, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753279526658068, "dur": 1122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526659198, "dur": 620, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526660130, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526660262, "dur": 366, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753279526660629, "dur": 4724432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526527557, "dur": 17838, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526545435, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526545586, "dur": 1030, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1753279526545403, "dur": 1214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_38FFBE07FFE62337.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753279526546671, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526546669, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F1B86C7E0BF881E0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753279526546816, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526547043, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F1B86C7E0BF881E0.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753279526547168, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526547402, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526547622, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526547798, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526548108, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526548752, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526548827, "dur": 1658, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526550485, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526551606, "dur": 2348, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526553954, "dur": 1813, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526555767, "dur": 822, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526557593, "dur": 904, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Variables\\UnifiedVariableUnitDescriptor.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753279526556589, "dur": 2422, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526559011, "dur": 1173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526560184, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526561853, "dur": 1192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526563093, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Windows\\Sidebars\\Sidebars.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753279526563045, "dur": 1748, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526564793, "dur": 1337, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526566130, "dur": 1261, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526567391, "dur": 2266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526569657, "dur": 1471, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526571128, "dur": 2286, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526573414, "dur": 1296, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526574710, "dur": 651, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526575361, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753279526575622, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526576117, "dur": 360, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526575777, "dur": 1109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753279526576887, "dur": 757, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526577652, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526578042, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526578147, "dur": 311, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526578707, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526579305, "dur": 485, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526578459, "dur": 1346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753279526579805, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526579898, "dur": 68212, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526648690, "dur": 532, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526648118, "dur": 2817, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753279526650936, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526651346, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526652211, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526651039, "dur": 2078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753279526653118, "dur": 1582, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526655319, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526656260, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Principal.Windows.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526656397, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Burst.CodeGen.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526654704, "dur": 2622, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753279526657326, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526659370, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526659597, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526659829, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526659926, "dur": 595, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753279526657518, "dur": 3011, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753279526660530, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753279526660683, "dur": 4724362, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526527572, "dur": 17832, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526545429, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753279526545613, "dur": 1042, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1753279526545407, "dur": 1249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4012322E9A5614BC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753279526546785, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526546907, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753279526546907, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_31272CE89BD91E91.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753279526547023, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526547090, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753279526547088, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753279526547167, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526547357, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526547420, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526547501, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526547720, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526547878, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526548023, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526548217, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526548536, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526548748, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526548805, "dur": 1705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526550511, "dur": 1357, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526551868, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526553271, "dur": 1734, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526555006, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526557592, "dur": 836, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Serialization\\JsonObject.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753279526556809, "dur": 2113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526558923, "dur": 965, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526559888, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526561026, "dur": 1341, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526562368, "dur": 1212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526563580, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526564737, "dur": 1460, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526566197, "dur": 1873, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526568071, "dur": 1931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526570003, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526571677, "dur": 1547, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526573224, "dur": 1498, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526574722, "dur": 456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526575181, "dur": 628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753279526575857, "dur": 400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753279526576257, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526576389, "dur": 661, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753279526577050, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526577465, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753279526577137, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753279526577773, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526577924, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.2D.IK.Runtime.ref.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753279526577922, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.2D.IK.Runtime.ref.dll_16F22BA31A1D34C0.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753279526578094, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526578210, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753279526578767, "dur": 368, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753279526578350, "dur": 991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753279526579342, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526579696, "dur": 68397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526650036, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753279526648095, "dur": 2443, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753279526650538, "dur": 701, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526651346, "dur": 828, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753279526654280, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753279526651250, "dur": 3322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753279526654573, "dur": 1676, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526656258, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEditor.UI.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753279526656257, "dur": 2305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753279526658567, "dur": 813, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526659425, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526659488, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526659547, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526660155, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526660256, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753279526660331, "dur": 4724722, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526527592, "dur": 17818, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526545443, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526545619, "dur": 944, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1753279526545415, "dur": 1148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_7C296F75F94E46F6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753279526546564, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526546657, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526546656, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_E5A3FD65734E3273.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753279526546800, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526547082, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526547081, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753279526547140, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526547328, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526547490, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526547557, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526547619, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526547765, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526547825, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526548171, "dur": 251, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526548422, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp2"}}, {"pid": 12345, "tid": 16, "ts": 1753279526548755, "dur": 943, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526549698, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526551841, "dur": 620, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\inspectors\\DirectorNamedColorInspector.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753279526551030, "dur": 2840, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526553870, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526555115, "dur": 1899, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526557612, "dur": 970, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\TargetResources\\Fields.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753279526557014, "dur": 2345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526559359, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526560804, "dur": 1063, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526561867, "dur": 1165, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526563033, "dur": 2229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526565262, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526566760, "dur": 1831, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526568591, "dur": 1779, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526570371, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526571425, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526573005, "dur": 1708, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526574713, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526575176, "dur": 461, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753279526575638, "dur": 1352, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526577574, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\Interface\\ISpriteEditorDataProvider.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753279526576994, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753279526577751, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526577854, "dur": 331, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526578193, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753279526578400, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526578513, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526578614, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526579121, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.SecureString.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526578487, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753279526579476, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526579621, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526579679, "dur": 32808, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526612488, "dur": 35589, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526649533, "dur": 227, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.ServicePoint.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526649844, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526650524, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526648084, "dur": 2943, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753279526651028, "dur": 380, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526651666, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526653198, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.DataAnnotations.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526653497, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipelines.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526653892, "dur": 441, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526654923, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526655305, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526651419, "dur": 4045, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753279526655465, "dur": 396, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526656533, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526656863, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-environment-l1-1-0.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526657414, "dur": 1327, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipelines.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526658930, "dur": 455, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526659598, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753279526655870, "dur": 4617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753279526660488, "dur": 131, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753279526660682, "dur": 4724422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526527623, "dur": 17805, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526545439, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753279526545625, "dur": 733, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1753279526545434, "dur": 925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_826CA845B2B614E1.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753279526546359, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526546467, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753279526546465, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_6D0709FBC14ECAA8.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753279526546575, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526546750, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526546911, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753279526546910, "dur": 143, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_9AAEFD6A746FD785.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753279526547206, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526547393, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526547544, "dur": 293, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526547877, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1753279526547970, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526548766, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526548840, "dur": 1244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526550084, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526551647, "dur": 1869, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526553516, "dur": 1870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526555386, "dur": 1725, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526557601, "dur": 826, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Descriptors\\IncludeDescriptor.cs"}}, {"pid": 12345, "tid": 17, "ts": 1753279526557112, "dur": 1880, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526558993, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526560037, "dur": 1446, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526561484, "dur": 1032, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526562516, "dur": 1616, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526564132, "dur": 1463, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526565596, "dur": 1935, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526567532, "dur": 2097, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526569630, "dur": 1406, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526571037, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526572233, "dur": 1769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526574002, "dur": 726, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526574728, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526575185, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753279526575602, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526575987, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753279526576113, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753279526575670, "dur": 867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753279526576538, "dur": 1489, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526578041, "dur": 447, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526578493, "dur": 148, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526578646, "dur": 2313, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526580960, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753279526581053, "dur": 554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753279526581608, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526581696, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526581762, "dur": 69298, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526651152, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753279526651345, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753279526652218, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Caching.Abstractions.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753279526652764, "dur": 296, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753279526651061, "dur": 2666, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753279526653728, "dur": 135, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526654923, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Caching.Memory.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753279526655297, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753279526653868, "dur": 2893, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753279526656761, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526658725, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753279526656887, "dur": 2208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753279526659096, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526659287, "dur": 477, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526659807, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526659947, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526660061, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526660293, "dur": 877, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526661174, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753279526661233, "dur": 4723861, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526527651, "dur": 17793, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526545467, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526545620, "dur": 735, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1753279526545452, "dur": 903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4F40831771D8FBF5.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753279526546410, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526546409, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D218FBB45FD12FE4.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753279526546470, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526546548, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526546546, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_39D3561411FEB126.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753279526546880, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526546880, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_46FFF6FD196ECAB7.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753279526547002, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526547092, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526547180, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526547384, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526547460, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526547552, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526547633, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526547721, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526547984, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526548112, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526548416, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526548521, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753279526548759, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526548844, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526549980, "dur": 1395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526551375, "dur": 2420, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526553796, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526555643, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526557553, "dur": 897, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\Fullscreen\\FullscreenSubTarget.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753279526556812, "dur": 2117, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526558930, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526560368, "dur": 1332, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526561701, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526562555, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526563723, "dur": 1857, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526565581, "dur": 1998, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526567580, "dur": 1783, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526569364, "dur": 1394, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526570759, "dur": 1169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526571928, "dur": 1947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526573876, "dur": 826, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526574752, "dur": 451, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526575210, "dur": 296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753279526575507, "dur": 443, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526576218, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526575953, "dur": 757, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753279526576711, "dur": 238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526576955, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753279526577207, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526577294, "dur": 441, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753279526577736, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526577843, "dur": 191, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526578040, "dur": 242, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753279526578282, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526578768, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526579121, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526579308, "dur": 198, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526578336, "dur": 1422, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753279526579758, "dur": 213, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526579983, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526580039, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753279526580162, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526580215, "dur": 405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753279526580621, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526580830, "dur": 67228, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526648060, "dur": 2296, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753279526650364, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526651002, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526651345, "dur": 844, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526653841, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526650487, "dur": 3775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753279526654263, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526655304, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526656260, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Unity.ILPP.Runner.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526654455, "dur": 2763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753279526657218, "dur": 1807, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526659369, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526659511, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526659646, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526659929, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526660136, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753279526659036, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753279526661228, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753279526661334, "dur": 4723803, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526527676, "dur": 17781, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526545471, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526545566, "dur": 1035, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1753279526545463, "dur": 1139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_4245840F5C4641AB.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753279526546602, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526546859, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526546858, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_7406CA3F32B5E22E.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753279526546919, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526547259, "dur": 315, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526547258, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_8F519FE4E54F1B21.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753279526547925, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526548124, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526548313, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526548396, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526548529, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526548611, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526549564, "dur": 644, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.OpenSsl.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526548800, "dur": 1894, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526550694, "dur": 2713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526553408, "dur": 2004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526555412, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526557602, "dur": 852, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Ports\\UnitInputPortWidget.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753279526556154, "dur": 2753, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526558907, "dur": 1836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526560744, "dur": 1428, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526562173, "dur": 1170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526563343, "dur": 2080, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526565423, "dur": 1331, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526566755, "dur": 1339, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526568094, "dur": 1594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526569688, "dur": 1227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526570915, "dur": 974, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526571890, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526573739, "dur": 972, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526574712, "dur": 462, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526575175, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753279526575312, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526576115, "dur": 361, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\Prefabs\\Scripts\\UIFoldout.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753279526575539, "dur": 1188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753279526576728, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526576888, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526576947, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753279526577363, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526577573, "dur": 386, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526578003, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526577428, "dur": 907, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753279526578336, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526578532, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753279526578699, "dur": 653, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526579355, "dur": 717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753279526580073, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526580252, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526580591, "dur": 83, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753279526580676, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526580765, "dur": 251, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753279526581016, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526581136, "dur": 66985, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526648262, "dur": 2750, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.JSONSerializeModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526651346, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526653112, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526648126, "dur": 5336, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753279526653463, "dur": 1471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526654960, "dur": 358, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526655324, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526656256, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.Core.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526656851, "dur": 580, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526654943, "dur": 3589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753279526658533, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753279526658929, "dur": 296, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526659891, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\clrjit.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753279526658666, "dur": 2314, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753279526661052, "dur": 4724046, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526527699, "dur": 17767, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526545481, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753279526545554, "dur": 988, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1753279526545467, "dur": 1076, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_B5C2173E9AD0B5D6.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753279526546544, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526546620, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753279526546618, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreFontEngineModule.dll_758CA54D648AA1BC.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753279526546768, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753279526546767, "dur": 129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E81E4251EABAE467.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753279526546897, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526547269, "dur": 311, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Mdb.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753279526547268, "dur": 315, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Mdb.dll_CB8452BAE2BF35AA.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753279526547583, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526547777, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526547840, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526548001, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526548091, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526548795, "dur": 2008, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526550803, "dur": 2689, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526553493, "dur": 1818, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526555312, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526557553, "dur": 901, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Util\\UIUtilities.cs"}}, {"pid": 12345, "tid": 20, "ts": 1753279526556723, "dur": 2263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526558986, "dur": 1897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526560884, "dur": 1457, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526562341, "dur": 2009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526564350, "dur": 1632, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526565982, "dur": 2290, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526568272, "dur": 1730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526570002, "dur": 1601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526572620, "dur": 577, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Runtime\\Editor\\BurstCompileTarget.cs"}}, {"pid": 12345, "tid": 20, "ts": 1753279526571603, "dur": 1695, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526573299, "dur": 1422, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526574722, "dur": 512, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526575237, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753279526575596, "dur": 538, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526576138, "dur": 719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753279526576857, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526577062, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753279526577274, "dur": 692, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526578266, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753279526577971, "dur": 743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753279526578715, "dur": 1689, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526580409, "dur": 67667, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526649204, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HttpLogging.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753279526649406, "dur": 182, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.DataAnnotations.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753279526649913, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753279526651054, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753279526648079, "dur": 3445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753279526651525, "dur": 1059, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526652593, "dur": 2291, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753279526654885, "dur": 1971, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526656865, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.2D.Animation.Editor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753279526657148, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753279526656864, "dur": 2195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753279526659060, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526659239, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526659443, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526659569, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526659623, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526660221, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526660282, "dur": 626, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526660912, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753279526660987, "dur": 4724068, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753279531392060, "dur": 3800, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21312, "tid": 8381, "ts": 1753279531397226, "dur": 919, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21312, "tid": 8381, "ts": 1753279531398205, "dur": 21215, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21312, "tid": 8381, "ts": 1753279531396292, "dur": 23164, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}