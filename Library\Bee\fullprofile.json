{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21312, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21312, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21312, "tid": 8828, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21312, "tid": 8828, "ts": 1753300582945146, "dur": 535, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21312, "tid": 8828, "ts": 1753300582948266, "dur": 795, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21312, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21312, "tid": 1, "ts": 1753300580245445, "dur": 7905, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21312, "tid": 1, "ts": 1753300580253353, "dur": 64227, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21312, "tid": 1, "ts": 1753300580317588, "dur": 42325, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21312, "tid": 8828, "ts": 1753300582949064, "dur": 9, "ph": "X", "name": "", "args": {}}, {"pid": 21312, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580241249, "dur": 122, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580241372, "dur": 2695544, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580242006, "dur": 2963, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580244974, "dur": 1268, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580246245, "dur": 230, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580246478, "dur": 9, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580246487, "dur": 120, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580246609, "dur": 1, "ph": "X", "name": "ProcessMessages 1403", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580246611, "dur": 114, "ph": "X", "name": "ReadAsync 1403", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580246726, "dur": 1, "ph": "X", "name": "ProcessMessages 1492", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580246727, "dur": 106, "ph": "X", "name": "ReadAsync 1492", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580246835, "dur": 1, "ph": "X", "name": "ProcessMessages 2023", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580246837, "dur": 23, "ph": "X", "name": "ReadAsync 2023", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580246863, "dur": 18, "ph": "X", "name": "ReadAsync 1043", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580246883, "dur": 141, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247027, "dur": 116, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247145, "dur": 2, "ph": "X", "name": "ProcessMessages 3603", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247147, "dur": 116, "ph": "X", "name": "ReadAsync 3603", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247264, "dur": 1, "ph": "X", "name": "ProcessMessages 2915", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247273, "dur": 93, "ph": "X", "name": "ReadAsync 2915", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247367, "dur": 1, "ph": "X", "name": "ProcessMessages 2756", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247369, "dur": 117, "ph": "X", "name": "ReadAsync 2756", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247488, "dur": 1, "ph": "X", "name": "ProcessMessages 1962", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247490, "dur": 103, "ph": "X", "name": "ReadAsync 1962", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247594, "dur": 1, "ph": "X", "name": "ProcessMessages 3188", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247597, "dur": 109, "ph": "X", "name": "ReadAsync 3188", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247707, "dur": 1, "ph": "X", "name": "ProcessMessages 2221", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247709, "dur": 38, "ph": "X", "name": "ReadAsync 2221", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247749, "dur": 1, "ph": "X", "name": "ProcessMessages 2418", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247751, "dur": 27, "ph": "X", "name": "ReadAsync 2418", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247780, "dur": 23, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247805, "dur": 20, "ph": "X", "name": "ReadAsync 706", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247828, "dur": 17, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247847, "dur": 30, "ph": "X", "name": "ReadAsync 343", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247880, "dur": 18, "ph": "X", "name": "ReadAsync 1190", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247901, "dur": 17, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247920, "dur": 23, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247945, "dur": 19, "ph": "X", "name": "ReadAsync 513", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247967, "dur": 18, "ph": "X", "name": "ReadAsync 799", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580247988, "dur": 18, "ph": "X", "name": "ReadAsync 703", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248008, "dur": 16, "ph": "X", "name": "ReadAsync 688", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248026, "dur": 18, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248046, "dur": 16, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248069, "dur": 24, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248096, "dur": 47, "ph": "X", "name": "ReadAsync 736", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248146, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248172, "dur": 19, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248193, "dur": 19, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248214, "dur": 10, "ph": "X", "name": "ProcessMessages 593", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248224, "dur": 18, "ph": "X", "name": "ReadAsync 593", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248245, "dur": 19, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248266, "dur": 21, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248290, "dur": 18, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248311, "dur": 16, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248330, "dur": 29, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248368, "dur": 180, "ph": "X", "name": "ReadAsync 984", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248551, "dur": 1, "ph": "X", "name": "ProcessMessages 924", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248553, "dur": 26, "ph": "X", "name": "ReadAsync 924", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248581, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248583, "dur": 129, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248714, "dur": 1, "ph": "X", "name": "ProcessMessages 778", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248715, "dur": 38, "ph": "X", "name": "ReadAsync 778", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248755, "dur": 2, "ph": "X", "name": "ProcessMessages 3193", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248757, "dur": 21, "ph": "X", "name": "ReadAsync 3193", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248781, "dur": 44, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248827, "dur": 20, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248850, "dur": 24, "ph": "X", "name": "ReadAsync 631", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248877, "dur": 19, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248899, "dur": 35, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248937, "dur": 21, "ph": "X", "name": "ReadAsync 914", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248960, "dur": 21, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580248984, "dur": 23, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249028, "dur": 26, "ph": "X", "name": "ReadAsync 614", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249055, "dur": 1, "ph": "X", "name": "ProcessMessages 1211", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249057, "dur": 31, "ph": "X", "name": "ReadAsync 1211", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249090, "dur": 18, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249111, "dur": 26, "ph": "X", "name": "ReadAsync 673", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249139, "dur": 21, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249163, "dur": 16, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249182, "dur": 43, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249227, "dur": 20, "ph": "X", "name": "ReadAsync 1020", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249249, "dur": 20, "ph": "X", "name": "ReadAsync 545", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249272, "dur": 21, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249295, "dur": 32, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249330, "dur": 19, "ph": "X", "name": "ReadAsync 784", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249353, "dur": 1, "ph": "X", "name": "ProcessMessages 207", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249354, "dur": 37, "ph": "X", "name": "ReadAsync 207", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249393, "dur": 1, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249395, "dur": 27, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249424, "dur": 1, "ph": "X", "name": "ProcessMessages 1153", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249426, "dur": 24, "ph": "X", "name": "ReadAsync 1153", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249455, "dur": 72, "ph": "X", "name": "ReadAsync 268", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249530, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249532, "dur": 38, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249571, "dur": 1, "ph": "X", "name": "ProcessMessages 2117", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249573, "dur": 20, "ph": "X", "name": "ReadAsync 2117", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249596, "dur": 20, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249618, "dur": 34, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249655, "dur": 24, "ph": "X", "name": "ReadAsync 789", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249682, "dur": 25, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249709, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249710, "dur": 25, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249738, "dur": 18, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249772, "dur": 32, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249806, "dur": 1, "ph": "X", "name": "ProcessMessages 1216", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249813, "dur": 26, "ph": "X", "name": "ReadAsync 1216", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249842, "dur": 34, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249880, "dur": 17, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249899, "dur": 30, "ph": "X", "name": "ReadAsync 925", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249932, "dur": 28, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249962, "dur": 4, "ph": "X", "name": "ProcessMessages 800", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249967, "dur": 27, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249995, "dur": 1, "ph": "X", "name": "ProcessMessages 1017", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580249997, "dur": 22, "ph": "X", "name": "ReadAsync 1017", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250021, "dur": 1, "ph": "X", "name": "ProcessMessages 874", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250022, "dur": 34, "ph": "X", "name": "ReadAsync 874", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250060, "dur": 80, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250143, "dur": 1, "ph": "X", "name": "ProcessMessages 767", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250145, "dur": 43, "ph": "X", "name": "ReadAsync 767", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250190, "dur": 2, "ph": "X", "name": "ProcessMessages 2439", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250193, "dur": 30, "ph": "X", "name": "ReadAsync 2439", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250234, "dur": 202, "ph": "X", "name": "ReadAsync 239", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250442, "dur": 2, "ph": "X", "name": "ProcessMessages 4454", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250445, "dur": 93, "ph": "X", "name": "ReadAsync 4454", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250540, "dur": 1, "ph": "X", "name": "ProcessMessages 2122", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250551, "dur": 92, "ph": "X", "name": "ReadAsync 2122", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250646, "dur": 1, "ph": "X", "name": "ProcessMessages 2757", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250649, "dur": 101, "ph": "X", "name": "ReadAsync 2757", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250755, "dur": 1, "ph": "X", "name": "ProcessMessages 1119", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580250976, "dur": 104, "ph": "X", "name": "ReadAsync 1119", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251084, "dur": 5, "ph": "X", "name": "ProcessMessages 9579", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251089, "dur": 184, "ph": "X", "name": "ReadAsync 9579", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251275, "dur": 12, "ph": "X", "name": "ProcessMessages 989", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251289, "dur": 49, "ph": "X", "name": "ReadAsync 989", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251339, "dur": 2, "ph": "X", "name": "ProcessMessages 4382", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251342, "dur": 28, "ph": "X", "name": "ReadAsync 4382", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251371, "dur": 1, "ph": "X", "name": "ProcessMessages 1000", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251373, "dur": 21, "ph": "X", "name": "ReadAsync 1000", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251397, "dur": 23, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251422, "dur": 29, "ph": "X", "name": "ReadAsync 717", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251455, "dur": 19, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251476, "dur": 20, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251500, "dur": 22, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251529, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251531, "dur": 29, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251563, "dur": 25, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251590, "dur": 34, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251626, "dur": 1, "ph": "X", "name": "ProcessMessages 1149", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251627, "dur": 33, "ph": "X", "name": "ReadAsync 1149", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251663, "dur": 1, "ph": "X", "name": "ProcessMessages 902", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251664, "dur": 38, "ph": "X", "name": "ReadAsync 902", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251704, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251706, "dur": 19, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251728, "dur": 20, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251751, "dur": 19, "ph": "X", "name": "ReadAsync 793", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251773, "dur": 47, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251823, "dur": 20, "ph": "X", "name": "ReadAsync 1395", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251846, "dur": 17, "ph": "X", "name": "ReadAsync 768", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251865, "dur": 20, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251888, "dur": 17, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251908, "dur": 22, "ph": "X", "name": "ReadAsync 285", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251933, "dur": 19, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251955, "dur": 20, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580251978, "dur": 21, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252002, "dur": 35, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252038, "dur": 1, "ph": "X", "name": "ProcessMessages 896", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252040, "dur": 19, "ph": "X", "name": "ReadAsync 896", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252062, "dur": 24, "ph": "X", "name": "ReadAsync 659", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252088, "dur": 7, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252095, "dur": 23, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252120, "dur": 1, "ph": "X", "name": "ProcessMessages 883", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252122, "dur": 34, "ph": "X", "name": "ReadAsync 883", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252159, "dur": 17, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252178, "dur": 17, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252198, "dur": 29, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252229, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252251, "dur": 19, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252272, "dur": 55, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252330, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252358, "dur": 1, "ph": "X", "name": "ProcessMessages 807", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252359, "dur": 17, "ph": "X", "name": "ReadAsync 807", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252379, "dur": 42, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252423, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252444, "dur": 22, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252468, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252469, "dur": 46, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252518, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252545, "dur": 18, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252566, "dur": 42, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252610, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252659, "dur": 1, "ph": "X", "name": "ProcessMessages 1051", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252661, "dur": 37, "ph": "X", "name": "ReadAsync 1051", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252705, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252729, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252730, "dur": 22, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252759, "dur": 33, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252794, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252822, "dur": 19, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252848, "dur": 36, "ph": "X", "name": "ReadAsync 552", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252886, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252915, "dur": 1, "ph": "X", "name": "ProcessMessages 758", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252916, "dur": 15, "ph": "X", "name": "ReadAsync 758", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252934, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580252977, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253007, "dur": 19, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253029, "dur": 51, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253082, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253107, "dur": 18, "ph": "X", "name": "ReadAsync 695", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253133, "dur": 36, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253171, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253201, "dur": 17, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253221, "dur": 17, "ph": "X", "name": "ReadAsync 568", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253241, "dur": 34, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253277, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253301, "dur": 19, "ph": "X", "name": "ReadAsync 751", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253323, "dur": 40, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253365, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253389, "dur": 19, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253411, "dur": 39, "ph": "X", "name": "ReadAsync 491", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253452, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253477, "dur": 18, "ph": "X", "name": "ReadAsync 759", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253497, "dur": 47, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253547, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253575, "dur": 18, "ph": "X", "name": "ReadAsync 868", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253595, "dur": 39, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253637, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253661, "dur": 19, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253683, "dur": 110, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253797, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253836, "dur": 1, "ph": "X", "name": "ProcessMessages 1303", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253844, "dur": 21, "ph": "X", "name": "ReadAsync 1303", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253867, "dur": 28, "ph": "X", "name": "ReadAsync 241", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253897, "dur": 78, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580253978, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254003, "dur": 1, "ph": "X", "name": "ProcessMessages 982", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254004, "dur": 24, "ph": "X", "name": "ReadAsync 982", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254033, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254071, "dur": 1, "ph": "X", "name": "ProcessMessages 809", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254073, "dur": 81, "ph": "X", "name": "ReadAsync 809", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254157, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254159, "dur": 26, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254188, "dur": 84, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254276, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254277, "dur": 36, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254315, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254317, "dur": 17, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254337, "dur": 51, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254390, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254413, "dur": 22, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254438, "dur": 94, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254536, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254567, "dur": 1, "ph": "X", "name": "ProcessMessages 1030", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254568, "dur": 26, "ph": "X", "name": "ReadAsync 1030", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254597, "dur": 18, "ph": "X", "name": "ReadAsync 795", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254617, "dur": 9, "ph": "X", "name": "ProcessMessages 305", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254626, "dur": 30, "ph": "X", "name": "ReadAsync 305", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254659, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254683, "dur": 22, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254707, "dur": 23, "ph": "X", "name": "ReadAsync 623", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254732, "dur": 30, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254764, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254787, "dur": 28, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254826, "dur": 45, "ph": "X", "name": "ReadAsync 999", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254874, "dur": 18, "ph": "X", "name": "ReadAsync 1107", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254895, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254917, "dur": 58, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580254977, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255003, "dur": 26, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255032, "dur": 52, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255096, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255120, "dur": 1, "ph": "X", "name": "ProcessMessages 860", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255121, "dur": 18, "ph": "X", "name": "ReadAsync 860", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255141, "dur": 52, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255195, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255231, "dur": 1, "ph": "X", "name": "ProcessMessages 958", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255232, "dur": 17, "ph": "X", "name": "ReadAsync 958", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255251, "dur": 39, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255293, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255318, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255320, "dur": 20, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255342, "dur": 42, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255387, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255412, "dur": 18, "ph": "X", "name": "ReadAsync 808", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255433, "dur": 46, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255482, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255504, "dur": 21, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255528, "dur": 48, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255578, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255605, "dur": 1, "ph": "X", "name": "ProcessMessages 665", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255606, "dur": 30, "ph": "X", "name": "ReadAsync 665", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255640, "dur": 30, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255672, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255696, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255698, "dur": 26, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255731, "dur": 16, "ph": "X", "name": "ReadAsync 562", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255750, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255784, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255807, "dur": 19, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255828, "dur": 46, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255877, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255897, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255898, "dur": 20, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255921, "dur": 53, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580255976, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256010, "dur": 17, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256030, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256075, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256110, "dur": 17, "ph": "X", "name": "ReadAsync 820", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256135, "dur": 24, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256162, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256183, "dur": 18, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256203, "dur": 19, "ph": "X", "name": "ReadAsync 595", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256225, "dur": 33, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256260, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256280, "dur": 535, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256817, "dur": 1, "ph": "X", "name": "ProcessMessages 2697", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256819, "dur": 103, "ph": "X", "name": "ReadAsync 2697", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256924, "dur": 3, "ph": "X", "name": "ProcessMessages 6538", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256928, "dur": 22, "ph": "X", "name": "ReadAsync 6538", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256951, "dur": 1, "ph": "X", "name": "ProcessMessages 1120", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256953, "dur": 25, "ph": "X", "name": "ReadAsync 1120", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580256980, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257003, "dur": 1, "ph": "X", "name": "ProcessMessages 607", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257004, "dur": 21, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257028, "dur": 38, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257068, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257110, "dur": 17, "ph": "X", "name": "ReadAsync 712", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257130, "dur": 24, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257156, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257177, "dur": 28, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257208, "dur": 40, "ph": "X", "name": "ReadAsync 592", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257261, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257283, "dur": 1, "ph": "X", "name": "ProcessMessages 1019", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257285, "dur": 16, "ph": "X", "name": "ReadAsync 1019", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257304, "dur": 36, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257342, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257363, "dur": 19, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257384, "dur": 42, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257429, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257468, "dur": 18, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257488, "dur": 44, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257535, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257571, "dur": 18, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257591, "dur": 20, "ph": "X", "name": "ReadAsync 438", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257613, "dur": 19, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257641, "dur": 18, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257661, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257680, "dur": 38, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257721, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257745, "dur": 18, "ph": "X", "name": "ReadAsync 657", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257766, "dur": 41, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257809, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257830, "dur": 19, "ph": "X", "name": "ReadAsync 466", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257852, "dur": 48, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257902, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257923, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257944, "dur": 44, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580257990, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258012, "dur": 19, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258034, "dur": 17, "ph": "X", "name": "ReadAsync 635", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258053, "dur": 54, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258110, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258179, "dur": 1, "ph": "X", "name": "ProcessMessages 1667", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258180, "dur": 23, "ph": "X", "name": "ReadAsync 1667", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258206, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258208, "dur": 19, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258230, "dur": 17, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258250, "dur": 18, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258270, "dur": 60, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258332, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258354, "dur": 20, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258377, "dur": 33, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258413, "dur": 21, "ph": "X", "name": "ReadAsync 942", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258436, "dur": 1, "ph": "X", "name": "ProcessMessages 477", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258438, "dur": 35, "ph": "X", "name": "ReadAsync 477", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258478, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258480, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258516, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258548, "dur": 257, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258829, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580258846, "dur": 328, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580259181, "dur": 208, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580259391, "dur": 799, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260195, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260215, "dur": 91, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260309, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260311, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260365, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260367, "dur": 26, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260397, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260399, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260431, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260485, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260487, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260522, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260524, "dur": 43, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260570, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260573, "dur": 45, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260620, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260623, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260655, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260657, "dur": 30, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260690, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260691, "dur": 43, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260742, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260744, "dur": 30, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260777, "dur": 1, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260779, "dur": 32, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260818, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260862, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580260874, "dur": 142, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261020, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261048, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261092, "dur": 2, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261095, "dur": 24, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261123, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261126, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261163, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261165, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261222, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261225, "dur": 51, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261278, "dur": 1, "ph": "X", "name": "ProcessMessages 172", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261281, "dur": 92, "ph": "X", "name": "ReadAsync 172", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261377, "dur": 2, "ph": "X", "name": "ProcessMessages 164", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261380, "dur": 51, "ph": "X", "name": "ReadAsync 164", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261434, "dur": 3, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261438, "dur": 38, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261480, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261482, "dur": 54, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261540, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261543, "dur": 66, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261611, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261614, "dur": 32, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261648, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261650, "dur": 22, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261674, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261676, "dur": 23, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261702, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261703, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261737, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261739, "dur": 55, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261796, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261798, "dur": 43, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261844, "dur": 1, "ph": "X", "name": "ProcessMessages 228", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261846, "dur": 27, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261875, "dur": 10, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261886, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261925, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261927, "dur": 36, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261965, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580261967, "dur": 78, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262048, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262052, "dur": 44, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262099, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262102, "dur": 38, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262142, "dur": 2, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262145, "dur": 40, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262189, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262191, "dur": 36, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262230, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262232, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262260, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262262, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262294, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262296, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262328, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262330, "dur": 31, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262363, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262365, "dur": 37, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262404, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262406, "dur": 65, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262475, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262477, "dur": 27, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262506, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262507, "dur": 31, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262540, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262542, "dur": 27, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262572, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262575, "dur": 44, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262621, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262623, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262662, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262664, "dur": 27, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262694, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262696, "dur": 40, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262739, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262741, "dur": 33, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262777, "dur": 1, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262780, "dur": 40, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262824, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262887, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580262889, "dur": 19313, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580282220, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580282229, "dur": 71, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580282306, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580282308, "dur": 1912, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580284226, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580284229, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580284289, "dur": 5, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580284303, "dur": 41, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580284347, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580284350, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580284391, "dur": 1246, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580285648, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580285650, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580285686, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580285688, "dur": 49, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580285742, "dur": 13, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580285762, "dur": 421, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286194, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286223, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286277, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286279, "dur": 130, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286416, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286465, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286471, "dur": 44, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286518, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286520, "dur": 35, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286561, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286563, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286600, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286641, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286643, "dur": 44, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286696, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286699, "dur": 14, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286716, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286763, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286782, "dur": 88, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286880, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286893, "dur": 58, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580286958, "dur": 122, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287083, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287093, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287124, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287126, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287164, "dur": 8, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287174, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287210, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287240, "dur": 10, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287251, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287305, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287355, "dur": 6, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287362, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287412, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287445, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287447, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287532, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287561, "dur": 53, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287618, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287670, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287672, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287737, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287739, "dur": 20, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287762, "dur": 61, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287831, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287885, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287888, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287913, "dur": 64, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580287993, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288041, "dur": 24, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288074, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288076, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288109, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288159, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288190, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288230, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288272, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288310, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288312, "dur": 61, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288377, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288410, "dur": 10, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288421, "dur": 36, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288464, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288465, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288509, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288511, "dur": 19, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288533, "dur": 16, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288552, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288579, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288581, "dur": 43, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288630, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288632, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288668, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288741, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288749, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288787, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288789, "dur": 47, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288847, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288903, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288904, "dur": 27, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288941, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580288943, "dur": 86, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289037, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289038, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289071, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289074, "dur": 19, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289096, "dur": 36, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289135, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289178, "dur": 49, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289249, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289287, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289297, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289355, "dur": 15, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289371, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289432, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289483, "dur": 5, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289489, "dur": 160, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289654, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289697, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289699, "dur": 26, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289738, "dur": 53, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289803, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289805, "dur": 63, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289873, "dur": 8, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289888, "dur": 42, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289949, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289981, "dur": 3, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580289993, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290037, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290039, "dur": 73, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290123, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290125, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290184, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290217, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290244, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290288, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290332, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290334, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290363, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290365, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290407, "dur": 41, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580290455, "dur": 3594, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580294054, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580294057, "dur": 104, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580294162, "dur": 477, "ph": "X", "name": "ProcessMessages 1032", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580294670, "dur": 63758, "ph": "X", "name": "ReadAsync 1032", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580358436, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580358439, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580358481, "dur": 2109, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580360595, "dur": 6814, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367418, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367423, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367463, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367465, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367499, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367501, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367534, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367536, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367565, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367566, "dur": 281, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367854, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367857, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367884, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580367886, "dur": 375, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368266, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368268, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368303, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368306, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368495, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368519, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368521, "dur": 209, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368734, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368763, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368765, "dur": 65, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368833, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368836, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368876, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580368877, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580369018, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580369019, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580369056, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580369097, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580369098, "dur": 187, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580369291, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580369320, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580369323, "dur": 926, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580370365, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580370368, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580370414, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580370417, "dur": 169, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580370591, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580370645, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580370647, "dur": 224, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580370876, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580370913, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580370915, "dur": 517, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580371437, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580371440, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580371478, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580371480, "dur": 208, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580371691, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580371693, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580371726, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580371729, "dur": 294, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372027, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372029, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372061, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372062, "dur": 352, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372419, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372421, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372462, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372464, "dur": 84, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372551, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372553, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372590, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372622, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372624, "dur": 78, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372705, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372707, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372739, "dur": 48, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372790, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372791, "dur": 86, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372882, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580372913, "dur": 148, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580373065, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580373094, "dur": 568, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580373665, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580373667, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580373709, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580373711, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580373760, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580373762, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580373794, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580373796, "dur": 414, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580374213, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580374216, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580374253, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580374255, "dur": 175, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580374432, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580374435, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580374465, "dur": 167, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580374637, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580374669, "dur": 125, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580374799, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580374830, "dur": 390, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375223, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375225, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375261, "dur": 277, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375542, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375544, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375568, "dur": 217, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375791, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375793, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375817, "dur": 38, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375860, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375895, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375934, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375935, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375968, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580375970, "dur": 30, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376003, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376004, "dur": 31, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376038, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376040, "dur": 28, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376071, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376073, "dur": 32, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376110, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376140, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376169, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376205, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376207, "dur": 27, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376236, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376238, "dur": 37, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376285, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376287, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376330, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376333, "dur": 30, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376366, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376368, "dur": 22, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376394, "dur": 24, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376422, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376456, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376457, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376490, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376492, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376532, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376533, "dur": 27, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376564, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376595, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376623, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376625, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376657, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376684, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376686, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376716, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376718, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376752, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376784, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376785, "dur": 26, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376815, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376843, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376845, "dur": 29, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376878, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376910, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376912, "dur": 18, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376936, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376938, "dur": 44, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580376988, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377024, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377025, "dur": 38, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377066, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377068, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377115, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377118, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377155, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377157, "dur": 181, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377343, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377423, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377425, "dur": 71, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377500, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377503, "dur": 89, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377596, "dur": 95, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377694, "dur": 148, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377846, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377849, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377871, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580377872, "dur": 574, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580378453, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580378455, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580378492, "dur": 95539, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580474038, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580474041, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580474136, "dur": 3706, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580477846, "dur": 21732, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580499585, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580499588, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300580499613, "dur": 844849, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581344467, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581344471, "dur": 82, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581344560, "dur": 4, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581344566, "dur": 473617, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581818192, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581818195, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581818231, "dur": 25, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581818258, "dur": 2112, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581820382, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581820387, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581820420, "dur": 35, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581820456, "dur": 15791, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581836255, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581836260, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581836282, "dur": 1211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581837497, "dur": 75, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581837575, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581837579, "dur": 1408, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581838991, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581839019, "dur": 19, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581839039, "dur": 115977, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581955024, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581955032, "dur": 85, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581955122, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300581955124, "dur": 969321, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300582924452, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300582924455, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300582924501, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300582924504, "dur": 1645, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300582926154, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300582926157, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300582926208, "dur": 20, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300582926229, "dur": 821, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300582927056, "dur": 122, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300582927181, "dur": 467, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753300582927659, "dur": 9146, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21312, "tid": 8828, "ts": 1753300582949074, "dur": 802, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21312, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21312, "tid": 8589934592, "ts": 1753300580238735, "dur": 121205, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21312, "tid": 8589934592, "ts": 1753300580359942, "dur": 3, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21312, "tid": 8589934592, "ts": 1753300580359946, "dur": 916, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21312, "tid": 8828, "ts": 1753300582949877, "dur": 4, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21312, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21312, "tid": 4294967296, "ts": 1753300580146771, "dur": 2791265, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21312, "tid": 4294967296, "ts": 1753300580149956, "dur": 7184, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21312, "tid": 4294967296, "ts": 1753300582938053, "dur": 4989, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21312, "tid": 4294967296, "ts": 1753300582940475, "dur": 102, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21312, "tid": 4294967296, "ts": 1753300582943098, "dur": 8, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21312, "tid": 8828, "ts": 1753300582949883, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753300580179103, "dur": 1665, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753300580180777, "dur": 620, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753300580181518, "dur": 58, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753300580181576, "dur": 529, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753300580182795, "dur": 60394, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D293A48779142A71.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753300580244059, "dur": 2319, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D2F5C6CBE8D78A56.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753300580250941, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1753300580182126, "dur": 76345, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753300580258483, "dur": 2667777, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753300582926261, "dur": 455, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753300582926939, "dur": 1663, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753300580182155, "dur": 76338, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580258516, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580258647, "dur": 1756, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1753300580258604, "dur": 1799, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FA85EF908E5A33BF.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753300580260527, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580260526, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_41265C97D041BE6E.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753300580260591, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580260781, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580260780, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_9F8263619ADFDEFD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753300580260945, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580261155, "dur": 271, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580261155, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_62273FC84B3E0F2D.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753300580261430, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580261708, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580261908, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580261969, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580262121, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580262670, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580263838, "dur": 2167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580266005, "dur": 1362, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580267367, "dur": 1486, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580268853, "dur": 1014, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580270498, "dur": 579, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\ReorderableTextListView.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753300580269867, "dur": 2220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580272087, "dur": 775, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580272862, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580273905, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580274853, "dur": 1865, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580276718, "dur": 1586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580278305, "dur": 1659, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580279965, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580281278, "dur": 1625, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580282903, "dur": 997, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580283901, "dur": 282, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580284184, "dur": 1413, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580285597, "dur": 615, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580286212, "dur": 153, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753300580286365, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580286449, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753300580286782, "dur": 276, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580286697, "dur": 997, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753300580287695, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580287986, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580288135, "dur": 189, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580288328, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580288406, "dur": 224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753300580288631, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580288687, "dur": 1064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753300580289751, "dur": 150, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580289951, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580290089, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753300580290220, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580290275, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753300580290757, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580290961, "dur": 86, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753300580291084, "dur": 385, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753300580291470, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580291626, "dur": 73222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580364993, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580367419, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580367524, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\UnityEngine.TestRunner.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580364849, "dur": 3660, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753300580368510, "dur": 3099, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580371618, "dur": 2833, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753300580374452, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580374723, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580374875, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580375809, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580376010, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.WebSockets.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580376138, "dur": 209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Hosting.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580376380, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.DataAnnotations.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580376676, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Http.Json.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753300580374581, "dur": 2791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753300580377373, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753300580377487, "dur": 2548750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580182235, "dur": 76299, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580258550, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753300580258609, "dur": 1990, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 2, "ts": 1753300580258542, "dur": 2058, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DE98305BD6D68C77.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753300580260766, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753300580260765, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_F27749E0A6B65183.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753300580261194, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_D5253DAEDFB20EED.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753300580261525, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753300580261658, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580261796, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580261871, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753300580261934, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580262200, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753300580262417, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580262615, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580262682, "dur": 2149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580264832, "dur": 1651, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580266483, "dur": 2170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580270477, "dur": 592, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Plugin\\Changelogs\\Changelog_1_4_6.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753300580268653, "dur": 2475, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580271129, "dur": 937, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580272066, "dur": 1412, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580273479, "dur": 1411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580274891, "dur": 1504, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580276396, "dur": 1149, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580277546, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580278748, "dur": 1535, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580280284, "dur": 1447, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580281731, "dur": 1304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580283035, "dur": 690, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580283760, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580284173, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580284236, "dur": 1337, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580285573, "dur": 607, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580286183, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753300580286532, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580287043, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753300580286612, "dur": 1493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753300580288106, "dur": 196, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580288309, "dur": 718, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753300580289027, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580289338, "dur": 757, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580290095, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580290229, "dur": 739, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580290969, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753300580291069, "dur": 440, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753300580291510, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580291657, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753300580291752, "dur": 257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753300580292010, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580292126, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753300580292214, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753300580292420, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580292529, "dur": 72334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580366472, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Http.Connections.Common.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753300580367339, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753300580368255, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753300580364866, "dur": 3752, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Routing.dll"}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 283, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 3342, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 470, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.RenderPipelines.Universal.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": ****************, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753300580372652, "dur": 2906, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753300580375559, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580375745, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580375845, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580375998, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580376109, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580376228, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580376340, "dur": 162, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580376510, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580376571, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580376646, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580376723, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580376837, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580376971, "dur": 101535, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300580478509, "dur": 863438, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753300580478508, "dur": 864543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753300581344288, "dur": 142, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753300581344439, "dur": 475869, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753300581837216, "dur": 1087087, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753300581837215, "dur": 1087090, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753300582924355, "dur": 1725, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580182272, "dur": 76362, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580258880, "dur": 1425, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1753300580258848, "dur": 1458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_63198FADDC2DE5CF.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753300580260307, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580260386, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580260385, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_55536420766141FA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753300580260496, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580260557, "dur": 398, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580260555, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_1CE9B2E479BEC5CA.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753300580260958, "dur": 294, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580261271, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580261368, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580261429, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580261861, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753300580261943, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580262076, "dur": 137, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1753300580262215, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1753300580262277, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580262556, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580262704, "dur": 1604, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580264308, "dur": 1852, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580266161, "dur": 2101, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580268262, "dur": 1697, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580270459, "dur": 636, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\PreviewManager.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753300580269959, "dur": 2170, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580272130, "dur": 2262, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580274393, "dur": 1708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580276101, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580277583, "dur": 1162, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580278746, "dur": 1135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580279881, "dur": 1073, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580280954, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580281955, "dur": 1552, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580283507, "dur": 518, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580283507, "dur": 526, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580284033, "dur": 145, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580284178, "dur": 1396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580285574, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580286168, "dur": 278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753300580286446, "dur": 239, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580286696, "dur": 521, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753300580287218, "dur": 378, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580287602, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753300580287751, "dur": 698, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580289089, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PhotoShopFileType\\BlendModeMapping.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753300580288452, "dur": 813, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753300580289265, "dur": 1483, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580290751, "dur": 74092, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580365007, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580366995, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580367900, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580364845, "dur": 3763, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753300580368609, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580370566, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipelines.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580370783, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580371541, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580369005, "dur": 3196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753300580372201, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580372423, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580374662, "dur": 340, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.OpenSsl.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580375157, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580375811, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580375993, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580376132, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580376333, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753300580372371, "dur": 4240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753300580376612, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580376888, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580376972, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300580377032, "dur": 1460225, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753300581837269, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753300581837268, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753300581837441, "dur": 1499, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1753300581838942, "dur": 1087456, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580182173, "dur": 76343, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580258534, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580258610, "dur": 1966, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 4, "ts": 1753300580258523, "dur": 2054, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_C0FCC60981132629.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753300580260774, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580260772, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5AAB6A659692F205.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753300580260955, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580261033, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5AAB6A659692F205.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753300580261305, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580261509, "dur": 222, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580261736, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580261918, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580262039, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580262109, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580262218, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580262350, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580262503, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580262602, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580262660, "dur": 1381, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580264042, "dur": 1780, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580265823, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580267008, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580268338, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580270502, "dur": 566, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\StickyNote.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753300580269850, "dur": 1950, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580271801, "dur": 1800, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580273601, "dur": 1181, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580274783, "dur": 1931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580276715, "dur": 854, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580277570, "dur": 1359, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580278929, "dur": 1637, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580280566, "dur": 1652, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580282219, "dur": 905, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580283124, "dur": 985, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580284109, "dur": 66, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580284175, "dur": 1397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580285572, "dur": 581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580286154, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753300580286790, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580287470, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\BinaryOperatorHandler.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753300580287659, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Operators\\UnaryOperatorHandler.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753300580287782, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\Optimization\\InstanceActionInvoker_2.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753300580287960, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\RenamedAssemblyAttribute.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753300580286368, "dur": 1854, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753300580288223, "dur": 337, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580288631, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753300580289051, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580289106, "dur": 1220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753300580290326, "dur": 603, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580290965, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753300580291058, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753300580291297, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580291438, "dur": 73397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580365956, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Formatters.Xml.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580364838, "dur": 2437, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753300580367275, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580367562, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580369465, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580367440, "dur": 3174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753300580370615, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580371414, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580373631, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580370817, "dur": 3548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753300580374366, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580374974, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580375160, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580375992, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.AspNetCore.Server.ClientFactory.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580376197, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Diagnostics.Abstractions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580376676, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753300580374743, "dur": 2719, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753300580377462, "dur": 258, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753300580377776, "dur": 2548597, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580182281, "dur": 76799, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580259126, "dur": 1256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 5, "ts": 1753300580259081, "dur": 1302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6FDD4B7A6EC84268.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753300580260383, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580260612, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580260610, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E81E4251EABAE467.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753300580260684, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580260953, "dur": 407, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\DotNetZip\\Unity.VisualScripting.IonicZip.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580260952, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.IonicZip.dll_840FAE5C8A141D5B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753300580261363, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580261448, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580261585, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580261684, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580261815, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580261917, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580262008, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580262071, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580262143, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580262198, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753300580262262, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580262355, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580262503, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580262673, "dur": 1498, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580264171, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580265301, "dur": 1047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580266349, "dur": 1730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580268079, "dur": 1757, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580270411, "dur": 646, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Descriptors\\FieldDescriptor.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753300580269836, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580271113, "dur": 599, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580271712, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580273275, "dur": 1297, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580274573, "dur": 1540, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580276114, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580277490, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580278792, "dur": 1085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580279877, "dur": 1510, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580281387, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580282449, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580283632, "dur": 99, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580283758, "dur": 419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580284177, "dur": 1398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580285575, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580286156, "dur": 393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753300580286549, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580287273, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XmlSerializer.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580286634, "dur": 1349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753300580287984, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580288208, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753300580288456, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580288798, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580289021, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580289231, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580288513, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753300580289390, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580289679, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753300580289851, "dur": 797, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753300580290649, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580290925, "dur": 73946, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580367418, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580368254, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580368401, "dur": 332, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580364874, "dur": 3862, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753300580368736, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580369224, "dur": 3123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753300580372352, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580375619, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580375743, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580375992, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580376265, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.XDocument.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753300580372542, "dur": 3867, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753300580376409, "dur": 394, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580376808, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753300580376807, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Sprite.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1753300580376859, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580376926, "dur": 542, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580377472, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753300580377543, "dur": 2548851, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580182400, "dur": 76589, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580259131, "dur": 313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580259445, "dur": 979, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 6, "ts": 1753300580259052, "dur": 1373, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_CBFD31FC7A6EE8BC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753300580260504, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580260503, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_F419673AF1271816.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753300580260732, "dur": 337, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580260730, "dur": 341, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F72D29FA518CE2F1.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753300580261072, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580261388, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580261482, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580261667, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580261742, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580261814, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580261875, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1753300580261938, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580261996, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580262096, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.rsp2"}}, {"pid": 12345, "tid": 6, "ts": 1753300580262245, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580262384, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580262696, "dur": 2260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580264957, "dur": 1597, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580266555, "dur": 1242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580267798, "dur": 1335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580270499, "dur": 516, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Codebase\\GetMemberDescriptor.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753300580269133, "dur": 2047, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580271180, "dur": 1664, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580272845, "dur": 1058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580273903, "dur": 1874, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580275778, "dur": 1782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580277561, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580279053, "dur": 1556, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580280609, "dur": 901, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580281510, "dur": 1308, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580282819, "dur": 1062, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580283881, "dur": 291, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580284173, "dur": 1417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580285590, "dur": 816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580286407, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753300580286640, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580286731, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753300580287271, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580287707, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580287780, "dur": 459, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753300580288240, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580288313, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580288399, "dur": 236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753300580288636, "dur": 620, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580289258, "dur": 483, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753300580289741, "dur": 179, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580289952, "dur": 138, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580290091, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753300580290250, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580290327, "dur": 471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753300580290798, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580291083, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753300580291191, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580291267, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753300580291680, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580291846, "dur": 73496, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580367039, "dur": 212, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.ViewFeatures.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580367410, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Ini.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580368977, "dur": 496, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580365344, "dur": 4220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753300580369565, "dur": 950, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580370515, "dur": 60, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753300580370591, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580371087, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580373670, "dur": 234, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580373997, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580370578, "dur": 3771, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753300580374349, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580375017, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580375160, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580375731, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580375931, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authorization.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580376132, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Localization.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753300580374578, "dur": 2925, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753300580377503, "dur": 324, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753300580377869, "dur": 2548510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580182162, "dur": 76346, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580258518, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580258636, "dur": 1466, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 7, "ts": 1753300580258600, "dur": 1503, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_939D9295B43D54DB.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753300580260528, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580260662, "dur": 267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580260970, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580261273, "dur": 160, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580261497, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580261615, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580261755, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580261864, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753300580261931, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580262113, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753300580262325, "dur": 165, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1753300580262521, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580262720, "dur": 2135, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580264855, "dur": 1102, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580265957, "dur": 2575, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580268532, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580270498, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Contexts\\TargetPropertyGUIContext.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753300580269845, "dur": 2537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580272382, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580273874, "dur": 1121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580274995, "dur": 1490, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580276486, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580277811, "dur": 1279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580279090, "dur": 1461, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580280552, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580281844, "dur": 884, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580282728, "dur": 1033, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580283762, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580284175, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580284240, "dur": 1354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580285594, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580286181, "dur": 707, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753300580286889, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580287471, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753300580287776, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Runtime\\Audio\\AudioClipProperties.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753300580287022, "dur": 980, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753300580288002, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580288232, "dur": 141, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753300580288374, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580288463, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753300580288448, "dur": 1151, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753300580289600, "dur": 471, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580290108, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580290185, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753300580290395, "dur": 1254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753300580291650, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580291907, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753300580291961, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580292016, "dur": 388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753300580292404, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580292606, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753300580292789, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580292959, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753300580293050, "dur": 185, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753300580293534, "dur": 180384, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753300580478675, "dur": 20623, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753300580478458, "dur": 20892, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753300580499351, "dur": 100, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300580499465, "dur": 842509, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753300580499463, "dur": 843591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753300581343901, "dur": 122, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753300581344437, "dur": 473673, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753300581836010, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1753300581836008, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1753300581836165, "dur": 1090147, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580182226, "dur": 76298, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580258540, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580258598, "dur": 1516, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 8, "ts": 1753300580258531, "dur": 1584, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A3A3243B74248B90.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753300580260228, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580260480, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580260479, "dur": 76, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_21357EA18D8E171B.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753300580260556, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580260650, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580260760, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580260759, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.PresetsUIModule.dll_ADDD82C2A90EA988.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753300580260887, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580260991, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580261180, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1B6116B393E72286.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753300580261257, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580261499, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580261771, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580262177, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580262270, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580262405, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580262463, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1723042715960067801.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1753300580262516, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580262691, "dur": 2121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580265068, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\treeview\\TrackPropertyCurvesDataSource.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753300580264812, "dur": 1532, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580266344, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580267576, "dur": 1667, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580270497, "dur": 522, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Description\\UnitDescription.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753300580269244, "dur": 1993, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580271238, "dur": 1733, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580272972, "dur": 1642, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580274614, "dur": 1043, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580275658, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580277247, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580278223, "dur": 1113, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580279336, "dur": 1771, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580281108, "dur": 1595, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580282703, "dur": 1302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580284005, "dur": 187, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580284192, "dur": 1399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580285592, "dur": 599, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580286192, "dur": 286, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753300580286479, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580286545, "dur": 264, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753300580287659, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.28\\Rider\\Editor\\RiderInitializer.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753300580287876, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.28\\Rider\\Editor\\RiderStyles.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753300580286810, "dur": 1287, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753300580288097, "dur": 273, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580288396, "dur": 337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753300580288734, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580288873, "dur": 626, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753300580289500, "dur": 619, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580290129, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580290230, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753300580290341, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580290403, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753300580291056, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580291228, "dur": 92, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753300580291347, "dur": 481, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753300580291829, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580291979, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753300580292057, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753300580292308, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580292422, "dur": 72432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580367515, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580368164, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580364864, "dur": 3966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753300580368836, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580370286, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.SignalR.Common.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580371951, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580372398, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580368997, "dur": 3567, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753300580372565, "dur": 436, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580373012, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.Mathematics.Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580373636, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580375103, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Cryptography.Pkcs.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580375809, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580375931, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753300580373011, "dur": 3656, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753300580376668, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580376970, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753300580377035, "dur": 2549213, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580182274, "dur": 76409, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580258721, "dur": 1864, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 9, "ts": 1753300580258684, "dur": 1902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_49D0198E18ABFDE2.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753300580260587, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580260745, "dur": 265, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580260744, "dur": 268, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_31272CE89BD91E91.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753300580261012, "dur": 501, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580261524, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753300580261679, "dur": 299, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580261980, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580262182, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580262251, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580262402, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580262503, "dur": 236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580262772, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580262990, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580263279, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580263344, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580263454, "dur": 628, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580264084, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580264264, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Buffers.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580264323, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580264386, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580264464, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580264611, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580264855, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580264976, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580265111, "dur": 145, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580265257, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580265434, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580265495, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580265545, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580265762, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580265935, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580266179, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580266301, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580266470, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580266595, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580266785, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580266841, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580267014, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580267186, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580267304, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580267484, "dur": 250, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580267735, "dur": 225, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580268086, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580268252, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580268356, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580268496, "dur": 458, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580268958, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580269024, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580269162, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580269306, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580269493, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580269612, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580269850, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580269921, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580270064, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580270128, "dur": 256, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580270385, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580270476, "dur": 634, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580271111, "dur": 504, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580271656, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580271812, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580272006, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580272072, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580272229, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580272309, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580272370, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580272502, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580272609, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580272736, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580272787, "dur": 293, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580273088, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580273158, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580273407, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580273546, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580273754, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580273822, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580273939, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580274063, "dur": 243, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580274307, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580274438, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580274620, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580274719, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580274826, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ValueTuple.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580275015, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580275275, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580275472, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\AllocatingGCMemoryConstraint.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580275701, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\InvalidSignatureException.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580275886, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogEvent.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580276031, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogMatch.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580276229, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnhandledLogMessageException.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580276324, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnityTestTimeoutException.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580276438, "dur": 303, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ActionDelegator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580276787, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\TestMustExpectAllLogsAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580277041, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityCombinatorialStrategy.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580277109, "dur": 185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityPlatformAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580277346, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Attributes\\UnityTestAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580277456, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\BaseDelegator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580277507, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\BeforeAfterTestCommandBase.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580277724, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableSetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580277825, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580277881, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestState.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580277996, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\ImmediateEnumerableCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580278055, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\OuterUnityTestActionCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580278108, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\SetUpTearDownCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580278265, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\UnityTestMethodCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580278334, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ConstructDelegator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580278403, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Filters\\AssemblyNameFilter.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580278563, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\IAsyncTestAssemblyBuilder.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580278703, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\ITestSuiteModifier.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580278806, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\OrderedTestSuiteModifier.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580278894, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580279014, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CoroutineTestWorkItem.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580279146, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\FailCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580279360, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\IEnumerableTestMethodCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580279416, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580279592, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\TestCommandBuilder.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580279702, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityLogCheckDelegatingCommand.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580279875, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestAssemblyRunner.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580279977, "dur": 120, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityWorkItem.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580280293, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRenderer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580280475, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\TestResultRendererCallback.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580280528, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\ITestRunnerListener.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580280755, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\PlaymodeTestsController.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580280877, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\IRemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580281017, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\PlayerConnectionMessageIds.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580281070, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestData.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580281123, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultData.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580281179, "dur": 89, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580281269, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataWithTestData.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580281331, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RuntimeTestRunnerFilter.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580281404, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\SynchronousFilter.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580281474, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestEnumeratorWrapper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580281658, "dur": 139, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestListenerWrapper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580281847, "dur": 172, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\AssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580282045, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyLoadProxy.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580282148, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IAssemblyWrapper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580282258, "dur": 196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580282455, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\PlayerTestAssemblyProvider.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580282560, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AttributeHelper.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580282680, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\CoroutineRunner.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580282793, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IOuterUnityTestAction.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580282863, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IPostBuildCleanup.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580282944, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IPrebuildSceneSetup.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580283006, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ITestRunCallback.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580283059, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\IMonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580283113, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\MonoBehaviourTest\\MonoBehaviourTest.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580283257, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackAttribute.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580283406, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackListener.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580283457, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Utils.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580283559, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector2ComparerWithEqualsOperator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580283779, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector2EqualityComparer.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580283935, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector4ComparerWithEqualsOperator.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580261608, "dur": 22411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753300580284020, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580284216, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753300580284428, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580284879, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580284305, "dur": 1101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753300580285407, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580285682, "dur": 218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753300580285901, "dur": 174, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580286165, "dur": 311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753300580286477, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580286550, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753300580287240, "dur": 240, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580287489, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580287581, "dur": 238, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753300580287820, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580288640, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\ILPostProcessingLegacy.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753300580287914, "dur": 848, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753300580288762, "dur": 198, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580288979, "dur": 775, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1753300580289788, "dur": 183, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580290469, "dur": 67898, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 9, "ts": 1753300580364846, "dur": 2275, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753300580367125, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580367315, "dur": 2687, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753300580370004, "dur": 171, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580372398, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580370187, "dur": 3257, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753300580373445, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580374200, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580375993, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753300580373601, "dur": 3237, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753300580376838, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580377008, "dur": 340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753300580377384, "dur": 2548872, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580182357, "dur": 76501, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580258894, "dur": 1641, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 10, "ts": 1753300580258859, "dur": 1677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4F40831771D8FBF5.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753300580260606, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580260723, "dur": 224, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753300580260722, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3F15606BE1109C0F.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753300580260950, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580261058, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580261137, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580261188, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1753300580261466, "dur": 221, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580261692, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580261812, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580261868, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753300580261939, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580262000, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580262079, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 10, "ts": 1753300580262173, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 10, "ts": 1753300580262225, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580262536, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580262678, "dur": 2231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580264909, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580266352, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580267576, "dur": 1141, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580268718, "dur": 1288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580270471, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Inspector\\TabbedView\\TabButton.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753300580270006, "dur": 2392, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580272398, "dur": 999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580273397, "dur": 1130, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580274528, "dur": 1761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580276289, "dur": 1298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580277587, "dur": 1264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580278852, "dur": 1497, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580280349, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580281393, "dur": 1920, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580283314, "dur": 893, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753300580283313, "dur": 1860, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580285173, "dur": 398, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580285606, "dur": 563, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580286188, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753300580286791, "dur": 153, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753300580287091, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753300580286558, "dur": 1044, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753300580287603, "dur": 827, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580288437, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580288545, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753300580289080, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753300580288781, "dur": 806, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753300580289588, "dur": 348, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580289947, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580290030, "dur": 73, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580290103, "dur": 149, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580290252, "dur": 2711, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580292964, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753300580293090, "dur": 71751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580365221, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753300580367418, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753300580364856, "dur": 2870, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753300580367726, "dur": 468, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580368206, "dur": 1974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753300580370181, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580370505, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753300580371082, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753300580370336, "dur": 2231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753300580372571, "dur": 1123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580375810, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753300580373703, "dur": 2819, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753300580376523, "dur": 356, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580376883, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580376957, "dur": 815, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753300580377772, "dur": 2548581, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580182394, "dur": 76464, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580258894, "dur": 1351, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 11, "ts": 1753300580258858, "dur": 1388, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GIModule.dll_826CA845B2B614E1.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753300580260378, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753300580260377, "dur": 90, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_D0034867E905C483.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753300580260563, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580260723, "dur": 237, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753300580260721, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_46FFF6FD196ECAB7.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753300580260963, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580261300, "dur": 212, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580261857, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753300580261909, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580261976, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580262036, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580262088, "dur": 132, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 11, "ts": 1753300580262221, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580262551, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580262700, "dur": 1483, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580264184, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580265124, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580266177, "dur": 1167, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580267344, "dur": 934, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580268279, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580269357, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\GraphInputWidget.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753300580270502, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Control\\SelectUnitDescriptor.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753300580269086, "dur": 2082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580271168, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580272351, "dur": 1641, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580273993, "dur": 1034, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580275028, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580275911, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580277345, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580278903, "dur": 1951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580280854, "dur": 1145, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580282000, "dur": 1277, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580283278, "dur": 734, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753300580283277, "dur": 1904, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580285181, "dur": 396, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580285577, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580286173, "dur": 117, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753300580286291, "dur": 514, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580287043, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753300580286812, "dur": 578, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753300580287390, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580287595, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753300580287744, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580287804, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753300580288287, "dur": 486, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580288785, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580288842, "dur": 137, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580289018, "dur": 1073, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580290091, "dur": 140, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580290231, "dur": 1751, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580291983, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753300580292077, "dur": 221, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753300580292299, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580292439, "dur": 72419, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580365336, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Sockets.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753300580365554, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753300580367359, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Transactions.Local.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753300580364868, "dur": 3617, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753300580368486, "dur": 2322, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580372399, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753300580370820, "dur": 3026, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753300580373846, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580374152, "dur": 2350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753300580376506, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580376701, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580376867, "dur": 142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753300580377045, "dur": 2549182, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580182423, "dur": 76447, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580258888, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753300580258939, "dur": 1185, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 12, "ts": 1753300580258871, "dur": 1254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_4245840F5C4641AB.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753300580260335, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580260390, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753300580260389, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D293A48779142A71.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753300580260739, "dur": 231, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580261025, "dur": 321, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580261348, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753300580261492, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580261700, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580261854, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580262079, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753300580262172, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580262226, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580262663, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580262756, "dur": 1848, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580264605, "dur": 926, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580265532, "dur": 1067, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580266600, "dur": 1364, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580267964, "dur": 1553, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580270464, "dur": 507, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Processors\\MatrixNames.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753300580269517, "dur": 1454, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580270972, "dur": 2085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580273057, "dur": 1436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580274494, "dur": 1272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580275767, "dur": 1299, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580277066, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580278574, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580280112, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580280694, "dur": 1132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580281826, "dur": 1439, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580283265, "dur": 840, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753300580283265, "dur": 1721, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580284986, "dur": 598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580285587, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580286162, "dur": 367, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753300580286537, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580287040, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753300580286616, "dur": 657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753300580287274, "dur": 495, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580287782, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.ShaderGraph.Utilities.ref.dll_0524057423981A9D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753300580287833, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580287927, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580288086, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580288228, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753300580288658, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753300580288401, "dur": 749, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753300580289150, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580289398, "dur": 695, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580290093, "dur": 134, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580290227, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753300580290384, "dur": 403, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753300580290788, "dur": 235, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580291025, "dur": 73827, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580365193, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753300580366830, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Principal.Windows.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753300580367526, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753300580364856, "dur": 3222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753300580368079, "dur": 382, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580368468, "dur": 2249, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753300580370718, "dur": 639, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580371366, "dur": 2038, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753300580373405, "dur": 2534, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580375954, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580376179, "dur": 144, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580376653, "dur": 547, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753300580377209, "dur": 2549015, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580182443, "dur": 76453, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580258980, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580259204, "dur": 1017, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 13, "ts": 1753300580258902, "dur": 1319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_B5C2173E9AD0B5D6.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753300580260302, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580260531, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580260530, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FDF3153BFA104355.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753300580260661, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580260931, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580260930, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7880F7755F74374F.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753300580261069, "dur": 304, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580261398, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580261547, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753300580261634, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580261803, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580261983, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580262176, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580262254, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Data.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580262482, "dur": 114, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580262686, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580262832, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580262961, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580263016, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580263071, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580263258, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580263365, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580263454, "dur": 618, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580264073, "dur": 341, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580264454, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580264599, "dur": 190, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580264791, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580265079, "dur": 175, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580265256, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580265438, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Console.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580265505, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580265560, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580265762, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580265915, "dur": 148, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580266153, "dur": 135, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580266289, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580266446, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580266593, "dur": 151, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580266745, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580266865, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580267063, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580267197, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580267295, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580267463, "dur": 261, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580267726, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580267892, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580268113, "dur": 179, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580268293, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580268377, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580268497, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580268656, "dur": 170, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580268834, "dur": 93, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Expressions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580268928, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580269029, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580269142, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580269211, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580269291, "dur": 273, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580269593, "dur": 177, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580269773, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebHeaderCollection.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580269842, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580270075, "dur": 293, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580270369, "dur": 670, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580271044, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580271143, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580271296, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580271401, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580271489, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Reader.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580271652, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580271800, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580271920, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580271983, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580272054, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580272205, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580272297, "dur": 115, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580272413, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580272508, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580272603, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580272794, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580272996, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Encoding.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580273076, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580273155, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580273313, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580273377, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580273578, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580273753, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580273827, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580273932, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580274064, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580274298, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580274441, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580274620, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580274719, "dur": 184, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580274927, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580275074, "dur": 204, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580275285, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\Extensions\\2.0.0\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580275343, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580275497, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580275642, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\AxisEventData.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580275748, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\BaseEventData.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580275799, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventData\\PointerEventData.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580275932, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventHandle.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580276111, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventInterfaces.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580276210, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTrigger.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580276411, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\EventTriggerType.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580276462, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\ExecuteEvents.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580276559, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInput.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580276707, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\StandaloneInputModule.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580276813, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\TouchInputModule.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580276867, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\MoveDirection.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580277033, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\BaseRaycaster.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580277220, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\PhysicsRaycaster.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580277427, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelEventHandler.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580277639, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelRaycaster.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580277801, "dur": 87, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\AnimationTriggers.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580277978, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\ClipperRegistry.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580278048, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\Clipping.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580278181, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\DefaultControls.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580278293, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontData.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580278360, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\FontUpdateTracker.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580278693, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRebuildTracker.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580278789, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IGraphicEnabledDisabled.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580278909, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMask.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580279107, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\IMaskable.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580279242, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\AspectRatioFitter.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580279450, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\CanvasScaler.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580279587, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ContentSizeFitter.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580279640, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\GridLayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580279692, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\HorizontalLayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580279915, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580279994, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutRebuilder.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580280090, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutUtility.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580280208, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\VerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580280259, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Mask.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580280389, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MaskUtilities.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580280544, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\MultipleDisplayUtilities.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580281045, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Toggle.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580281183, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\ToggleGroup.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580281288, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\VertexHelper.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580281441, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\Outline.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580281542, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\PositionAsUV1.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753300580281616, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.Properties.SourceGenerator.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580281700, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\Unity.SourceGenerators\\Unity.SourceGenerators.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580261697, "dur": 20060, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753300580281758, "dur": 296, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580282126, "dur": 1465, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580283592, "dur": 79, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580283672, "dur": 61, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580283773, "dur": 405, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580284178, "dur": 1391, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580285615, "dur": 544, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580286163, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753300580286317, "dur": 2313, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580288633, "dur": 729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753300580289363, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580289632, "dur": 467, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580290099, "dur": 165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580290264, "dur": 70533, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580360798, "dur": 5070, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580365924, "dur": 325, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580366835, "dur": 253, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580367140, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580367246, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580368977, "dur": 501, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580365869, "dur": 4078, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753300580369948, "dur": 388, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580370504, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580370853, "dur": 740, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580373708, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753300580370344, "dur": 3467, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753300580373812, "dur": 1907, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580375730, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580375913, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580376162, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580376298, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580376473, "dur": 764, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753300580377241, "dur": 2548981, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580182464, "dur": 76367, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580258869, "dur": 1679, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 14, "ts": 1753300580258832, "dur": 1717, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_7C296F75F94E46F6.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753300580260574, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580260573, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VirtualTexturingModule.dll_609DF0C9F4BC7F1C.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753300580260778, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WebGLSupport\\UnityEditor.WebGL.Extensions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580260778, "dur": 111, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WebGL.Extensions.dll_C8504B54661C208D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753300580260915, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580261165, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Pdb.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580261164, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Pdb.dll_E2D6CB2A6174372D.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753300580261287, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580261384, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753300580261710, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580261866, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580261966, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580262357, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580262625, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580262745, "dur": 1163, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580263908, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580265271, "dur": 1009, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580266281, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580267264, "dur": 1046, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580268310, "dur": 1114, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580270322, "dur": 744, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Importers\\ShaderGraphImporter.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753300580269425, "dur": 2060, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580271486, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580272761, "dur": 961, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580273722, "dur": 1434, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580275156, "dur": 1213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580276369, "dur": 1523, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580277892, "dur": 1525, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580279418, "dur": 903, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580280321, "dur": 1492, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580281813, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580283141, "dur": 892, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580284033, "dur": 146, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580284179, "dur": 1410, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580285590, "dur": 561, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580286153, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753300580286386, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580286793, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580286441, "dur": 767, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753300580287209, "dur": 1109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580288357, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753300580288796, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580288650, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753300580289327, "dur": 262, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580289653, "dur": 113, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753300580289766, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580289822, "dur": 409, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753300580290231, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580290851, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580290906, "dur": 73955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580365744, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580366296, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Hosting.Abstractions.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580367003, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580367418, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580367525, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580368059, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580364862, "dur": 3390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753300580368256, "dur": 410, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580368816, "dur": 180, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580369464, "dur": 550, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-string-l1-1-0.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580370183, "dur": 336, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Localization.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580370782, "dur": 331, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580368676, "dur": 3194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753300580371870, "dur": 943, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580372823, "dur": 2223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753300580375047, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580375163, "dur": 662, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580375163, "dur": 663, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEngine.UI.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300580376038, "dur": 195, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580376272, "dur": 720, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300580376995, "dur": 1459057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753300581836054, "dur": 118811, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300581836054, "dur": 118822, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753300581954929, "dur": 971459, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580182483, "dur": 76345, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580258834, "dur": 484, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753300580259327, "dur": 1064, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 15, "ts": 1753300580258829, "dur": 1563, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4012322E9A5614BC.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753300580260393, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580260473, "dur": 147, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UmbraModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753300580260472, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UmbraModule.dll_0E723EAE4164004E.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753300580260622, "dur": 329, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580260962, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580261056, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580261122, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580261257, "dur": 224, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580261488, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580261555, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 15, "ts": 1753300580261647, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580261840, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580261951, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580262012, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580262080, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580262169, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580262247, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580262344, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580262462, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580262569, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580262703, "dur": 1962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580264665, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580265764, "dur": 1536, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580267301, "dur": 1291, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580268592, "dur": 1429, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580270354, "dur": 641, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Inspector\\PropertyDrawers\\MatrixPropertyDrawer.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753300580270021, "dur": 2051, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580272072, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580273122, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580274246, "dur": 1153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580275399, "dur": 1383, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580276782, "dur": 1554, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580278337, "dur": 1182, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580279519, "dur": 1407, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580280927, "dur": 1442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580282369, "dur": 1123, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580283492, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753300580283492, "dur": 1507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580285000, "dur": 593, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580285593, "dur": 570, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580286172, "dur": 310, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753300580286483, "dur": 865, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580287866, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.WindowsRuntime.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753300580287354, "dur": 869, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753300580288223, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580288422, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580288559, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580288721, "dur": 260, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580288983, "dur": 1405, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580290392, "dur": 74454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580364847, "dur": 2570, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753300580367417, "dur": 1582, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580369116, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753300580369315, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VehiclesModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753300580370328, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-utility-l1-1-0.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753300580370826, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.ConfigurationExtensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753300580372051, "dur": 221, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Extensions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753300580369007, "dur": 3566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753300580372573, "dur": 1797, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580375151, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.Lightweight.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753300580374379, "dur": 2702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753300580377082, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753300580377375, "dur": 2548947, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580182545, "dur": 76209, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580258784, "dur": 1556, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 16, "ts": 1753300580258754, "dur": 1595, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_7F06B267F1BFA456.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753300580260422, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580260421, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3AA9E1B89A839D24.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753300580260504, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580260571, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VFXModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580260570, "dur": 60, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VFXModule.dll_A857FD1C4743904D.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753300580260751, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneTemplateModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580260750, "dur": 265, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneTemplateModule.dll_64C5CD55CF2E68A6.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753300580261359, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580261450, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580261521, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753300580261598, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753300580261672, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580261747, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580261936, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580262016, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580262137, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580262481, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580262698, "dur": 2110, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580264808, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580266026, "dur": 1082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580267108, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580268354, "dur": 1408, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580270471, "dur": 518, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Enumerations\\ZWrite.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753300580269762, "dur": 1711, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580271473, "dur": 1303, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580272776, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580274126, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580275222, "dur": 933, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580276155, "dur": 893, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580277049, "dur": 931, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580277980, "dur": 1431, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580279411, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580280797, "dur": 1560, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580282357, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580283625, "dur": 148, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580283774, "dur": 399, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580284174, "dur": 1402, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580285577, "dur": 580, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580286158, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753300580286261, "dur": 805, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580287658, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580287072, "dur": 924, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753300580288011, "dur": 458, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580288482, "dur": 1583, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580290091, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753300580290207, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580290267, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753300580290611, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580290775, "dur": 74091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580364875, "dur": 2231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753300580367107, "dur": 364, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580368255, "dur": 152, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Overlapped.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580368912, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.Binder.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580370566, "dur": 556, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580367483, "dur": 4089, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753300580371572, "dur": 904, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580372693, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580373699, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.UserSecrets.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580374004, "dur": 694, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580375100, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580372486, "dur": 3254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753300580375740, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580375948, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580376026, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.2D.IK.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580376026, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.IK.Editor.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753300580376098, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580376637, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580376695, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580376870, "dur": 481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753300580377351, "dur": 2548907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580182501, "dur": 76300, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580258835, "dur": 1587, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 17, "ts": 1753300580258801, "dur": 1632, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_38FFBE07FFE62337.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753300580260433, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580260726, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580261178, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1753300580261254, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580261338, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580261453, "dur": 259, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580261765, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580261934, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580262116, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580262196, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753300580262419, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580262587, "dur": 197, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580262786, "dur": 2509, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580265295, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580266360, "dur": 1747, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580268107, "dur": 1389, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580269496, "dur": 2169, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580271666, "dur": 951, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580272618, "dur": 1918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580274537, "dur": 1839, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580276377, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580277704, "dur": 1071, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580278775, "dur": 1421, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580280196, "dur": 1161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580281357, "dur": 807, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580282164, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580283476, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753300580283476, "dur": 1668, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580285145, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580285577, "dur": 801, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580286379, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753300580286790, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753300580286552, "dur": 397, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753300580286949, "dur": 287, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580287757, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753300580287246, "dur": 973, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753300580288219, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580288632, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753300580288826, "dur": 241, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580289073, "dur": 1214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753300580290288, "dur": 215, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580290513, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580290635, "dur": 74203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580364839, "dur": 2500, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753300580367340, "dur": 1082, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580368436, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.RenderPipelines.Core.Editor.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753300580370183, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753300580370782, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753300580368435, "dur": 2872, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753300580371308, "dur": 1245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580375105, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753300580372560, "dur": 2798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753300580375358, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580375484, "dur": 274, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580375765, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580375849, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580375923, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580376009, "dur": 147, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580376166, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580376254, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580376415, "dur": 246, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580376697, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580376762, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580376869, "dur": 401, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753300580377307, "dur": 2548969, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580182528, "dur": 76243, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580258813, "dur": 1700, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 18, "ts": 1753300580258772, "dur": 1742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DirectorModule.dll_3AC0D0C527F5439B.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753300580260578, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580260715, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753300580260714, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_EA305F3817072CC7.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753300580261066, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580261197, "dur": 227, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580261432, "dur": 226, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580261662, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580261761, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580261945, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580262113, "dur": 51, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753300580262166, "dur": 94, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753300580262261, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580262332, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753300580262492, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580262657, "dur": 940, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580263598, "dur": 2022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580265621, "dur": 1276, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580266898, "dur": 774, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580267672, "dur": 1218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580268891, "dur": 1558, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580270450, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Inspector\\MasterPreviewView.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753300580270450, "dur": 1443, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580271893, "dur": 1022, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580272915, "dur": 1529, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580274445, "dur": 895, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580275340, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580275895, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580277376, "dur": 1445, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580278821, "dur": 1138, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580279960, "dur": 1828, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580281789, "dur": 1229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580283018, "dur": 1249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580284267, "dur": 1329, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580285596, "dur": 569, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580286173, "dur": 543, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753300580286717, "dur": 430, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580287261, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753300580287568, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753300580288135, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.pixel-perfect@5.0.3\\Runtime\\PixelPerfectCameraInternal.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753300580287158, "dur": 1064, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753300580288223, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580288523, "dur": 1194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580289721, "dur": 373, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580290094, "dur": 136, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580290230, "dur": 1430, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580291661, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753300580291741, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753300580291954, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580292100, "dur": 72750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580365875, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753300580366327, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Net.Http.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753300580366781, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Connections.Abstractions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753300580368255, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753300580364865, "dur": 3684, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753300580368550, "dur": 399, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580368977, "dur": 492, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.PresetsUIModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753300580369471, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753300580370505, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-libraryloader-l1-1-0.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753300580368958, "dur": 2966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753300580371924, "dur": 778, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580373635, "dur": 414, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Hosting.Abstractions.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753300580372727, "dur": 2995, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753300580375723, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580375987, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580376238, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580376332, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580376463, "dur": 882, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753300580377402, "dur": 2548848, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580182567, "dur": 76169, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580258742, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753300580258792, "dur": 1421, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 19, "ts": 1753300580258737, "dur": 1477, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_71CF6729598465FE.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753300580260215, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580260273, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_A964AEB46C39AB32.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753300580260323, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580260416, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753300580260415, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_BD74DBBEF40E5ADA.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753300580260495, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580260561, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753300580260559, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_8837211AE3CE1746.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753300580260630, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580260706, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AccessibilityModule.dll_976B38ECCFA49043.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753300580260759, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580260958, "dur": 371, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580261370, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580261471, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580261598, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580261872, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.rsp"}}, {"pid": 12345, "tid": 19, "ts": 1753300580262260, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580262381, "dur": 164, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580262597, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580262676, "dur": 1705, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580264382, "dur": 1426, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580265809, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580267005, "dur": 563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580267568, "dur": 1796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580270489, "dur": 552, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Util\\ListUtilities.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753300580269364, "dur": 1677, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580271041, "dur": 1150, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580272191, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580273932, "dur": 1030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580274962, "dur": 1534, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580276497, "dur": 1706, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580278203, "dur": 1238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580279441, "dur": 1313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580280755, "dur": 1094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580281850, "dur": 778, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580282658, "dur": 587, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580283245, "dur": 1185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580284430, "dur": 1165, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580285595, "dur": 590, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580286186, "dur": 936, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753300580287122, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580287783, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.XRModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753300580287468, "dur": 786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753300580288254, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580288499, "dur": 398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753300580288897, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580289226, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Console.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753300580288965, "dur": 533, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753300580289499, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580289618, "dur": 474, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580290093, "dur": 135, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580290229, "dur": 170, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753300580290439, "dur": 390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753300580290830, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580290950, "dur": 73890, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580364840, "dur": 2518, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753300580367358, "dur": 416, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580369449, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Metadata.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753300580367782, "dur": 2897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753300580370679, "dur": 1277, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580372398, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753300580372841, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-math-l1-1-0.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753300580373626, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.Xml.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753300580374000, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753300580374090, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubstanceModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753300580374453, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.Encoding.Extensions.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753300580371965, "dur": 3101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753300580375067, "dur": 908, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580375985, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580376446, "dur": 373, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580376852, "dur": 118, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580376970, "dur": 876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753300580377889, "dur": 2548412, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580182585, "dur": 76132, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580258756, "dur": 1788, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1753300580258718, "dur": 1826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_77AE899CA5593464.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753300580260545, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580260772, "dur": 327, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.QuickSearchModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580260771, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.QuickSearchModule.dll_9AAEFD6A746FD785.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753300580261509, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580261714, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580261852, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753300580261917, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580261992, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580262062, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580262263, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580262336, "dur": 112, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.rsp2"}}, {"pid": 12345, "tid": 20, "ts": 1753300580262546, "dur": 159, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580262710, "dur": 1437, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580264147, "dur": 1896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580266043, "dur": 1206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580268786, "dur": 585, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Editor\\SkinningModule\\UI\\PoseToolbar.cs"}}, {"pid": 12345, "tid": 20, "ts": 1753300580267250, "dur": 2146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580269397, "dur": 1093, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580270491, "dur": 550, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Controls\\DefaultControl.cs"}}, {"pid": 12345, "tid": 20, "ts": 1753300580270491, "dur": 1820, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580272311, "dur": 1502, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580273813, "dur": 1572, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580275385, "dur": 1211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580276597, "dur": 836, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580277433, "dur": 1239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580278672, "dur": 969, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580279641, "dur": 1258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580280899, "dur": 1432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580282331, "dur": 1316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580283760, "dur": 416, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580284176, "dur": 1400, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580285576, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580286166, "dur": 126, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753300580286293, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580286791, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\ref\\2.1.0\\netstandard.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580286353, "dur": 539, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753300580286892, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580287073, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580287708, "dur": 239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753300580287947, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580288422, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580288021, "dur": 594, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753300580288615, "dur": 216, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580288843, "dur": 327, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580289184, "dur": 914, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580290098, "dur": 143, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580290241, "dur": 2307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580292548, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753300580292619, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753300580292813, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580292898, "dur": 74534, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580367522, "dur": 129, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580367656, "dur": 611, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580368597, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580369232, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.Common.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580370187, "dur": 331, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580370582, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580371125, "dur": 238, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580367432, "dur": 4114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753300580371546, "dur": 1105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580373262, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Numerics.Vectors.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580373421, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Extensions.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580374005, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.ClientFactory.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580374796, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580375157, "dur": 831, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580376333, "dur": 355, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753300580372655, "dur": 4526, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753300580377182, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753300580377289, "dur": 2548989, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753300582932935, "dur": 3482, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21312, "tid": 8828, "ts": 1753300582950826, "dur": 82809, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21312, "tid": 8828, "ts": 1753300583033710, "dur": 2886, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21312, "tid": 8828, "ts": 1753300582947312, "dur": 90019, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}