{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21312, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21312, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21312, "tid": 9069, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21312, "tid": 9069, "ts": 1753301889552128, "dur": 439, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21312, "tid": 9069, "ts": 1753301889554910, "dur": 597, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21312, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21312, "tid": 1, "ts": 1753301886952027, "dur": 4220, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21312, "tid": 1, "ts": 1753301886956251, "dur": 29921, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21312, "tid": 1, "ts": 1753301886986180, "dur": 25771, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21312, "tid": 9069, "ts": 1753301889555510, "dur": 8, "ph": "X", "name": "", "args": {}}, {"pid": 21312, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886948144, "dur": 53, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886948197, "dur": 2597070, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886948776, "dur": 2759, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886951539, "dur": 1062, "ph": "X", "name": "ProcessMessages 20485", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886952604, "dur": 221, "ph": "X", "name": "ReadAsync 20485", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886952827, "dur": 9, "ph": "X", "name": "ProcessMessages 20488", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886952837, "dur": 37, "ph": "X", "name": "ReadAsync 20488", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886952877, "dur": 22, "ph": "X", "name": "ReadAsync 933", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886952904, "dur": 44, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886952950, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886952980, "dur": 19, "ph": "X", "name": "ReadAsync 94", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953002, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953004, "dur": 22, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953029, "dur": 25, "ph": "X", "name": "ReadAsync 339", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953057, "dur": 19, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953079, "dur": 21, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953103, "dur": 19, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953124, "dur": 23, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953151, "dur": 19, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953173, "dur": 25, "ph": "X", "name": "ReadAsync 385", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953201, "dur": 17, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953220, "dur": 19, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953242, "dur": 28, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953273, "dur": 19, "ph": "X", "name": "ReadAsync 930", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953294, "dur": 1, "ph": "X", "name": "ProcessMessages 654", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953296, "dur": 18, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953317, "dur": 19, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953339, "dur": 19, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953361, "dur": 19, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953382, "dur": 18, "ph": "X", "name": "ReadAsync 594", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953403, "dur": 22, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953428, "dur": 20, "ph": "X", "name": "ReadAsync 750", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953450, "dur": 18, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953471, "dur": 24, "ph": "X", "name": "ReadAsync 377", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953498, "dur": 20, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953520, "dur": 19, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953542, "dur": 21, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953565, "dur": 22, "ph": "X", "name": "ReadAsync 749", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953590, "dur": 18, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953611, "dur": 19, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953632, "dur": 21, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953656, "dur": 19, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953678, "dur": 19, "ph": "X", "name": "ReadAsync 629", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953699, "dur": 18, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953720, "dur": 22, "ph": "X", "name": "ReadAsync 194", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953744, "dur": 20, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953768, "dur": 27, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953798, "dur": 26, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953827, "dur": 20, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953849, "dur": 22, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953874, "dur": 21, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953898, "dur": 16, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953916, "dur": 42, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953960, "dur": 21, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886953984, "dur": 20, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954007, "dur": 17, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954027, "dur": 18, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954047, "dur": 19, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954069, "dur": 19, "ph": "X", "name": "ReadAsync 719", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954091, "dur": 18, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954112, "dur": 18, "ph": "X", "name": "ReadAsync 476", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954133, "dur": 20, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954156, "dur": 20, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954179, "dur": 20, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954202, "dur": 20, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954225, "dur": 19, "ph": "X", "name": "ReadAsync 787", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954248, "dur": 20, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954270, "dur": 18, "ph": "X", "name": "ReadAsync 693", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954291, "dur": 19, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954312, "dur": 23, "ph": "X", "name": "ReadAsync 746", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954338, "dur": 1, "ph": "X", "name": "ProcessMessages 698", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954341, "dur": 117, "ph": "X", "name": "ReadAsync 698", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954461, "dur": 1, "ph": "X", "name": "ProcessMessages 1137", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954463, "dur": 34, "ph": "X", "name": "ReadAsync 1137", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954499, "dur": 1, "ph": "X", "name": "ProcessMessages 2335", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954501, "dur": 118, "ph": "X", "name": "ReadAsync 2335", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954623, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954667, "dur": 5, "ph": "X", "name": "ProcessMessages 2858", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954673, "dur": 78, "ph": "X", "name": "ReadAsync 2858", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954755, "dur": 24, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954781, "dur": 22, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954806, "dur": 17, "ph": "X", "name": "ReadAsync 611", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954826, "dur": 19, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954848, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954870, "dur": 21, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954893, "dur": 23, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954920, "dur": 17, "ph": "X", "name": "ReadAsync 702", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886954954, "dur": 118, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955076, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955102, "dur": 28, "ph": "X", "name": "ReadAsync 670", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955132, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955133, "dur": 21, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955157, "dur": 16, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955176, "dur": 27, "ph": "X", "name": "ReadAsync 83", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955206, "dur": 20, "ph": "X", "name": "ReadAsync 682", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955229, "dur": 19, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955250, "dur": 17, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955270, "dur": 24, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955296, "dur": 27, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955326, "dur": 19, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955348, "dur": 17, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955368, "dur": 24, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955395, "dur": 19, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955416, "dur": 37, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955456, "dur": 17, "ph": "X", "name": "ReadAsync 417", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955475, "dur": 19, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955497, "dur": 19, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955519, "dur": 20, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955542, "dur": 18, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955563, "dur": 18, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955583, "dur": 27, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955613, "dur": 18, "ph": "X", "name": "ReadAsync 1049", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955633, "dur": 18, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955654, "dur": 18, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955675, "dur": 21, "ph": "X", "name": "ReadAsync 354", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955698, "dur": 19, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955720, "dur": 19, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955741, "dur": 24, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955769, "dur": 29, "ph": "X", "name": "ReadAsync 365", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955800, "dur": 19, "ph": "X", "name": "ReadAsync 927", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955823, "dur": 18, "ph": "X", "name": "ReadAsync 617", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955843, "dur": 19, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955865, "dur": 20, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955889, "dur": 22, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955914, "dur": 17, "ph": "X", "name": "ReadAsync 537", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955934, "dur": 24, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955961, "dur": 26, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886955990, "dur": 18, "ph": "X", "name": "ReadAsync 829", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956010, "dur": 17, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956031, "dur": 19, "ph": "X", "name": "ReadAsync 215", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956053, "dur": 20, "ph": "X", "name": "ReadAsync 671", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956076, "dur": 18, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956097, "dur": 18, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956117, "dur": 18, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956138, "dur": 19, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956160, "dur": 20, "ph": "X", "name": "ReadAsync 708", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956182, "dur": 18, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956204, "dur": 47, "ph": "X", "name": "ReadAsync 654", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956253, "dur": 21, "ph": "X", "name": "ReadAsync 668", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956277, "dur": 17, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956297, "dur": 19, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956318, "dur": 17, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956338, "dur": 21, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956362, "dur": 28, "ph": "X", "name": "ReadAsync 731", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956402, "dur": 1, "ph": "X", "name": "ProcessMessages 961", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956403, "dur": 25, "ph": "X", "name": "ReadAsync 961", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956431, "dur": 20, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956454, "dur": 19, "ph": "X", "name": "ReadAsync 721", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956475, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956477, "dur": 30, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956510, "dur": 21, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956533, "dur": 20, "ph": "X", "name": "ReadAsync 517", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956557, "dur": 20, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956579, "dur": 18, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956600, "dur": 20, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956623, "dur": 20, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956646, "dur": 19, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956667, "dur": 18, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956688, "dur": 24, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956715, "dur": 20, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956737, "dur": 20, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956760, "dur": 17, "ph": "X", "name": "ReadAsync 730", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956780, "dur": 20, "ph": "X", "name": "ReadAsync 185", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956803, "dur": 35, "ph": "X", "name": "ReadAsync 680", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956840, "dur": 33, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956877, "dur": 24, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956904, "dur": 20, "ph": "X", "name": "ReadAsync 753", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956927, "dur": 28, "ph": "X", "name": "ReadAsync 607", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956958, "dur": 21, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886956982, "dur": 38, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957023, "dur": 26, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957051, "dur": 18, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957072, "dur": 20, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957093, "dur": 1, "ph": "X", "name": "ProcessMessages 701", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957096, "dur": 21, "ph": "X", "name": "ReadAsync 701", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957121, "dur": 19, "ph": "X", "name": "ReadAsync 630", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957143, "dur": 17, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957183, "dur": 26, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957210, "dur": 1, "ph": "X", "name": "ProcessMessages 1561", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957212, "dur": 18, "ph": "X", "name": "ReadAsync 1561", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957233, "dur": 18, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957256, "dur": 20, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957278, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957280, "dur": 20, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957303, "dur": 18, "ph": "X", "name": "ReadAsync 655", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957323, "dur": 20, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957346, "dur": 29, "ph": "X", "name": "ReadAsync 710", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957377, "dur": 18, "ph": "X", "name": "ReadAsync 903", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957398, "dur": 35, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957435, "dur": 1, "ph": "X", "name": "ProcessMessages 815", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957436, "dur": 22, "ph": "X", "name": "ReadAsync 815", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957461, "dur": 21, "ph": "X", "name": "ReadAsync 742", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957484, "dur": 17, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957504, "dur": 22, "ph": "X", "name": "ReadAsync 171", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957529, "dur": 31, "ph": "X", "name": "ReadAsync 912", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957562, "dur": 20, "ph": "X", "name": "ReadAsync 849", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957584, "dur": 19, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957606, "dur": 19, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957628, "dur": 21, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957652, "dur": 20, "ph": "X", "name": "ReadAsync 802", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957674, "dur": 20, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957697, "dur": 20, "ph": "X", "name": "ReadAsync 704", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957719, "dur": 16, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957738, "dur": 18, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957758, "dur": 50, "ph": "X", "name": "ReadAsync 133", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957810, "dur": 1, "ph": "X", "name": "ProcessMessages 867", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957811, "dur": 24, "ph": "X", "name": "ReadAsync 867", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957838, "dur": 22, "ph": "X", "name": "ReadAsync 845", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957863, "dur": 20, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957886, "dur": 19, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957907, "dur": 20, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957930, "dur": 19, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957951, "dur": 20, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957974, "dur": 17, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886957993, "dur": 19, "ph": "X", "name": "ReadAsync 252", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958015, "dur": 21, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958040, "dur": 28, "ph": "X", "name": "ReadAsync 612", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958071, "dur": 21, "ph": "X", "name": "ReadAsync 838", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958094, "dur": 19, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958116, "dur": 20, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958138, "dur": 20, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958161, "dur": 20, "ph": "X", "name": "ReadAsync 726", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958183, "dur": 19, "ph": "X", "name": "ReadAsync 681", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958205, "dur": 20, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958228, "dur": 19, "ph": "X", "name": "ReadAsync 711", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958250, "dur": 18, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958271, "dur": 18, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958292, "dur": 27, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958322, "dur": 18, "ph": "X", "name": "ReadAsync 800", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958343, "dur": 20, "ph": "X", "name": "ReadAsync 295", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958366, "dur": 20, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958389, "dur": 34, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958424, "dur": 1, "ph": "X", "name": "ProcessMessages 775", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958425, "dur": 21, "ph": "X", "name": "ReadAsync 775", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958449, "dur": 18, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958469, "dur": 1, "ph": "X", "name": "ProcessMessages 566", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958471, "dur": 19, "ph": "X", "name": "ReadAsync 566", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958493, "dur": 35, "ph": "X", "name": "ReadAsync 497", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958531, "dur": 414, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958950, "dur": 1, "ph": "X", "name": "ProcessMessages 281", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886958952, "dur": 174, "ph": "X", "name": "ReadAsync 281", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959129, "dur": 3, "ph": "X", "name": "ProcessMessages 4165", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959133, "dur": 26, "ph": "X", "name": "ReadAsync 4165", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959163, "dur": 24, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959190, "dur": 20, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959213, "dur": 82, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959298, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959302, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959352, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959354, "dur": 24, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959380, "dur": 49, "ph": "X", "name": "ReadAsync 143", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959434, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959475, "dur": 1, "ph": "X", "name": "ProcessMessages 818", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959477, "dur": 19, "ph": "X", "name": "ReadAsync 818", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959499, "dur": 163, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959664, "dur": 117, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959783, "dur": 1, "ph": "X", "name": "ProcessMessages 1992", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959786, "dur": 79, "ph": "X", "name": "ReadAsync 1992", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959868, "dur": 2, "ph": "X", "name": "ProcessMessages 1620", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959870, "dur": 22, "ph": "X", "name": "ReadAsync 1620", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959895, "dur": 32, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959930, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959953, "dur": 19, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959976, "dur": 18, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886959997, "dur": 50, "ph": "X", "name": "ReadAsync 197", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960050, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960073, "dur": 18, "ph": "X", "name": "ReadAsync 636", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960093, "dur": 17, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960113, "dur": 34, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960149, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960150, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960181, "dur": 17, "ph": "X", "name": "ReadAsync 952", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960201, "dur": 34, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960238, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960259, "dur": 19, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960281, "dur": 44, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960328, "dur": 92, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960424, "dur": 35, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960460, "dur": 1, "ph": "X", "name": "ProcessMessages 1502", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960462, "dur": 17, "ph": "X", "name": "ReadAsync 1502", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960481, "dur": 113, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960597, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960619, "dur": 1, "ph": "X", "name": "ProcessMessages 1072", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960621, "dur": 23, "ph": "X", "name": "ReadAsync 1072", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960647, "dur": 19, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960669, "dur": 16, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960687, "dur": 30, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960720, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960741, "dur": 22, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960766, "dur": 47, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960815, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960836, "dur": 17, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960857, "dur": 39, "ph": "X", "name": "ReadAsync 557", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960897, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960920, "dur": 17, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886960940, "dur": 57, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961000, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961031, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961033, "dur": 20, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961055, "dur": 29, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961087, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961110, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961111, "dur": 18, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961132, "dur": 16, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961151, "dur": 31, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961185, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961227, "dur": 20, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961249, "dur": 16, "ph": "X", "name": "ReadAsync 664", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961268, "dur": 32, "ph": "X", "name": "ReadAsync 139", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961302, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961323, "dur": 23, "ph": "X", "name": "ReadAsync 515", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961349, "dur": 342, "ph": "X", "name": "ReadAsync 573", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961700, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961705, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961771, "dur": 3, "ph": "X", "name": "ProcessMessages 4332", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961775, "dur": 100, "ph": "X", "name": "ReadAsync 4332", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961879, "dur": 24, "ph": "X", "name": "ReadAsync 796", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961905, "dur": 1, "ph": "X", "name": "ProcessMessages 727", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961908, "dur": 25, "ph": "X", "name": "ReadAsync 727", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886961937, "dur": 115, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962055, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962085, "dur": 1, "ph": "X", "name": "ProcessMessages 1094", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962087, "dur": 23, "ph": "X", "name": "ReadAsync 1094", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962111, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962113, "dur": 18, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962134, "dur": 37, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962173, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962195, "dur": 24, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962222, "dur": 17, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962242, "dur": 40, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962285, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962307, "dur": 22, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962331, "dur": 38, "ph": "X", "name": "ReadAsync 661", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962372, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962397, "dur": 19, "ph": "X", "name": "ReadAsync 774", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962419, "dur": 36, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962457, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962478, "dur": 18, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962499, "dur": 40, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962542, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962567, "dur": 18, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962588, "dur": 39, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962629, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962651, "dur": 19, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962673, "dur": 40, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962716, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962738, "dur": 19, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962760, "dur": 37, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962799, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962836, "dur": 15, "ph": "X", "name": "ReadAsync 996", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962854, "dur": 30, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962886, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962907, "dur": 21, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962931, "dur": 16, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962950, "dur": 29, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886962982, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963005, "dur": 22, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963029, "dur": 1, "ph": "X", "name": "ProcessMessages 931", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963031, "dur": 22, "ph": "X", "name": "ReadAsync 931", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963055, "dur": 1, "ph": "X", "name": "ProcessMessages 836", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963056, "dur": 20, "ph": "X", "name": "ReadAsync 836", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963079, "dur": 17, "ph": "X", "name": "ReadAsync 690", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963099, "dur": 35, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963136, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963160, "dur": 18, "ph": "X", "name": "ReadAsync 672", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963181, "dur": 19, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963203, "dur": 17, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963223, "dur": 23, "ph": "X", "name": "ReadAsync 165", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963248, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963270, "dur": 18, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963291, "dur": 17, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963310, "dur": 35, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963348, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963376, "dur": 1, "ph": "X", "name": "ProcessMessages 941", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963378, "dur": 17, "ph": "X", "name": "ReadAsync 941", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963398, "dur": 31, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963432, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963460, "dur": 21, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963483, "dur": 18, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963504, "dur": 40, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963547, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963571, "dur": 20, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963593, "dur": 17, "ph": "X", "name": "ReadAsync 496", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963613, "dur": 31, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963646, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963670, "dur": 20, "ph": "X", "name": "ReadAsync 643", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963694, "dur": 34, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963730, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963752, "dur": 18, "ph": "X", "name": "ReadAsync 613", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963773, "dur": 37, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963813, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963835, "dur": 19, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963857, "dur": 38, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963897, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963919, "dur": 18, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963939, "dur": 17, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963958, "dur": 32, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886963993, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964015, "dur": 19, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964036, "dur": 37, "ph": "X", "name": "ReadAsync 625", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964076, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964097, "dur": 21, "ph": "X", "name": "ReadAsync 589", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964120, "dur": 36, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964159, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964180, "dur": 19, "ph": "X", "name": "ReadAsync 605", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964201, "dur": 38, "ph": "X", "name": "ReadAsync 581", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964242, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964263, "dur": 18, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964283, "dur": 19, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964305, "dur": 21, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964329, "dur": 18, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964351, "dur": 17, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964370, "dur": 33, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964405, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964430, "dur": 18, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964451, "dur": 36, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964489, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964514, "dur": 19, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964535, "dur": 17, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964554, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964589, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964608, "dur": 20, "ph": "X", "name": "ReadAsync 242", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964631, "dur": 17, "ph": "X", "name": "ReadAsync 707", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964650, "dur": 30, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964683, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964722, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964724, "dur": 21, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964749, "dur": 34, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964786, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964879, "dur": 1, "ph": "X", "name": "ProcessMessages 2142", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964881, "dur": 18, "ph": "X", "name": "ReadAsync 2142", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964901, "dur": 18, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964922, "dur": 15, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886964940, "dur": 59, "ph": "X", "name": "ReadAsync 53", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965001, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965023, "dur": 22, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965047, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965049, "dur": 23, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965075, "dur": 18, "ph": "X", "name": "ReadAsync 811", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965096, "dur": 15, "ph": "X", "name": "ReadAsync 534", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965113, "dur": 18, "ph": "X", "name": "ReadAsync 74", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965134, "dur": 40, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965176, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965196, "dur": 358, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965558, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965608, "dur": 199, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965810, "dur": 87, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965900, "dur": 2, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965904, "dur": 38, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965944, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965947, "dur": 42, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965992, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886965994, "dur": 192, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966189, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966191, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966236, "dur": 1, "ph": "X", "name": "ProcessMessages 272", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966238, "dur": 30, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966271, "dur": 25, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966299, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966336, "dur": 52, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966392, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966394, "dur": 36, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966433, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966435, "dur": 170, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966610, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966659, "dur": 3, "ph": "X", "name": "ProcessMessages 468", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966664, "dur": 36, "ph": "X", "name": "ReadAsync 468", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966702, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966705, "dur": 49, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966757, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966759, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966794, "dur": 1, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966796, "dur": 39, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966838, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966840, "dur": 29, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966872, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966875, "dur": 35, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966912, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966914, "dur": 58, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966975, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886966977, "dur": 30, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967010, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967012, "dur": 109, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967125, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967127, "dur": 50, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967180, "dur": 3, "ph": "X", "name": "ProcessMessages 400", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967184, "dur": 40, "ph": "X", "name": "ReadAsync 400", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967226, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967228, "dur": 31, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967264, "dur": 2, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967267, "dur": 37, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967306, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967308, "dur": 35, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967345, "dur": 1, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967347, "dur": 25, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967374, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967376, "dur": 38, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967417, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967419, "dur": 43, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967464, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967466, "dur": 62, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967530, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967531, "dur": 32, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967565, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967568, "dur": 42, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967613, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967615, "dur": 32, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967649, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967652, "dur": 44, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967697, "dur": 1, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967700, "dur": 29, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967730, "dur": 1, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967732, "dur": 26, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967761, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967763, "dur": 29, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967794, "dur": 1, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967795, "dur": 57, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967856, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967888, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967890, "dur": 29, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967922, "dur": 1, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967925, "dur": 31, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967959, "dur": 1, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967962, "dur": 28, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967992, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886967994, "dur": 35, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886968033, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886968035, "dur": 66, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886968104, "dur": 2, "ph": "X", "name": "ProcessMessages 180", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886968108, "dur": 29, "ph": "X", "name": "ReadAsync 180", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886968140, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886968169, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886968193, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886968223, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886968306, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886968307, "dur": 108, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886968419, "dur": 15569, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886983995, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886983998, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886984052, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886984055, "dur": 2202, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886986262, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886986266, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886986299, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886986302, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886986336, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886986338, "dur": 54, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886986397, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886986429, "dur": 155, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886986588, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886986590, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886986636, "dur": 4882, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886991524, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886991526, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886991569, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886991571, "dur": 39, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886991615, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886991642, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886991863, "dur": 20, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886991886, "dur": 83, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886991972, "dur": 137, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992114, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992149, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992151, "dur": 42, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992198, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992232, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992267, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992295, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992296, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992340, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992365, "dur": 65, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992432, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992458, "dur": 27, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992488, "dur": 114, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992605, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992635, "dur": 72, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992710, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992741, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992770, "dur": 90, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992863, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886992890, "dur": 131, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993024, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993062, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993063, "dur": 29, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993095, "dur": 56, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993155, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993190, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993214, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993237, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993297, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993325, "dur": 90, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993418, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993449, "dur": 23, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993475, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993477, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993512, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993536, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993560, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993584, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993623, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993650, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993682, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993705, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993707, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993758, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993783, "dur": 20, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993806, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993840, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993842, "dur": 54, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993900, "dur": 39, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993941, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886993968, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994007, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994039, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994082, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994084, "dur": 27, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994116, "dur": 55, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994174, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994215, "dur": 28, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994247, "dur": 26, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994277, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994308, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994310, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994344, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994345, "dur": 155, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994505, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994542, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994577, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994659, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994690, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994722, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994756, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994800, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994802, "dur": 31, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994836, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994838, "dur": 24, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994866, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994896, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994898, "dur": 58, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994958, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994960, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886994990, "dur": 52, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995047, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995049, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995083, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995085, "dur": 42, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995131, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995133, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995182, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995184, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995219, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995221, "dur": 242, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995467, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995496, "dur": 40, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995540, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995563, "dur": 21, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995586, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995587, "dur": 24, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995615, "dur": 41, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995660, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995662, "dur": 50, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995716, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995752, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995799, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995801, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995834, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886995835, "dur": 202, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996042, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996068, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996167, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996226, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996227, "dur": 26, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996257, "dur": 21, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996281, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996314, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996348, "dur": 20, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996370, "dur": 45, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996420, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996446, "dur": 544, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886996993, "dur": 46, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997044, "dur": 3, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997049, "dur": 100, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997154, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997190, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997192, "dur": 45, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997242, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997269, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997270, "dur": 23, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997296, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997297, "dur": 18, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997317, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997336, "dur": 130, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997469, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997490, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997585, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997608, "dur": 59, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997670, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886997690, "dur": 334, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998040, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998043, "dur": 19, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998065, "dur": 16, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998083, "dur": 16, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998103, "dur": 15, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998121, "dur": 66, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998189, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998208, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998232, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998249, "dur": 22, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998274, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998292, "dur": 149, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998444, "dur": 15, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998462, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998489, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998517, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998543, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998707, "dur": 18, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998727, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998747, "dur": 17, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998766, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998803, "dur": 16, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886998822, "dur": 211, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886999036, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886999103, "dur": 21, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886999127, "dur": 21, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886999151, "dur": 17, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886999170, "dur": 17, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886999191, "dur": 494, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886999687, "dur": 61, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886999750, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301886999752, "dur": 45808, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887045568, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887045572, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887045601, "dur": 2449, "ph": "X", "name": "ProcessMessages 199", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887048054, "dur": 7288, "ph": "X", "name": "ReadAsync 199", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887055349, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887055354, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887055405, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887055407, "dur": 170, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887055581, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887055590, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887055624, "dur": 662, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056289, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056291, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056331, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056333, "dur": 95, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056431, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056461, "dur": 276, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056748, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056750, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056785, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056787, "dur": 56, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056847, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056849, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056885, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887056977, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887057005, "dur": 815, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887057824, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887057859, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887057860, "dur": 24, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887057887, "dur": 389, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887058279, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887058308, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887058309, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887058346, "dur": 626, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887058977, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887059006, "dur": 230, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887059240, "dur": 23, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887059267, "dur": 117, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887059386, "dur": 468, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887059858, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887059909, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887059911, "dur": 47, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887059961, "dur": 27, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887059991, "dur": 852, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887060847, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887060881, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887060883, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887061072, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887061100, "dur": 52, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887061156, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887061188, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887061190, "dur": 287, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887061481, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887061513, "dur": 26, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887061543, "dur": 40, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887061585, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887061587, "dur": 25, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887061617, "dur": 691, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887062312, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887062343, "dur": 390, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887062737, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887062739, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887062774, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887062776, "dur": 441, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063223, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063267, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063303, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063305, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063336, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063339, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063411, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063445, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063447, "dur": 42, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063495, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063524, "dur": 22, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063549, "dur": 20, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063572, "dur": 22, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063598, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063631, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063633, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063662, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063664, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063690, "dur": 23, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063715, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063717, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063745, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063778, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063808, "dur": 25, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063837, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063865, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063896, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063929, "dur": 25, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063957, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063959, "dur": 25, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887063988, "dur": 46, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064038, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064075, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064077, "dur": 36, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064115, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064117, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064153, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064155, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064185, "dur": 25, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064214, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064245, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064247, "dur": 28, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064277, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064279, "dur": 27, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064309, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064337, "dur": 26, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064367, "dur": 26, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064396, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064429, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064461, "dur": 23, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064486, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064488, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064515, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064517, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064541, "dur": 22, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064566, "dur": 22, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064591, "dur": 24, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064617, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064619, "dur": 21, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064643, "dur": 21, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064668, "dur": 25, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064697, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064698, "dur": 25, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064726, "dur": 22, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064751, "dur": 21, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064776, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064809, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064812, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064847, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064849, "dur": 21, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064872, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064903, "dur": 36, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064943, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887064945, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887065051, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887065054, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887065103, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887065105, "dur": 351, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887065461, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887065463, "dur": 94, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887065560, "dur": 1, "ph": "X", "name": "ProcessMessages 148", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887065564, "dur": 38, "ph": "X", "name": "ReadAsync 148", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887065605, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887065606, "dur": 203378, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887268993, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887268996, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887269021, "dur": 3374, "ph": "X", "name": "ProcessMessages 353", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887272398, "dur": 61608, "ph": "X", "name": "ReadAsync 353", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887334015, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887334018, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887334040, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301887334041, "dur": 1002892, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888336943, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888336947, "dur": 69, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888337021, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888337026, "dur": 27, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888337055, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888337057, "dur": 80768, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888417832, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888417835, "dur": 21, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888417857, "dur": 17, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888417875, "dur": 8392, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888426275, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888426278, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888426311, "dur": 20, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888426332, "dur": 9060, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888435398, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888435401, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888435439, "dur": 7561, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888443006, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888443008, "dur": 138, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888443150, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888443153, "dur": 1787, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888444946, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888444949, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888444987, "dur": 27, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888445016, "dur": 92657, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888537682, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888537686, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888537749, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301888537752, "dur": 995232, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301889532993, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301889532996, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301889533039, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301889533042, "dur": 1258, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301889534308, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301889534311, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301889534360, "dur": 27, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301889534390, "dur": 714, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301889535108, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301889535110, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301889535146, "dur": 383, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21312, "tid": 12884901888, "ts": 1753301889535531, "dur": 9539, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21312, "tid": 9069, "ts": 1753301889555520, "dur": 834, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21312, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21312, "tid": 8589934592, "ts": 1753301886946249, "dur": 65745, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21312, "tid": 8589934592, "ts": 1753301887011996, "dur": 4, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21312, "tid": 8589934592, "ts": 1753301887012000, "dur": 977, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21312, "tid": 9069, "ts": 1753301889556356, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21312, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21312, "tid": 4294967296, "ts": 1753301886819812, "dur": 2726109, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21312, "tid": 4294967296, "ts": 1753301886822643, "dur": 5163, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21312, "tid": 4294967296, "ts": 1753301889545933, "dur": 4265, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21312, "tid": 4294967296, "ts": 1753301889548120, "dur": 116, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21312, "tid": 4294967296, "ts": 1753301889550282, "dur": 25, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21312, "tid": 9069, "ts": 1753301889556364, "dur": 4, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1753301886841217, "dur": 1693, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753301886842920, "dur": 585, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753301886843610, "dur": 58, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1753301886843668, "dur": 529, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753301886844794, "dur": 105032, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D293A48779142A71.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753301886950584, "dur": 2184, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Unsafe.dll_D2F5C6CBE8D78A56.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1753301886954585, "dur": 122, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1753301886958933, "dur": 176, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.PixelPerfect.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1753301886961335, "dur": 57, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.2D.Tilemap.Extras.Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1753301886844220, "dur": 120908, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753301886965140, "dur": 2569209, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753301889534351, "dur": 382, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753301889534958, "dur": 53, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753301889535033, "dur": 1814, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1753301886844207, "dur": 120942, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886965170, "dur": 242, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886965430, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterRendererModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301886965426, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_49D0198E18ABFDE2.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753301886965650, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputLegacyModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301886965702, "dur": 84, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 1, "ts": 1753301886965649, "dur": 138, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_6FDD4B7A6EC84268.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753301886965788, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886965847, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301886965846, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIModule.dll_E5A3FD65734E3273.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753301886965958, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestWWWModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301886965957, "dur": 157, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_8837211AE3CE1746.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753301886966115, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886966278, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\PlaybackEngines\\WindowsStandaloneSupport\\UnityEditor.WindowsStandalone.Extensions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301886966276, "dur": 110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.WindowsStandalone.Extensions.dll_9F8263619ADFDEFD.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753301886966436, "dur": 200, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301886966435, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.TextureAssets.dll_E1772EF4FEB70289.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753301886966637, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886966791, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1753301886966905, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886967266, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886967366, "dur": 118, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886967562, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886967623, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886967736, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886967902, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886968773, "dur": 585, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301886967989, "dur": 1963, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886969952, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886971513, "dur": 535, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_EditorCoroutine.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753301886972220, "dur": 510, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Editor\\TMP_ColorGradientAssetMenu.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753301886970934, "dur": 2306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886974421, "dur": 597, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\VFXGraph\\VFXURPLitMeshOutput.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753301886973240, "dur": 1803, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886975501, "dur": 514, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Util\\AssertHelpers.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753301886976467, "dur": 526, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\ShaderGraphShortcuts.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753301886977227, "dur": 537, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Serialization\\MultiJsonEntry.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753301886975044, "dur": 2720, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886977765, "dur": 617, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Basic\\TimeNode.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753301886978641, "dur": 540, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\BlockNode.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753301886977765, "dur": 1500, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886979337, "dur": 633, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Documentation.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753301886979265, "dur": 1491, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886980757, "dur": 1282, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886982039, "dur": 1559, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886983599, "dur": 1620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886985219, "dur": 968, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886986197, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886986321, "dur": 1804, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886988125, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886989205, "dur": 1563, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886990768, "dur": 675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886991443, "dur": 441, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886991885, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753301886992019, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886992909, "dur": 101, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.2d.sprite@1.0.0\\Editor\\SpriteEditorModule\\SpriteOutlineModule.cs"}}, {"pid": 12345, "tid": 1, "ts": 1753301886992176, "dur": 902, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Sprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753301886993078, "dur": 473, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886993559, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886993618, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753301886993877, "dur": 693, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEditorBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753301886994570, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886994694, "dur": 334, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886995028, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886995197, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753301886995531, "dur": 505, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753301886996037, "dur": 153, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886996256, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753301886996364, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886996433, "dur": 641, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753301886997074, "dur": 140, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886997272, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1753301886997412, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1753301886997802, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301886997991, "dur": 55052, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301887053054, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887053212, "dur": 317, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.GraphViewModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887054440, "dur": 199, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887054776, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887055265, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887055626, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887055754, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887056345, "dur": 238, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.ResponseCaching.Abstractions.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887056827, "dur": 123, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Data.Common.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887058234, "dur": 691, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887053044, "dur": 5887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEngineBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753301887058932, "dur": 952, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301887060064, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887061392, "dur": 134, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.VisualBasic.Core.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887062187, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887062643, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887063150, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ObjectModel.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887063232, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887063350, "dur": 227, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Primitives.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887063600, "dur": 169, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 1, "ts": 1753301887059890, "dur": 3990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InternalAPIEditorBridge.001.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1753301887063881, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301887064109, "dur": 82, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301887064202, "dur": 180, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301887064493, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301887064618, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301887064759, "dur": 152, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301887064921, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1753301887064982, "dur": 2469359, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886844598, "dur": 121271, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886965881, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753301886965870, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_D0DC9EA7252E13B4.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753301886966105, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886966195, "dur": 219, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753301886966194, "dur": 222, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreTextEngineModule.dll_3F15606BE1109C0F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753301886966662, "dur": 397, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886967084, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886967291, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886967405, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886967604, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886967723, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5803490648119114145.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753301886967846, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3938377011463375229.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1753301886967954, "dur": 155, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886968113, "dur": 2269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886970383, "dur": 2122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886972505, "dur": 2010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886974516, "dur": 569, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\StateUnit.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753301886976176, "dur": 862, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.State\\INesterStateTransition.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753301886974515, "dur": 2524, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886977039, "dur": 1618, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886978812, "dur": 738, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Editor\\BurstDisassembler.Core.LLVMIR.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753301886979701, "dur": 805, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Editor\\BurstAotCompiler.cs"}}, {"pid": 12345, "tid": 2, "ts": 1753301886978657, "dur": 2240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886980897, "dur": 732, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886981630, "dur": 1048, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886982678, "dur": 1769, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886984448, "dur": 1533, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886985981, "dur": 1375, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886987356, "dur": 1347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886988703, "dur": 1159, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886989863, "dur": 683, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886990712, "dur": 728, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886991441, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886991888, "dur": 131, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753301886992019, "dur": 190, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886992213, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753301886992706, "dur": 278, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886993024, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886993089, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886993146, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886993541, "dur": 218, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886993760, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753301886994010, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886994162, "dur": 638, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753301886994801, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886995074, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886995179, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753301886995391, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886995722, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753301886996066, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886996190, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753301886996268, "dur": 389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753301886996657, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886996739, "dur": 70, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1753301886996829, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1753301886997108, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301886997203, "dur": 55782, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301887053476, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753301887055227, "dur": 446, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\WindowsBase.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753301887053004, "dur": 3540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753301887056545, "dur": 1267, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301887058041, "dur": 82, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753301887059333, "dur": 529, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Drawing.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753301887060291, "dur": 117, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753301887057820, "dur": 3554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753301887061379, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301887062324, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753301887063026, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Logging.EventSource.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753301887063572, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753301887063683, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Security.Principal.dll"}}, {"pid": 12345, "tid": 2, "ts": 1753301887061523, "dur": 3110, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1753301887064634, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301887064754, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301887064831, "dur": 1377844, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1753301888442681, "dur": 132, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753301888442680, "dur": 134, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753301888442845, "dur": 1939, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1753301888444787, "dur": 1089564, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886844302, "dur": 120872, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886965192, "dur": 236, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ARModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753301886965430, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 3, "ts": 1753301886965181, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ARModule.dll_A3A3243B74248B90.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753301886965569, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.DSPGraphModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753301886965568, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_4012322E9A5614BC.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753301886965683, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753301886965681, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PerformanceReportingModule.dll_85F6C15D73A578BD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753301886965860, "dur": 270, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753301886965858, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_F1B86C7E0BF881E0.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753301886966133, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886966297, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886966463, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886966528, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886966879, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886967057, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Animation.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1753301886967219, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886967296, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886967410, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886967670, "dur": 103, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886967800, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886968875, "dur": 549, "ph": "X", "name": "File", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753301886967996, "dur": 2072, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886970783, "dur": 546, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Manipulators\\Sequence\\TrackZoom.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753301886970069, "dur": 2085, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886973775, "dur": 545, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.State\\Plugin\\Changelogs\\Changelog_1_2_4.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753301886972155, "dur": 2728, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886974884, "dur": 697, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\GraphOutputWidget.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753301886976209, "dur": 931, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Control\\SwitchUnitDescriptor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753301886974884, "dur": 2630, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886977515, "dur": 1361, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886979032, "dur": 1219, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.common@8.0.2\\Path\\Editor\\EditorTool\\PathComponentEditor.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753301886978876, "dur": 1793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886980669, "dur": 2098, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886982767, "dur": 1044, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886983812, "dur": 1565, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886985377, "dur": 1947, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886987325, "dur": 1530, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886988855, "dur": 1837, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886990705, "dur": 750, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886991455, "dur": 463, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886991920, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1753301886992190, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886992908, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.cursor@2c0153a9ba\\Editor\\ProcessRunner.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753301886993048, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ide.cursor@2c0153a9ba\\Editor\\ProjectGeneration\\ProjectProperties.cs"}}, {"pid": 12345, "tid": 3, "ts": 1753301886992281, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Cursor.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753301886993193, "dur": 276, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886993478, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886993919, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753301886994092, "dur": 252, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753301886993781, "dur": 1051, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1753301886994833, "dur": 1279, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301886996114, "dur": 56866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301887052986, "dur": 2240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753301887055227, "dur": 266, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301887056733, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753301887055503, "dur": 2580, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753301887058084, "dur": 1238, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301887060975, "dur": 150, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.TypeExtensions.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753301887059329, "dur": 3178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753301887062508, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301887063232, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753301887064050, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authorization.Policy.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753301887064335, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Win32.Registry.dll"}}, {"pid": 12345, "tid": 3, "ts": 1753301887062688, "dur": 2564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1753301887065253, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1753301887065368, "dur": 2469006, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886844333, "dur": 120911, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886965258, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AudioModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753301886965253, "dur": 309, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AudioModule.dll_939D9295B43D54DB.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753301886965563, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886965697, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.Physics2DModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753301886965696, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.Physics2DModule.dll_0DA2B5FA22101DBE.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753301886965754, "dur": 254, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886966013, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753301886966012, "dur": 115, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.CoreModule.dll_4EAE93D6B17EFAD3.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753301886966127, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886966186, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIBuilderModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753301886966185, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIBuilderModule.dll_46FFF6FD196ECAB7.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753301886966254, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886966308, "dur": 300, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753301886966307, "dur": 303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Antlr3.Runtime.dll_1B6116B393E72286.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753301886966610, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886966741, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886966884, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886966971, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886967224, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886967415, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886967699, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753301886967844, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17766336155681823506.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1753301886967983, "dur": 1541, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886970463, "dur": 685, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio@2.0.22\\Editor\\VersionPair.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753301886969525, "dur": 2594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886972873, "dur": 779, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.State\\States\\StateDescription.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753301886972119, "dur": 2644, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886976601, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Invocations\\InvocationInspector.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753301886974763, "dur": 2624, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886977387, "dur": 1168, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886978639, "dur": 515, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Attributes\\BuiltinKeywordAttribute.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753301886979601, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\AssetCallbacks\\CreateShaderGraph.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753301886980769, "dur": 531, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Editor\\BurstMath.cs"}}, {"pid": 12345, "tid": 4, "ts": 1753301886978555, "dur": 2745, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886981300, "dur": 1567, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886982867, "dur": 2058, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886984925, "dur": 782, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886985707, "dur": 1326, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886987034, "dur": 1512, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886988546, "dur": 1099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886989645, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886990000, "dur": 911, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886990911, "dur": 582, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886991493, "dur": 397, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886991897, "dur": 101, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753301886992052, "dur": 849, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Path.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753301886992902, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886993176, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886993235, "dur": 300, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886993535, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753301886993686, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886993761, "dur": 283, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753301886994045, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886994112, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753301886994677, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886994904, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886995052, "dur": 162, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886995215, "dur": 2816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886998032, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1753301886998137, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1753301886998364, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301886998459, "dur": 54545, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887053133, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753301887055473, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753301887053006, "dur": 3411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753301887056417, "dur": 353, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887056798, "dur": 2826, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753301887059625, "dur": 237, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887061055, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Antiforgery.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753301887062328, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 4, "ts": 1753301887059870, "dur": 3062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Sprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1753301887062933, "dur": 426, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887063370, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887063433, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887063724, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887063900, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887063979, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887064042, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887064206, "dur": 387, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887064601, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887064779, "dur": 634, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1753301887065413, "dur": 2468955, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886844363, "dur": 121057, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886965441, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClusterInputModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301886965431, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterInputModule.dll_63198FADDC2DE5CF.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753301886965527, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886965680, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301886965679, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ParticleSystemModule.dll_D218FBB45FD12FE4.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753301886965751, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886965831, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TilemapModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301886965830, "dur": 69, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TilemapModule.dll_F44FCBA18E1E2303.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753301886965899, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886965994, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VRModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301886965990, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VRModule.dll_387623EB1508B7EB.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753301886966090, "dur": 342, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886966484, "dur": 214, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886966898, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886967091, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886967407, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886967706, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886967823, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886967877, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/6210570011482043853.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1753301886967945, "dur": 154, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886968101, "dur": 1739, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886969841, "dur": 1363, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886971205, "dur": 1917, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886973122, "dur": 1146, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886974269, "dur": 556, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\EditablePath\\IEditablePathController.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753301886974269, "dur": 2121, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886976535, "dur": 598, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\PropertyRow.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753301886976390, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886977745, "dur": 1404, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886979312, "dur": 608, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\Passes\\CapturePass.cs"}}, {"pid": 12345, "tid": 5, "ts": 1753301886979149, "dur": 1274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886980423, "dur": 2619, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886983042, "dur": 1770, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886984812, "dur": 1010, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886985822, "dur": 1179, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886987001, "dur": 1376, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886988377, "dur": 1566, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886989943, "dur": 1078, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886991021, "dur": 465, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886991486, "dur": 409, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886991904, "dur": 321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753301886992226, "dur": 457, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886993046, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301886993158, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Process.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301886992689, "dur": 937, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753301886993626, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886993955, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.HotReloadModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301886994135, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301886993876, "dur": 1121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1753301886994997, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886995079, "dur": 428, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886995510, "dur": 3499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301886999010, "dur": 73, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1753301886999116, "dur": 53876, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301887053032, "dur": 76, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301887054535, "dur": 125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Localization.Abstractions.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301887052993, "dur": 3119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753301887056113, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301887056258, "dur": 2498, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753301887058757, "dur": 143, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301887061141, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301887061870, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\EditorAssetResources\\Unity.VisualScripting.TextureAssets.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301887058913, "dur": 3137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.BurstCompatibilityGen.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753301887062051, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301887063942, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301887064374, "dur": 279, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ParticleSystemModule.dll"}}, {"pid": 12345, "tid": 5, "ts": 1753301887062242, "dur": 2972, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1753301887065215, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301887065333, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1753301887065405, "dur": 2468970, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886844292, "dur": 120874, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886965182, "dur": 242, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753301886965173, "dur": 284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AnimationModule.dll_C0FCC60981132629.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753301886965627, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886965709, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PropertiesModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753301886965708, "dur": 135, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PropertiesModule.dll_A964AEB46C39AB32.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753301886965843, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886965921, "dur": 357, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAudioModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753301886965919, "dur": 361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestAudioModule.dll_FDF3153BFA104355.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753301886966280, "dur": 255, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886966555, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886966644, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886966707, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886966773, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886966838, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886966893, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886967068, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886967148, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886967216, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886967286, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886967352, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886967454, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886967602, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886967866, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886967945, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886968002, "dur": 1886, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886969889, "dur": 583, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886970472, "dur": 730, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886971203, "dur": 1327, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886973078, "dur": 504, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Editor\\SkinningModule\\Undo\\UnityEngineUndo.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753301886973630, "dur": 565, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Editor\\SkinningModule\\Undo\\IUndo.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753301886972530, "dur": 2393, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886974924, "dur": 395, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886976520, "dur": 542, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\Fullscreen\\FullscreenShaderGUI.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753301886975320, "dur": 1742, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886978456, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Util\\PooledHashSet.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753301886977063, "dur": 1979, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886979156, "dur": 695, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\ShaderData.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753301886980599, "dur": 517, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\RendererFeatures\\ScreenSpaceAmbientOcclusion.cs"}}, {"pid": 12345, "tid": 6, "ts": 1753301886979042, "dur": 2124, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886981166, "dur": 1266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886982432, "dur": 1714, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886984147, "dur": 1079, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886985227, "dur": 1494, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886986722, "dur": 1355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886988077, "dur": 1612, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886989690, "dur": 1311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886991001, "dur": 476, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886991477, "dur": 432, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886991910, "dur": 411, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753301886992321, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886992908, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\unityplastic.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753301886992410, "dur": 876, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753301886993286, "dur": 127, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886993422, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886993520, "dur": 274, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753301886993838, "dur": 613, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753301886994452, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886994650, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886994707, "dur": 305, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886995012, "dur": 167, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886995199, "dur": 252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753301886995451, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886995519, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753301886996067, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886996274, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1753301886996422, "dur": 1030, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753301886997452, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886997614, "dur": 364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753301886997978, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886998135, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.PixelPerfect.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1753301886998335, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301886998415, "dur": 54601, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887054673, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Drawing.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753301887055092, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753301887055425, "dur": 138, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753301887053017, "dur": 3180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753301887056197, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887058048, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753301887056367, "dur": 2036, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753301887058403, "dur": 1515, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887060135, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753301887060906, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\log4netPlastic.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753301887061418, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Session.dll"}}, {"pid": 12345, "tid": 6, "ts": 1753301887059925, "dur": 3167, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1753301887063093, "dur": 169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887063277, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887063350, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887063550, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887063898, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887064001, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887064075, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887064136, "dur": 163, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887064305, "dur": 88, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887064422, "dur": 295, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887064768, "dur": 578, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887065350, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887065447, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1753301887065505, "dur": 2468866, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886844321, "dur": 120902, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886965234, "dur": 272, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AssetBundleModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301886965229, "dur": 279, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AssetBundleModule.dll_DE98305BD6D68C77.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753301886965509, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886965588, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GridModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301886965587, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GridModule.dll_4F40831771D8FBF5.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753301886965642, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886965758, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.StreamingModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301886965756, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.StreamingModule.dll_7591B359D09ADD67.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753301886965896, "dur": 230, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886966130, "dur": 142, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsSamplesModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301886966129, "dur": 145, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsSamplesModule.dll_7406CA3F32B5E22E.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753301886966275, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886966547, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886966611, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753301886966721, "dur": 335, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886967093, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886967322, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886967446, "dur": 166, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886967618, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886967683, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753301886967749, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886967882, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17738801749825545598.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1753301886967944, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886968215, "dur": 2180, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886970395, "dur": 976, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886971372, "dur": 2026, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886973399, "dur": 1561, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886975044, "dur": 824, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Description\\FlowMachineDescriptor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753301886974960, "dur": 1643, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886976604, "dur": 2263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886978959, "dur": 750, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.spriteshape@9.0.3\\Runtime\\External\\LibTessDotNet\\PriorityHeap.cs"}}, {"pid": 12345, "tid": 7, "ts": 1753301886978867, "dur": 1322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886980190, "dur": 1887, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886982077, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886983168, "dur": 1134, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886984302, "dur": 847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886985149, "dur": 1449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886986599, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886988157, "dur": 1425, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886989582, "dur": 919, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886990661, "dur": 50, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886990711, "dur": 733, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886991444, "dur": 444, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886991889, "dur": 548, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753301886992438, "dur": 199, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886992863, "dur": 96, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301886993046, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301886993304, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.Tasks.Parallel.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301886993523, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.Xml.Linq.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301886992648, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753301886993648, "dur": 201, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886993904, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886993999, "dur": 68, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886994074, "dur": 194, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886994273, "dur": 740, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886995013, "dur": 164, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886995178, "dur": 228, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1753301886995407, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886995478, "dur": 488, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.BurstCompatibilityGen.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1753301886995966, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301886996154, "dur": 56842, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301887053015, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DiagnosticsModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301887052999, "dur": 3094, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753301887056094, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301887057318, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-crt-time-l1-1-0.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301887058241, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Tasks.Dataflow.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301887058903, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301887059108, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301887056234, "dur": 3099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753301887059334, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301887060789, "dur": 197, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.FileExtensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301887061418, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Private.Xml.dll"}}, {"pid": 12345, "tid": 7, "ts": ****************, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Extensions.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301887061902, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301887062641, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301887062785, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301887059499, "dur": 3554, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Aseprite.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1753301887063054, "dur": 206, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301887063270, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301887063345, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301887063344, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.dll"}}, {"pid": 12345, "tid": 7, "ts": 1753301887063408, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301887063589, "dur": 307, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301887063904, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301887063995, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301887064059, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301887064164, "dur": 636, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301887064804, "dur": 703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1753301887065507, "dur": 2468839, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886844353, "dur": 120917, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886965278, "dur": 364, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886965274, "dur": 371, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClothModule.dll_FA85EF908E5A33BF.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753301886965645, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886965705, "dur": 220, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886965704, "dur": 223, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ProfilerModule.dll_6D0709FBC14ECAA8.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753301886966069, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886966255, "dur": 146, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886966254, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Graphs.dll_5AAB6A659692F205.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753301886966403, "dur": 415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886966834, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753301886967040, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886967232, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886967305, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886967535, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886967627, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886967781, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886967870, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ServiceModel.Web.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886967925, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886968020, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Windows.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886968185, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886968309, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886968373, "dur": 164, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886968538, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886968601, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886968704, "dur": 171, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886968876, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.EventBasedAsync.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886968949, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886969015, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886969160, "dur": 218, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886969473, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886969580, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886969685, "dur": 73, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886969842, "dur": 320, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886970165, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886970242, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886970303, "dur": 378, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886970682, "dur": 444, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.IsolatedStorage.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886971128, "dur": 233, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886971369, "dur": 334, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886971792, "dur": 173, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.WebSockets.Client.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886972111, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886972353, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Resources.ResourceManager.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886972412, "dur": 339, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886972757, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886972993, "dur": 701, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886973696, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Json.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886973754, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886973818, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886973911, "dur": 263, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Algorithms.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886974178, "dur": 288, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886974552, "dur": 546, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Tasks.Extensions.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886975105, "dur": 321, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886975500, "dur": 517, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\ConstraintsExtensions.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886976039, "dur": 436, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\Is.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886976562, "dur": 457, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\LogScope\\LogScope.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886977125, "dur": 419, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Assertions\\UnityTestTimeoutException.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886977808, "dur": 1031, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableApplyChangesToContextCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886978922, "dur": 666, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Commands\\EnumerableTestState.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886979887, "dur": 419, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\CompositeWorkItem.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886980358, "dur": 222, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\FailCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886980606, "dur": 290, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\PlaymodeWorkItemFactory.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886980901, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\RestoreTestContextAfterDomainReload.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886980968, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\TestCommandBuilder.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886981020, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityLogCheckDelegatingCommand.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886981075, "dur": 292, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestAssemblyRunner.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886981368, "dur": 247, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\Runner\\UnityTestExecutionContext.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886981711, "dur": 246, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\NUnitExtensions\\TestResultExtensions.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886981988, "dur": 962, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\Callbacks\\PlayerQuitHandler.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886983199, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RemoteHelpers\\RemoteTestResultDataFactory.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886983547, "dur": 269, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\RuntimeTestRunnerFilter.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886983861, "dur": 376, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\TestRunner\\TestListenerWrapper.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886984345, "dur": 186, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\IScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886984532, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\PlayerTestAssemblyProvider.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886984595, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AssemblyProvider\\ScriptingRuntimeProxy.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886984648, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\AttributeHelper.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886984710, "dur": 116, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\ColorEqualityComparer.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886984901, "dur": 267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\IPostBuildCleanup.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886985331, "dur": 160, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\StacktraceFilter.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886985492, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackAttribute.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886985546, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\TestRunCallbackListener.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886985686, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector2EqualityComparer.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886985944, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.test-framework@1.1.33\\UnityEngine.TestRunner\\Utils\\Vector4EqualityComparer.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886967136, "dur": 18895, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753301886986032, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886986198, "dur": 139, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886986353, "dur": 91, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753301886986445, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886986654, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886986716, "dur": 165, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\netstandard.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886986886, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.AppContext.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886986946, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886987002, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886987104, "dur": 90, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886987195, "dur": 229, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886987426, "dur": 53, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.Annotations.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886987622, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ComponentModel.TypeConverter.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886987774, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Contracts.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886987826, "dur": 79, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886987906, "dur": 131, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886988109, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.Tools.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886988193, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886988282, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Drawing.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886988335, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886988436, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.Calendars.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886988683, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886988787, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886988925, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.UnmanagedMemoryStream.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886989122, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Linq.Queryable.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886989174, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Memory.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886989285, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886989336, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886989388, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NetworkInformation.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886989493, "dur": 121, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Primitives.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886989615, "dur": 107, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886989879, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886989991, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886990091, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886990408, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Resources.Writer.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886990465, "dur": 103, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.CompilerServices.VisualC.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886990654, "dur": 74, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886986512, "dur": 4828, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753301886991555, "dur": 200, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753301886991892, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753301886992085, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886992503, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886992907, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Runtime\\Intrinsics\\Arm\\NEON.cs"}}, {"pid": 12345, "tid": 8, "ts": 1753301886992064, "dur": 974, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753301886993039, "dur": 366, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886993421, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886993489, "dur": 510, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1753301886994095, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AccessibilityModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301886994040, "dur": 668, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1753301886994708, "dur": 270, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886994995, "dur": 751, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1753301886995783, "dur": 144, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301886996383, "dur": 49103, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 8, "ts": 1753301887052986, "dur": 2020, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753301887055007, "dur": 1656, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887056674, "dur": 2815, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753301887059489, "dur": 272, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887060771, "dur": 83, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-debug-l1-1-0.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301887061788, "dur": 91, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301887062168, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 8, "ts": 1753301887059769, "dur": 2844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Psdimporter.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1753301887062614, "dur": 588, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887063212, "dur": 360, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887063579, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887063638, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887063721, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887063822, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887063883, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887064026, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887064262, "dur": 480, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887064747, "dur": 83, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887064833, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1753301887064895, "dur": 2469460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886844381, "dur": 121187, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886965590, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GameCenterModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301886965580, "dur": 87, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.GameCenterModule.dll_7C296F75F94E46F6.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753301886965668, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886965748, "dur": 59, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SharedInternalsModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301886965747, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SharedInternalsModule.dll_39D3561411FEB126.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753301886965809, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886965880, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityCurlModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301886965879, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_F419673AF1271816.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753301886965992, "dur": 157, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.WindModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301886965991, "dur": 160, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.WindModule.dll_E81E4251EABAE467.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753301886966202, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.SceneViewModule.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301886966201, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.SceneViewModule.dll_31272CE89BD91E91.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753301886966315, "dur": 124, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Dependencies\\YamlDotNet\\Unity.VisualScripting.YamlDotNet.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301886966313, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.YamlDotNet.dll_4A5905A13DE9A491.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753301886966737, "dur": 290, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886967106, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886967175, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886967292, "dur": 129, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886967427, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886967568, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886967690, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/11344994280883157806.rsp"}}, {"pid": 12345, "tid": 9, "ts": 1753301886967938, "dur": 193, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886968139, "dur": 2086, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886970225, "dur": 741, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886971705, "dur": 583, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.rider@3.0.28\\Rider\\Editor\\Util\\CommandLineParser.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753301886970966, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886972813, "dur": 877, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886974924, "dur": 683, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\Camera\\UniversalRenderPipelineCameraUI.Skin.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753301886973691, "dur": 2868, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886976560, "dur": 1334, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886978486, "dur": 547, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Artistic\\Blend\\BlendMode.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753301886979033, "dur": 722, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Artistic\\Adjustment\\WhiteBalanceNode.cs"}}, {"pid": 12345, "tid": 9, "ts": 1753301886977895, "dur": 2793, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886980688, "dur": 2100, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886982788, "dur": 2096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886984885, "dur": 1562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886986447, "dur": 939, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886987386, "dur": 1091, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886988478, "dur": 1354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886989832, "dur": 1129, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886990962, "dur": 516, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886991479, "dur": 447, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886991928, "dur": 427, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753301886992355, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886992909, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301886992413, "dur": 742, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753301886993156, "dur": 228, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886993421, "dur": 112, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886993534, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PsdPlugin.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753301886993671, "dur": 98, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886993775, "dur": 328, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753301886994103, "dur": 70, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886994179, "dur": 446, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Aseprite.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753301886994625, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886994834, "dur": 177, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886995014, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886995071, "dur": 156, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886995227, "dur": 2845, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886998073, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 9, "ts": 1753301886998222, "dur": 352, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 9, "ts": 1753301886998575, "dur": 121, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301886998701, "dur": 54287, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301887052990, "dur": 3092, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753301887056086, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301887056736, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Buffers.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301887056217, "dur": 2824, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753301887059041, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301887060977, "dur": 136, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.FileSystem.Primitives.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301887061147, "dur": 279, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.NameResolution.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301887061666, "dur": 140, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.IO.Compression.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301887059179, "dur": 2743, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.DocCodeSamples.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753301887061923, "dur": 743, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301887062696, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Unity.2D.Tilemap.Editor.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301887063017, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301887063149, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.AppContext.dll"}}, {"pid": 12345, "tid": 9, "ts": 1753301887062695, "dur": 2654, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 9, "ts": 1753301887065350, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 9, "ts": 1753301887065456, "dur": 2468922, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886844399, "dur": 121218, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886965700, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PhysicsModule.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753301886965699, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_3B0CE8B1A0A2F90C.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753301886966002, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886966250, "dur": 439, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886966720, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886966785, "dur": 225, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886967318, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886967766, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886967889, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886967959, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886969201, "dur": 679, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886970062, "dur": 570, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline@1.7.6\\Editor\\Utilities\\KeyTraverser.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753301886969880, "dur": 1673, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886972823, "dur": 730, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.psdimporter@8.0.5\\Editor\\PSDPlugin\\PDNWrapper\\Surface.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753301886971553, "dur": 2082, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886973635, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886974903, "dur": 1458, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886976440, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Extensions\\IConditionalExtensions.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753301886977899, "dur": 584, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\Slots\\ColorRGBSlotControlView.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753301886976361, "dur": 2122, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886978483, "dur": 386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886978869, "dur": 365, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886979234, "dur": 352, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886979587, "dur": 1176, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886980764, "dur": 1767, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886982826, "dur": 584, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Utilities\\SearchResult.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753301886982532, "dur": 1859, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886984391, "dur": 1325, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886985716, "dur": 1338, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886987054, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886988299, "dur": 1319, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886989651, "dur": 1317, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886990968, "dur": 510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886991478, "dur": 433, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886991912, "dur": 564, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753301886992477, "dur": 463, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886993752, "dur": 85, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Editor\\PlasticSCM\\UI\\BoolSetting.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753301886993853, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Editor\\PlasticSCM\\UI\\DrawActionHelpBox.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753301886993965, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Editor\\PlasticSCM\\UI\\HandleMenuItem.cs"}}, {"pid": 12345, "tid": 10, "ts": 1753301886992950, "dur": 1534, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753301886994485, "dur": 142, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886994636, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886994693, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 10, "ts": 1753301886994838, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886994894, "dur": 458, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 10, "ts": 1753301886995352, "dur": 369, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301886995724, "dur": 57250, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301887052976, "dur": 2821, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753301887055798, "dur": 1101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301887057625, "dur": 110, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753301887056907, "dur": 1832, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753301887058740, "dur": 177, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301887058921, "dur": 2010, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PsdPlugin.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753301887060931, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301887061417, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.ReaderWriter.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753301887062188, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Http.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753301887062324, "dur": 329, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.VisualBasic.Core.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753301887061001, "dur": 2672, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 10, "ts": 1753301887063674, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301887063830, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301887063907, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301887064024, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301887064094, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301887064198, "dur": 187, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301887064666, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301887064820, "dur": 1370294, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301888435116, "dur": 102398, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753301888435115, "dur": 102401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 10, "ts": 1753301888537517, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 10, "ts": 1753301888537583, "dur": 996777, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886844424, "dur": 121210, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886965651, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.IMGUIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301886965643, "dur": 105, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_CBFD31FC7A6EE8BC.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753301886965749, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886965827, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextRenderingModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301886965825, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextRenderingModule.dll_3AA9E1B89A839D24.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753301886965891, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886965951, "dur": 156, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestTextureModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301886965950, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_1CE9B2E479BEC5CA.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753301886966113, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886966287, "dur": 245, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886966545, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886966661, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886966742, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886966795, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886966877, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886966994, "dur": 112, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886967131, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886967213, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886967407, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886967566, "dur": 105, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886967716, "dur": 152, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886967875, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15312589170787849123.rsp"}}, {"pid": 12345, "tid": 11, "ts": 1753301886967931, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886967993, "dur": 1607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886969600, "dur": 2585, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886972185, "dur": 846, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886973031, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886973793, "dur": 1888, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886976401, "dur": 587, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGraph\\Targets\\BuiltInLitSubTarget.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753301886975682, "dur": 1615, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886977298, "dur": 609, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886977966, "dur": 697, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Legacy\\ShaderInput0.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753301886978844, "dur": 660, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Interfaces\\IPropertyDrawer.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753301886977907, "dur": 1736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886979643, "dur": 572, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\2D\\UTess2D\\UTess.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753301886980563, "dur": 646, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\2D\\UTess2D\\ArraySlice.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753301886979643, "dur": 2233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886981876, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Control\\Once.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753301886981876, "dur": 1633, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886983509, "dur": 672, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Inspection\\Special\\EnumInspector.cs"}}, {"pid": 12345, "tid": 11, "ts": 1753301886983509, "dur": 2057, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886985566, "dur": 1127, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886986693, "dur": 1194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886987888, "dur": 1423, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886989312, "dur": 1474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886990787, "dur": 652, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886991487, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886991905, "dur": 1037, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753301886992943, "dur": 173, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886993224, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.GIModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301886993282, "dur": 105, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ImageConversionModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301886993526, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Drawing.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301886993965, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301886993121, "dur": 989, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753301886994110, "dur": 646, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886994806, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 11, "ts": 1753301886995200, "dur": 98, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Diagnostics.TextWriterTraceListener.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301886994972, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 11, "ts": 1753301886995656, "dur": 577, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886996242, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301886996302, "dur": 56670, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301887052973, "dur": 1933, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753301887054907, "dur": 3349, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301887059334, "dur": 1214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AnimationModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301887060604, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301887058262, "dur": 3099, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753301887061361, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301887062324, "dur": 329, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Security.Cryptography.X509Certificates.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301887063058, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Rewrite.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301887063154, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Session.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301887063542, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Net.WebProxy.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301887064191, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 11, "ts": 1753301887061464, "dur": 3302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 11, "ts": 1753301887064767, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 11, "ts": 1753301887064863, "dur": 2469481, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886844441, "dur": 121220, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886965681, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.InputModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753301886965672, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_FA135A462A75797F.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753301886965859, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityAnalyticsCommonModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753301886965857, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_21357EA18D8E171B.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753301886966010, "dur": 109, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.DeviceSimulatorModule.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753301886966009, "dur": 112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DeviceSimulatorModule.dll_4423571CA8F0F05E.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753301886966122, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886966301, "dur": 213, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Dependencies\\NCalc\\Unity.VisualScripting.Antlr3.Runtime.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753301886966300, "dur": 215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.VisualScripting.Antlr3.Runtime.dll_FBC62C826EB2B4B0.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753301886966589, "dur": 141, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753301886966588, "dur": 144, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.dll_62273FC84B3E0F2D.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753301886966866, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886967158, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886967390, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Extras.Editor.rsp2"}}, {"pid": 12345, "tid": 12, "ts": 1753301886967884, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 12, "ts": 1753301886967968, "dur": 1743, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886969711, "dur": 2172, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886971883, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886973229, "dur": 1617, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886976238, "dur": 736, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\NesterUnitOption.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753301886974846, "dur": 2398, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886977244, "dur": 913, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886978158, "dur": 761, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886978999, "dur": 679, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\XR\\XRPassUniversal.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753301886979939, "dur": 634, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\UniversalAdditionalCameraData.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753301886978919, "dur": 1938, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886980915, "dur": 605, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Math\\Add.cs"}}, {"pid": 12345, "tid": 12, "ts": 1753301886980857, "dur": 1455, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886982312, "dur": 2335, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886984648, "dur": 1234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886985882, "dur": 1245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886987127, "dur": 918, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886988046, "dur": 1224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886989270, "dur": 1198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886990468, "dur": 411, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886990879, "dur": 559, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886991476, "dur": 436, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886991913, "dur": 416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753301886992373, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753301886992889, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886992972, "dur": 181, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEditor.Graphs.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753301886993803, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Threading.Thread.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753301886993949, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753301886992971, "dur": 1112, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753301886994084, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886994234, "dur": 629, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886994864, "dur": 132, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753301886994996, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886995053, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753301886995543, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886995733, "dur": 714, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886996450, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 12, "ts": 1753301886996534, "dur": 241, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.SpriteShape.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 12, "ts": 1753301886996775, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301886996878, "dur": 56091, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301887052971, "dur": 2202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753301887055174, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301887055294, "dur": 2178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.PixelPerfect.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753301887057476, "dur": 733, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301887058903, "dur": 444, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.OAuth.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753301887058216, "dur": 2298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.SpriteShape.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753301887060515, "dur": 260, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301887061057, "dur": 102, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.MemoryMappedFiles.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753301887062189, "dur": 258, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753301887062637, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Threading.Timer.dll"}}, {"pid": 12345, "tid": 12, "ts": 1753301887060783, "dur": 2932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 12, "ts": 1753301887063716, "dur": 519, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301887064293, "dur": 586, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 12, "ts": 1753301887064882, "dur": 2469460, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886844462, "dur": 121212, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886965701, "dur": 61, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.LocalizationModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301886965693, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_A1665A0073A9D8C8.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753301886965764, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886965907, "dur": 193, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.EditorToolbarModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301886965906, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_41265C97D041BE6E.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753301886966127, "dur": 113, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UnityConnectModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301886966126, "dur": 116, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UnityConnectModule.dll_E37D1A982AA61309.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753301886966314, "dur": 109, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886966426, "dur": 264, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886966701, "dur": 367, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886967077, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753301886967428, "dur": 145, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886967701, "dur": 56, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/3410860103080533286.rsp"}}, {"pid": 12345, "tid": 13, "ts": 1753301886967974, "dur": 1328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886969303, "dur": 1157, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886970460, "dur": 1040, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886971500, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886973508, "dur": 541, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.State\\Transitions\\NesterStateTransitionDescriptor.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753301886972092, "dur": 2259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886974352, "dur": 1340, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886976156, "dur": 808, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\OutputMetadata.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753301886977913, "dur": 655, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Enumerations\\RenderQueue.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753301886975692, "dur": 3399, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886979130, "dur": 707, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Runtime\\RendererFeatures\\DisallowMultipleRendererFeature.cs"}}, {"pid": 12345, "tid": 13, "ts": 1753301886979091, "dur": 2099, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886981190, "dur": 2306, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886983496, "dur": 2000, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886985497, "dur": 1177, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886986674, "dur": 522, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886987196, "dur": 1315, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886988512, "dur": 982, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886989495, "dur": 1436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886990931, "dur": 556, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886991488, "dur": 551, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886992040, "dur": 118, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753301886992159, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886992221, "dur": 133, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 13, "ts": 1753301886992355, "dur": 736, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886993133, "dur": 70, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301886993510, "dur": 77, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301886993660, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Security.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301886993799, "dur": 161, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Claims.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301886993095, "dur": 984, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 13, "ts": 1753301886994079, "dur": 1562, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301886995685, "dur": 57293, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301887053653, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Ping.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301887054220, "dur": 56, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Reflection.DispatchProxy.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301887054501, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XmlDocument.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301887054688, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\api-ms-win-core-libraryloader-l1-1-0.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301887054991, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Grpc.Net.Common.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301887055222, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Mvc.Cors.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301887055475, "dur": 2159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Options.DataAnnotations.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301887057722, "dur": 534, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.Debug.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301887058904, "dur": 445, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301887052979, "dur": 6694, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753301887059673, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301887060789, "dur": 69, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Identity.Stores.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301887061031, "dur": 86, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.IO.Compression.FileSystem.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301887062323, "dur": 331, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Threading.ThreadPool.dll"}}, {"pid": 12345, "tid": 13, "ts": ****************, "dur": 358, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\mscorlib.dll"}}, {"pid": 12345, "tid": 13, "ts": 1753301887059761, "dur": 3393, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 13, "ts": 1753301887063154, "dur": 1340, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301887064533, "dur": 339, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 13, "ts": 1753301887064875, "dur": 2469472, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886844480, "dur": 121228, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886965731, "dur": 88, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753301886965720, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll_E586C88FEB060A87.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753301886965824, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886966025, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886966165, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886966289, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886966563, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\Unity.CompilationPipeline.Common.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753301886966562, "dur": 95, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_8F519FE4E54F1B21.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753301886966657, "dur": 168, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886966836, "dur": 123, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886966963, "dur": 146, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886967608, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886967692, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/8899139255040401798.rsp"}}, {"pid": 12345, "tid": 14, "ts": 1753301886967971, "dur": 1300, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886969272, "dur": 1740, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886971013, "dur": 1377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886972976, "dur": 700, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\IK\\Runtime\\IKManager2DEditorData.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753301886974585, "dur": 946, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Editor\\UpgradeTools\\Utilities\\ButtonStripField.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753301886975629, "dur": 867, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Editor\\SpriteSkin\\TransformExtensions.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753301886972390, "dur": 4106, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886976496, "dur": 1039, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886978415, "dur": 1199, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Texture\\SampleTexture3DNode.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753301886979634, "dur": 555, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Texture\\SampleTexture2DLODNode.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753301886977535, "dur": 3118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886982030, "dur": 631, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\ISceneVariableUnit.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753301886980653, "dur": 2092, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886982745, "dur": 2004, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886984749, "dur": 1219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886985968, "dur": 1231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886987200, "dur": 1268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886988468, "dur": 929, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886989397, "dur": 1476, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886990873, "dur": 606, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886991480, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886991909, "dur": 305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753301886992214, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886992909, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMPro_ExtensionMethods.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753301886993047, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.textmeshpro@3.0.6\\Scripts\\Runtime\\TMP_MaterialManager.cs"}}, {"pid": 12345, "tid": 14, "ts": 1753301886992277, "dur": 897, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753301886993174, "dur": 1415, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886994602, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886994662, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 14, "ts": 1753301886994775, "dur": 542, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 14, "ts": 1753301886995317, "dur": 175, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301886995550, "dur": 17272, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301887012823, "dur": 2968, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301887015791, "dur": 37203, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301887055228, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753301887052995, "dur": 3146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753301887056142, "dur": 101, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301887056480, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753301887056249, "dur": 2772, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Tilemap.Extras.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753301887059022, "dur": 2385, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301887063349, "dur": 231, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ClothModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753301887063770, "dur": 451, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VirtualTexturingModule.dll"}}, {"pid": 12345, "tid": 14, "ts": 1753301887061418, "dur": 3396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 14, "ts": 1753301887064814, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 14, "ts": 1753301887064941, "dur": 2469439, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886844496, "dur": 121223, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886965997, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.DiagnosticsModule.dll_0F9F7C5D1B98DB2C.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753301886966133, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886966298, "dur": 168, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886966297, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Collections.LowLevel.ILSupport.dll_2E40CE80E145E521.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753301886966469, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886966538, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886966668, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886966834, "dur": 62, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753301886966897, "dur": 165, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886967206, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\mscorlib.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886967313, "dur": 106, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886967420, "dur": 158, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Core.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886967651, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Net.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886967730, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Numerics.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886967782, "dur": 155, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Runtime.Serialization.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886967939, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Transactions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886967995, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Web.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886968183, "dur": 108, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.Xml.Serialization.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886968366, "dur": 245, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Concurrent.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886968614, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886968696, "dur": 395, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Collections.Specialized.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886969180, "dur": 166, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.FileVersionInfo.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886969477, "dur": 57, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.Tracing.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886969562, "dur": 178, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Dynamic.Runtime.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886969768, "dur": 290, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Globalization.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886970100, "dur": 262, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.DriveInfo.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886970365, "dur": 740, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.FileSystem.Watcher.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886971231, "dur": 214, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886971594, "dur": 297, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Net.Requests.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886972013, "dur": 313, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886972480, "dur": 347, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886972829, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.InteropServices.RuntimeInformation.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886972900, "dur": 119, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Numerics.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886973021, "dur": 729, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Formatters.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886973753, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886973826, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Runtime.Serialization.Xml.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886974064, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Security.Cryptography.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886974426, "dur": 320, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Text.RegularExpressions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886974902, "dur": 259, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886975354, "dur": 413, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Targets\\BuiltIn\\Editor\\ShaderGUI\\MaterialAssemblyReference\\RawRenderQueue.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886975983, "dur": 384, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\BaseInputModule.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886976367, "dur": 617, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\PointerInputModule.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886976985, "dur": 97, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\InputModules\\StandaloneInputModule.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886977181, "dur": 191, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\BaseRaycaster.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886977373, "dur": 143, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\Physics2DRaycaster.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886977517, "dur": 381, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\Raycasters\\PhysicsRaycaster.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886977899, "dur": 307, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\RaycastResult.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886978207, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIBehaviour.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886978259, "dur": 394, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelEventHandler.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886978654, "dur": 547, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\EventSystem\\UIElements\\PanelRaycaster.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886979411, "dur": 698, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\IClipRegion.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886980113, "dur": 72, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Culling\\RectangularVertexClipper.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886980186, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\DefaultControls.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886980409, "dur": 112, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRaycaster.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886980522, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRebuildTracker.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886980573, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\GraphicRegistry.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886980907, "dur": 470, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\InputField.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886981520, "dur": 201, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\ILayoutElement.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886981743, "dur": 281, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\LayoutGroup.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886982063, "dur": 291, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Layout\\VerticalLayoutGroup.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886982749, "dur": 305, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\ReflectionMethodsCache.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886983055, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\Utility\\VertexHelper.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886983107, "dur": 374, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\BaseMeshEffect.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886983482, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.ugui@1.0.0\\Runtime\\UI\\Core\\VertexModifiers\\IMeshModifier.cs"}}, {"pid": 12345, "tid": 15, "ts": 1753301886967074, "dur": 16568, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753301886983642, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886983932, "dur": 1999, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886985931, "dur": 1607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886987538, "dur": 1195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886988733, "dur": 1710, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886990444, "dur": 592, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886991036, "dur": 428, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886991464, "dur": 450, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886991919, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753301886992469, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886992909, "dur": 100, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Runtime.Handles.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886993048, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.NetTcp.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886993159, "dur": 128, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301886992543, "dur": 1016, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753301886993560, "dur": 476, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886994052, "dur": 1024, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886995081, "dur": 119, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886995200, "dur": 1543, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886996743, "dur": 96, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 15, "ts": 1753301886996873, "dur": 258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 15, "ts": 1753301886997131, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301886997223, "dur": 57449, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301887055227, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Globalization.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887055473, "dur": 1049, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887056734, "dur": 130, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\hostfxr.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887057387, "dur": 255, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Linq.Parallel.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887057722, "dur": 534, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Reflection.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887058364, "dur": 154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Transactions.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887058571, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.CoreModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887059333, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Duplex.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887054672, "dur": 4850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753301887059523, "dur": 1555, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301887061532, "dur": 58, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.StackTrace.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887062252, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Authentication.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887062639, "dur": 453, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.DependencyInjection.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887063153, "dur": 63, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Win32.Primitives.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887063238, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\netstandard.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887063936, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SubsystemsModule.dll"}}, {"pid": 12345, "tid": 15, "ts": 1753301887061090, "dur": 3540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Cursor.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 15, "ts": 1753301887064631, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 15, "ts": 1753301887064880, "dur": 2469468, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886844518, "dur": 121233, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886965772, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.SpriteShapeModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753301886965761, "dur": 78, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteShapeModule.dll_F7F95DB68C78C481.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753301886965840, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886965898, "dur": 207, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityTestProtocolModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753301886965896, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_2FC980318CB13784.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753301886966133, "dur": 50, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.UIElementsModule.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753301886966133, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.UIElementsModule.dll_D3933C87E9EDE72F.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753301886966185, "dur": 365, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886966760, "dur": 116, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 16, "ts": 1753301886966878, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886967127, "dur": 99, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886967249, "dur": 94, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886967511, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 16, "ts": 1753301886967576, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886967641, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886967787, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886967918, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886967978, "dur": 1962, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886969941, "dur": 1847, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886971789, "dur": 715, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886972504, "dur": 1118, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886973622, "dur": 2068, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886976343, "dur": 706, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Processors\\ShaderGeneratorNames.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753301886975690, "dur": 1481, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886977171, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886978368, "dur": 1034, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\UVMaterialSlot.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753301886979717, "dur": 503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\TangentMaterialSlot.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753301886978245, "dur": 2419, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886981158, "dur": 787, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Framework\\Variables\\Obsolete\\GetGraphVariable.cs"}}, {"pid": 12345, "tid": 16, "ts": 1753301886980664, "dur": 2125, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886982789, "dur": 2030, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886984819, "dur": 1349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886986169, "dur": 1330, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886987500, "dur": 1273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886988773, "dur": 1314, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886990087, "dur": 1055, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886991143, "dur": 307, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886991452, "dur": 454, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886991907, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753301886992137, "dur": 652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Tilemap.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753301886992789, "dur": 1098, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886993945, "dur": 199, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886994187, "dur": 572, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886994759, "dur": 120, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753301886994879, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886994968, "dur": 1465, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753301886996434, "dur": 334, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886996781, "dur": 299, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886997091, "dur": 109, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753301886997251, "dur": 591, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753301886997843, "dur": 124, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886998029, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753301886998180, "dur": 382, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753301886998563, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301886998674, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753301886998748, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753301886999006, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 16, "ts": 1753301886999094, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753301886999602, "dur": 269252, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753301887273606, "dur": 60003, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753301887273393, "dur": 60280, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 16, "ts": 1753301887333674, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301887333804, "dur": 998125, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 16, "ts": 1753301887333802, "dur": 1001025, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753301888336622, "dur": 352, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 16, "ts": 1753301888337059, "dur": 80692, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 16, "ts": 1753301888435075, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1753301888435074, "dur": 97, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 16, "ts": 1753301888435200, "dur": 1099166, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886844542, "dur": 121260, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886965818, "dur": 340, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753301886965811, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainPhysicsModule.dll_D293A48779142A71.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753301886966162, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886966293, "dur": 192, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy@2.5.2\\Lib\\Editor\\PlasticSCM\\Unity.Plastic.Newtonsoft.Json.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753301886966292, "dur": 195, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Plastic.Newtonsoft.Json.dll_7880F7755F74374F.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753301886966487, "dur": 310, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886966805, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886966863, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753301886966926, "dur": 97, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886967046, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886967148, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886967199, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 17, "ts": 1753301886967422, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886967539, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Psdimporter.Editor.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753301886967593, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886967849, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5521740788080646575.rsp"}}, {"pid": 12345, "tid": 17, "ts": 1753301886967938, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886968037, "dur": 1201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886969239, "dur": 1188, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886970427, "dur": 1251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886971678, "dur": 2094, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886973772, "dur": 957, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886975455, "dur": 529, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Flow\\Plugin\\Changelogs\\Changelog_1_2_0.cs"}}, {"pid": 12345, "tid": 17, "ts": 1753301886974730, "dur": 1759, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886976489, "dur": 534, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Drawing\\Views\\GradientEdge.cs"}}, {"pid": 12345, "tid": 17, "ts": 1753301886976489, "dur": 1958, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886979171, "dur": 1187, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\GraphDataReadOnly.cs"}}, {"pid": 12345, "tid": 17, "ts": 1753301886978447, "dur": 1996, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886980444, "dur": 2229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886982674, "dur": 1152, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886983826, "dur": 1589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886985416, "dur": 1052, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886986468, "dur": 1209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886987677, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886988797, "dur": 1438, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886990235, "dur": 870, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886991105, "dur": 354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886991462, "dur": 417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886991880, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753301886992040, "dur": 683, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753301886992723, "dur": 966, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886993741, "dur": 316, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753301886994058, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886994160, "dur": 529, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753301886994689, "dur": 133, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886994859, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 17, "ts": 1753301886995009, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886995073, "dur": 344, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.Common.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 17, "ts": 1753301886995417, "dur": 102, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301886995555, "dur": 20241, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301887015797, "dur": 37185, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301887052989, "dur": 2172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753301887055162, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301887056731, "dur": 66, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netfx\\System.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753301887055248, "dur": 2192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753301887057441, "dur": 4027, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301887062639, "dur": 111, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.ComponentModel.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753301887061473, "dur": 2614, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753301887064088, "dur": 134, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301887064284, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301887064671, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301887064726, "dur": 92, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301887064818, "dur": 208624, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301887273445, "dur": 1058321, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753301887273444, "dur": 1061284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753301888336255, "dur": 217, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 17, "ts": 1753301888337003, "dur": 89151, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 17, "ts": 1753301888442631, "dur": 1090209, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753301888442630, "dur": 1090213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 17, "ts": 1753301889532867, "dur": 1366, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753301886844561, "dur": 121261, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886965839, "dur": 137, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753301886965831, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TextCoreTextEngineModule.dll_BD74DBBEF40E5ADA.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753301886965978, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886966099, "dur": 64, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AIModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753301886966098, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.AIModule.dll_EA305F3817072CC7.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753301886966195, "dur": 317, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreFontEngineModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753301886966195, "dur": 318, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TextCoreFontEngineModule.dll_F72D29FA518CE2F1.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753301886966841, "dur": 130, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886966973, "dur": 125, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886967098, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp2"}}, {"pid": 12345, "tid": 18, "ts": 1753301886967167, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886967332, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886967705, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 18, "ts": 1753301886967887, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886967981, "dur": 1636, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886969617, "dur": 1932, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886971549, "dur": 1835, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886973384, "dur": 1246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886974630, "dur": 1519, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886976177, "dur": 789, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Enumerations\\KeywordDefinition.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753301886976150, "dur": 2173, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886978708, "dur": 686, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\ShaderDropdown.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753301886979394, "dur": 582, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Graphs\\SerializableVirtualTexture.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753301886978324, "dur": 2507, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886980831, "dur": 1373, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886982907, "dur": 563, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Editor\\VisualScripting.Core\\Windows\\Sidebars\\ISidebarPanelContent.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753301886982204, "dur": 1809, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886984014, "dur": 845, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886984859, "dur": 1896, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886986755, "dur": 1061, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886987816, "dur": 723, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886988539, "dur": 1074, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886989614, "dur": 1054, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886990722, "dur": 723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886991446, "dur": 437, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886991884, "dur": 724, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753301886992609, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886993046, "dur": 75, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753301886993306, "dur": 118, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753301886993963, "dur": 183, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\Debugging\\Prefabs\\Scripts\\DebugUIHandlerProgressBar.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753301886994253, "dur": 54, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core@14.0.11\\Runtime\\PostProcessing\\LensFlareCommonSRP.cs"}}, {"pid": 12345, "tid": 18, "ts": 1753301886992802, "dur": 1650, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753301886994453, "dur": 629, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886995090, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886995198, "dur": 137, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 18, "ts": 1753301886995335, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886995404, "dur": 536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 18, "ts": 1753301886995941, "dur": 156, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301886996165, "dur": 56846, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301887053544, "dur": 99, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753301887053012, "dur": 2390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753301887055403, "dur": 114, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301887056481, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.Extensions.Configuration.KeyPerFile.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753301887056694, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Diagnostics.TraceSource.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753301887055522, "dur": 2129, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753301887057652, "dur": 91, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301887058240, "dur": 682, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Net.Http.Rtc.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753301887057751, "dur": 2513, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753301887060264, "dur": 2881, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301887063937, "dur": 55, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.ServiceModel.Http.dll"}}, {"pid": 12345, "tid": 18, "ts": 1753301887063157, "dur": 2061, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Common.Path.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 18, "ts": 1753301887065218, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 18, "ts": 1753301887065372, "dur": 2468986, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886844579, "dur": 121254, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886965845, "dur": 80, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UIElementsModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753301886965839, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UIElementsModule.dll_D441A9CFEC32007A.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753301886965968, "dur": 240, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.VideoModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753301886965967, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.VideoModule.dll_B8A7170076CF7080.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753301886966210, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886966290, "dur": 265, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886966563, "dur": 81, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.burst@1.8.15\\Unity.Burst.CodeGen\\Unity.Burst.Cecil.Rocks.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753301886966562, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_D5253DAEDFB20EED.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753301886966647, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886966890, "dur": 132, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886967030, "dur": 116, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886967267, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886967380, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886967450, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886967563, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886967620, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886967705, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886967933, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886967986, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886969331, "dur": 736, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886970067, "dur": 1292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886971360, "dur": 1324, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886972862, "dur": 899, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.2d.animation@9.1.1\\Editor\\SkinningModule\\Triangulation\\TriangulationUtility.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753301886972685, "dur": 1345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886974767, "dur": 579, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal@14.0.11\\Editor\\2D\\ShapeEditor\\GUIFramework\\ClickAction.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753301886974031, "dur": 2248, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886976444, "dur": 548, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Generation\\Contexts\\TargetPropertyGUIContext.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753301886976280, "dur": 1294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886978424, "dur": 562, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\Scene\\EyeIndexNode.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753301886979005, "dur": 1235, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Nodes\\Input\\PropertyNode.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753301886977574, "dur": 2906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886981859, "dur": 625, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Flow\\Ports\\ControlOutput.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753301886980481, "dur": 2432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886982913, "dur": 1230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886984143, "dur": 1557, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886985701, "dur": 1267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886986968, "dur": 883, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886987851, "dur": 1713, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886989564, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886990112, "dur": 1065, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886991177, "dur": 277, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886991454, "dur": 427, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886991889, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753301886992362, "dur": 62, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.TerrainPhysicsModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753301886992803, "dur": 104, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Reflection.Emit.ILGeneration.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753301886993364, "dur": 92, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Dependencies\\FullSerializer\\Internal\\fsOption.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753301886993751, "dur": 167, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\MonoBehaviour\\UnityOnJointBreak2DMessageListener.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753301886993980, "dur": 52, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Listeners\\UI\\UnityOnDropdownValueChangedMessageListener.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753301886994385, "dur": 71, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting@1.9.4\\Runtime\\VisualScripting.Core\\Reflection\\RenamedFromAttribute.cs"}}, {"pid": 12345, "tid": 19, "ts": 1753301886992134, "dur": 2454, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753301886994588, "dur": 128, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886994756, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753301886994879, "dur": 86, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886994970, "dur": 1305, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753301886996275, "dur": 138, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886996474, "dur": 119, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 19, "ts": 1753301886996643, "dur": 432, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 19, "ts": 1753301886997075, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301886997276, "dur": 57965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301887055265, "dur": 127, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.TextCoreTextEngineModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753301887055410, "dur": 78, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.AndroidJNIModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753301887056483, "dur": 1154, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.HttpsPolicy.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753301887058242, "dur": 674, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CrashReportingModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753301887055242, "dur": 4357, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753301887059599, "dur": 119, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301887059809, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.PerformanceReportingModule.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753301887060242, "dur": 189, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.IO.Pipes.dll"}}, {"pid": 12345, "tid": 19, "ts": 1753301887060711, "dur": 94, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.IO.Compression.ZipFile.dll"}}, {"pid": 12345, "tid": 19, "ts": ****************, "dur": 159, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Razor.dll"}}, {"pid": 12345, "tid": 19, "ts": ****************, "dur": 211, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\Microsoft.AspNetCore.Routing.Abstractions.dll"}}, {"pid": 12345, "tid": 19, "ts": ****************, "dur": 122, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\mscordaccore_amd64_amd64_6.0.1322.58009.dll"}}, {"pid": 12345, "tid": 19, "ts": ****************, "dur": 216, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Runtime.Serialization.Primitives.dll"}}, {"pid": 12345, "tid": 19, "ts": ****************, "dur": 68, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.CoreModule.dll"}}, {"pid": 12345, "tid": 19, "ts": ****************, "dur": 4364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.Animation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 19, "ts": 1753301887064086, "dur": 297, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301887064392, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301887064450, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301887064553, "dur": 126, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301887064731, "dur": 96, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 19, "ts": 1753301887064861, "dur": 2469510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886844217, "dur": 120942, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886965170, "dur": 250, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886965599, "dur": 67, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886965704, "dur": 95, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ScriptUpdater\\ApiUpdater.MovedFromExtractor.exe"}}, {"pid": 12345, "tid": 20, "ts": 1753301886965669, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_F782C228CEDFF0CD.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753301886965856, "dur": 120, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886965997, "dur": 144, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753301886965996, "dur": 147, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_065B772FA1729652.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753301886966144, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886966305, "dur": 137, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886966558, "dur": 217, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886966778, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753301886966845, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886966903, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886967209, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886967300, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886967359, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886967537, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886967703, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 20, "ts": 1753301886967940, "dur": 69, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886968012, "dur": 1669, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886969681, "dur": 1436, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886971117, "dur": 2193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886973311, "dur": 1402, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886974713, "dur": 2353, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886977066, "dur": 1137, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886978742, "dur": 651, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph@14.0.11\\Editor\\Data\\Implementation\\NodeUtils.cs"}}, {"pid": 12345, "tid": 20, "ts": 1753301886978203, "dur": 1897, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886980101, "dur": 1001, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886981102, "dur": 1096, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886982198, "dur": 607, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886982805, "dur": 1466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886984271, "dur": 906, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886985177, "dur": 1590, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886986768, "dur": 1196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886987964, "dur": 1537, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886989501, "dur": 1241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886990742, "dur": 700, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886991442, "dur": 443, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886991887, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753301886992145, "dur": 67, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.ProfilerModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753301886992056, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InternalAPIEngineBridge.001.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753301886992606, "dur": 647, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886993260, "dur": 89, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886993360, "dur": 122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753301886993483, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886993550, "dur": 689, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.2D.IK.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753301886994239, "dur": 176, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886994477, "dur": 574, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886995051, "dur": 127, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886995179, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractorCombine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll.mvfrm"}}, {"pid": 12345, "tid": 20, "ts": 1753301886995263, "dur": 288, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886995553, "dur": 298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.DocCodeSamples.dll (+2 others)"}}, {"pid": 12345, "tid": 20, "ts": 1753301886995852, "dur": 111, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301886995968, "dur": 57008, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301887054654, "dur": 60, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Tools\\ilpp\\Unity.ILPP.Runner\\System.Collections.NonGeneric.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753301887055228, "dur": 51, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEditor.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753301887055475, "dur": 1029, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\Managed\\UnityEngine\\UnityEngine.UnityWebRequestAssetBundleModule.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753301887052984, "dur": 4206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753301887057190, "dur": 2169, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301887059809, "dur": 741, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\NetStandard\\compat\\2.1.0\\shims\\netstandard\\System.Xml.XPath.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753301887060997, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\PackageCache\\com.unity.collections@1.2.4\\Unity.Collections.LowLevel.ILSupport\\Unity.Collections.LowLevel.ILSupport.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753301887063049, "dur": 126, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\Facades\\System.Xml.XDocument.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753301887063184, "dur": 65, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "C:\\Program Files\\Unity\\Hub\\Editor\\2022.3.33f1\\Editor\\Data\\UnityReferenceAssemblies\\unity-4.8-api\\System.ComponentModel.Composition.dll"}}, {"pid": 12345, "tid": 20, "ts": 1753301887059364, "dur": 3949, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.2D.IK.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 20, "ts": 1753301887063314, "dur": 392, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301887063750, "dur": 635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301887064622, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301887064787, "dur": 627, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 20, "ts": 1753301887065414, "dur": 2468964, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1753301889541272, "dur": 3351, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21312, "tid": 9069, "ts": 1753301889557045, "dur": 107632, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21312, "tid": 9069, "ts": 1753301889664795, "dur": 2002, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21312, "tid": 9069, "ts": 1753301889554046, "dur": 113522, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}