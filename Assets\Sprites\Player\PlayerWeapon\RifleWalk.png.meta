fileFormatVersion: 2
guid: 2e7c9253adc352e4f8f17e1f64c0f3b9
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 35
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: RifleWalk_0
      rect:
        serializedVersion: 2
        x: 0
        y: 208
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 97ef8d34090eb27439d25d3ac396ca23
      internalID: -1843532794
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_1
      rect:
        serializedVersion: 2
        x: 26
        y: 208
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b86fc2f76dc67064e82f5d41a6ae3553
      internalID: -1529726235
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_2
      rect:
        serializedVersion: 2
        x: 52
        y: 208
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 7c49fa049bd7cc5498a900c2f5b587ff
      internalID: 1919641928
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_3
      rect:
        serializedVersion: 2
        x: 78
        y: 208
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 658e46f2ee0a64d47802967ef7b30f32
      internalID: 266249972
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_4
      rect:
        serializedVersion: 2
        x: 104
        y: 208
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 691a37c78410df24e9bce169e9978b30
      internalID: 774565598
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_5
      rect:
        serializedVersion: 2
        x: 130
        y: 208
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dc436f0252a218f42b62b6dfc527f86d
      internalID: 1505970250
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_6
      rect:
        serializedVersion: 2
        x: 0
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f04f15f541b3b8245b81b2a2e5dd76f1
      internalID: -83162472
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_7
      rect:
        serializedVersion: 2
        x: 26
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dc9cff7cac147b7448cbc644a3342cd7
      internalID: 1710958685
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_8
      rect:
        serializedVersion: 2
        x: 52
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 1cccc285db1521445bf63d67d4706c38
      internalID: 1724145676
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_9
      rect:
        serializedVersion: 2
        x: 78
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f0e436977432df44995148a5fa7bec8e
      internalID: -645335322
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_10
      rect:
        serializedVersion: 2
        x: 104
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bac188a0069c41c499a811d6dacd1d6f
      internalID: 1960548607
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_11
      rect:
        serializedVersion: 2
        x: 130
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 34d1b99e6bfb35943ac710870873b0dc
      internalID: -896816218
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_12
      rect:
        serializedVersion: 2
        x: 0
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bb6a4c23f94b0df44a71bbb9397b0383
      internalID: 2029202151
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_13
      rect:
        serializedVersion: 2
        x: 26
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: af3c95d45b86db747a12073f16ae87c2
      internalID: 549317421
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_14
      rect:
        serializedVersion: 2
        x: 52
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 59ff8b8a4fac7cc49bf9a00fed31af56
      internalID: -17550234
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_15
      rect:
        serializedVersion: 2
        x: 78
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6259f26a79ab50e4080fc670f6813ff1
      internalID: 513227022
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_16
      rect:
        serializedVersion: 2
        x: 104
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 12f3f0417b14c9943bde5ccaa40b314b
      internalID: 1134524967
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_17
      rect:
        serializedVersion: 2
        x: 130
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a1cb8a3c30ba0d74c8fc211a36b8a503
      internalID: -726211010
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_18
      rect:
        serializedVersion: 2
        x: 0
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: bd4d02a6d01bd20468cfa05caee2f7f9
      internalID: 1749550870
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_19
      rect:
        serializedVersion: 2
        x: 26
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6f0a44553eb31294c961099d89efc321
      internalID: -1399648071
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_20
      rect:
        serializedVersion: 2
        x: 52
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 11eabafe41dee814c91c39fc9a5ea68f
      internalID: 708104380
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_21
      rect:
        serializedVersion: 2
        x: 78
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 06b89359b0e4c9b499bd787f0f5f1812
      internalID: -232872306
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_22
      rect:
        serializedVersion: 2
        x: 104
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6a6ed5d762866fc47b9af92d39c1bf2c
      internalID: 2072898432
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_23
      rect:
        serializedVersion: 2
        x: 130
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0b169f58679723f44a76046650a0bffb
      internalID: 2005906023
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_24
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c29e9d0d84864b24c90fef5bbca6a6c1
      internalID: 1999589587
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_25
      rect:
        serializedVersion: 2
        x: 26
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b412b950ed9f45d4b842fe00c3ee8aef
      internalID: -747618462
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_26
      rect:
        serializedVersion: 2
        x: 52
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 2edc8b8b28d3d754eaa441e24470ba3e
      internalID: 923790586
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_27
      rect:
        serializedVersion: 2
        x: 78
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e41ce5285d3e94444abda137ce6b6e6c
      internalID: 854025707
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_28
      rect:
        serializedVersion: 2
        x: 104
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a3a68388ad9936540b26169f7db98087
      internalID: -489327473
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleWalk_29
      rect:
        serializedVersion: 2
        x: 130
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 17c6f79b13eea3343bd301e332605023
      internalID: 1333860544
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      RifleWalk_0: -1843532794
      RifleWalk_1: -1529726235
      RifleWalk_10: 1960548607
      RifleWalk_11: -896816218
      RifleWalk_12: 2029202151
      RifleWalk_13: 549317421
      RifleWalk_14: -17550234
      RifleWalk_15: 513227022
      RifleWalk_16: 1134524967
      RifleWalk_17: -726211010
      RifleWalk_18: 1749550870
      RifleWalk_19: -1399648071
      RifleWalk_2: 1919641928
      RifleWalk_20: 708104380
      RifleWalk_21: -232872306
      RifleWalk_22: 2072898432
      RifleWalk_23: 2005906023
      RifleWalk_24: 1999589587
      RifleWalk_25: -747618462
      RifleWalk_26: 923790586
      RifleWalk_27: 854025707
      RifleWalk_28: -489327473
      RifleWalk_29: 1333860544
      RifleWalk_3: 266249972
      RifleWalk_4: 774565598
      RifleWalk_5: 1505970250
      RifleWalk_6: -83162472
      RifleWalk_7: 1710958685
      RifleWalk_8: 1724145676
      RifleWalk_9: -645335322
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
