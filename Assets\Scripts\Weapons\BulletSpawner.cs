using UnityEngine;

/// <summary>
/// Handles spawning bullets from weapon fire points using object pooling
/// </summary>
public class BulletSpawner : MonoBehaviour
{
    [<PERSON><PERSON>("Bullet Prefabs")]
    [Tooltip("Bullet prefab used by all weapons")]
    public GameObject bulletPrefab;
    
    [Header("Fire Points")]
    [Tooltip("Default fire point (if weapon-specific not found)")]
    public Transform defaultFirePoint;
    
    [<PERSON>lt<PERSON>("Pistol fire point")]
    public Transform pistolFirePoint;
    
    [Tooltip("Rifle fire point")]
    public Transform rifleFirePoint;
    
    [Tooltip("Shotgun fire point")]
    public Transform shotgunFirePoint;
    
    [Header("Bullet Properties")]
    [Tooltip("Default bullet speed")]
    public float defaultBulletSpeed = 50f;
    
    [Tooltip("Default bullet damage")]
    public float defaultBulletDamage = 25f;
    
    [Tooltip("Default bullet range")]
    public float defaultBulletRange = 100f;
    
    [<PERSON><PERSON>("Shotgun Settings")]
    [Toolt<PERSON>("Number of pellets per shotgun shot")]
    public int shotgunPelletCount = 8;
    
    [Tooltip("Base spread angle for shotgun pellets (degrees)")]
    public float shotgunSpread = 15f;
    
    [Tooltip("Multiplier for weapon's base accuracy when calculating shotgun spread")]
    [Range(0.5f, 3f)]
    public float shotgunAccuracyMultiplier = 1.5f;
    
    [Tooltip("Randomness level for pellet spread (0 = perfect pattern, 1 = very random)")]
    [Range(0f, 1f)]
    public float shotgunRandomnessLevel = 0.8f;
    
    [Tooltip("Distance variation factor for pellets (0 = all same distance, 1 = very varied)")]
    [Range(0f, 1f)]
    public float shotgunDistanceVariation = 0.5f;
    
    [Header("Object Pooling")]
    [Tooltip("Reference to the ObjectPool (auto-finds if not assigned)")]
    public ObjectPool objectPool;
    
    [Tooltip("Tag for the bullet pool (all weapons use the same pool)")]
    public string bulletPoolTag = "Bullet";
    
    private void Awake()
    {
        // Auto-find ObjectPool if not assigned
        if (objectPool == null)
        {
            objectPool = ObjectPool.Instance;
            if (objectPool == null)
            {
                Debug.LogError("BulletSpawner: No ObjectPool found! Make sure ObjectPool is in the scene.");
            }
        }
    }
    
    /// <summary>
    /// Spawn a bullet for the given weapon type
    /// </summary>
    public void SpawnBullet(WeaponType weaponType, Vector3 direction, InventoryItem weapon = null)
    {
        switch (weaponType)
        {
            case WeaponType.Pistol:
                SpawnSingleBullet(pistolFirePoint, direction, weapon);
                break;
                
            case WeaponType.Rifle:
                SpawnSingleBullet(rifleFirePoint, direction, weapon);
                break;
                
            case WeaponType.Shotgun:
                SpawnShotgunPellets(shotgunFirePoint, direction, weapon);
                break;
                
            default:
                SpawnSingleBullet(defaultFirePoint, direction, weapon);
                break;
        }
    }
    
    /// <summary>
    /// Spawn a single bullet using object pooling
    /// </summary>
    private void SpawnSingleBullet(Transform firePoint, Vector3 direction, InventoryItem weapon)
    {
        // Use default fire point if specific one not assigned
        Transform spawnPoint = firePoint != null ? firePoint : defaultFirePoint;
        
        if (bulletPrefab == null)
        {
            Debug.LogError("BulletSpawner: Missing bullet prefab!");
            return;
        }
        
        if (spawnPoint == null)
        {
            Debug.LogError("BulletSpawner: Missing fire point!");
            return;
        }
        
        GameObject bulletObj = null;
        
        // Try to spawn from pool first
        if (objectPool != null && !string.IsNullOrEmpty(bulletPoolTag))
        {
            bulletObj = objectPool.SpawnFromPool(bulletPoolTag, spawnPoint.position, Quaternion.identity);
            
            if (bulletObj == null)
            {
                Debug.LogWarning($"BulletSpawner: Failed to spawn bullet from pool {bulletPoolTag}, falling back to Instantiate");
            }
        }
        else
        {
            if (objectPool == null)
            {
                Debug.LogWarning("BulletSpawner: ObjectPool is null, falling back to Instantiate");
            }
            else if (string.IsNullOrEmpty(bulletPoolTag))
            {
                Debug.LogWarning("BulletSpawner: Bullet pool tag is empty, falling back to Instantiate");
            }
        }
        
        // Fallback to Instantiate if pool failed
        if (bulletObj == null)
        {
            bulletObj = Instantiate(bulletPrefab, spawnPoint.position, Quaternion.identity);
            
            // Add PooledObject component if it doesn't exist
            PooledObject pooledObj = bulletObj.GetComponent<PooledObject>();
            if (pooledObj == null)
            {
                pooledObj = bulletObj.AddComponent<PooledObject>();
            }
            
            // Set the pool tag for future use
            if (!string.IsNullOrEmpty(bulletPoolTag))
            {
                pooledObj.poolTag = bulletPoolTag;
            }
        }
        
        Bullet bullet = bulletObj.GetComponent<Bullet>();
        
        if (bullet != null)
        {
            // Get weapon-specific properties or use defaults
            float speed = GetBulletSpeed(weapon);
            float damage = GetBulletDamage(weapon);
            float range = GetBulletRange(weapon);
            
            // Initialize bullet
            bullet.Initialize(direction, speed, damage, range);
        }
        else
        {
            Debug.LogError("BulletSpawner: Bullet prefab missing Bullet component!");
        }
    }
    
    /// <summary>
    /// Spawn multiple pellets for shotgun using object pooling
    /// </summary>
    private void SpawnShotgunPellets(Transform firePoint, Vector3 baseDirection, InventoryItem weapon)
    {
        // Use default fire point if specific one not assigned
        Transform spawnPoint = firePoint != null ? firePoint : defaultFirePoint;
        
        if (bulletPrefab == null)
        {
            Debug.LogError("BulletSpawner: Missing bullet prefab!");
            return;
        }
        
        if (spawnPoint == null)
        {
            Debug.LogError("BulletSpawner: Missing fire point!");
            return;
        }
        
        if (objectPool == null)
        {
            Debug.LogError("BulletSpawner: ObjectPool is null!");
            return;
        }
        
        // Get weapon properties
        float speed = GetBulletSpeed(weapon);
        float damage = GetBulletDamage(weapon) / shotgunPelletCount; // Divide damage among pellets
        float range = GetBulletRange(weapon);
        
        // Calculate weapon-specific spread (use weapon's base accuracy instead of fixed shotgun spread)
        float weaponSpread = weapon?.itemData?.baseAccuracy ?? shotgunSpread;
        
        // Apply shotgun accuracy multiplier to get the final spread
        float finalSpread = weaponSpread * shotgunAccuracyMultiplier;
        
        // Ensure spread doesn't exceed maximum reasonable value
        finalSpread = Mathf.Clamp(finalSpread, 5f, 45f);
        
        // Spawn multiple pellets with consistent spread pattern
        for (int i = 0; i < shotgunPelletCount; i++)
        {
            // Calculate spread direction using consistent pattern
            Vector3 spreadDirection = CalculateConsistentSpreadDirection(baseDirection, finalSpread, i, shotgunPelletCount);
            
            GameObject pelletObj = null;
            
            // Try to spawn from pool first
            if (objectPool != null && !string.IsNullOrEmpty(bulletPoolTag))
            {
                pelletObj = objectPool.SpawnFromPool(bulletPoolTag, spawnPoint.position, Quaternion.identity);
                
                if (pelletObj == null)
                {
                    Debug.LogWarning($"BulletSpawner: Failed to spawn pellet {i} from pool {bulletPoolTag}, falling back to Instantiate");
                }
            }
            else
            {
                if (objectPool == null)
                {
                    Debug.LogWarning("BulletSpawner: ObjectPool is null, falling back to Instantiate");
                }
                else if (string.IsNullOrEmpty(bulletPoolTag))
                {
                    Debug.LogWarning("BulletSpawner: Bullet pool tag is empty, falling back to Instantiate");
                }
            }
            
            // Fallback to Instantiate if pool failed
            if (pelletObj == null)
            {
                pelletObj = Instantiate(bulletPrefab, spawnPoint.position, Quaternion.identity);
                
                // Add PooledObject component if it doesn't exist
                PooledObject pooledObj = pelletObj.GetComponent<PooledObject>();
                if (pooledObj == null)
                {
                    pooledObj = pelletObj.AddComponent<PooledObject>();
                }
                
                // Set the pool tag for future use
                if (!string.IsNullOrEmpty(bulletPoolTag))
                {
                    pooledObj.poolTag = bulletPoolTag;
                }
            }
            
            if (pelletObj != null)
            {
                Bullet pellet = pelletObj.GetComponent<Bullet>();
                
                if (pellet != null)
                {
                    pellet.Initialize(spreadDirection, speed, damage, range);
                }
            }
            else
            {
                Debug.LogWarning($"BulletSpawner: Failed to spawn pellet {i} (both pool and Instantiate failed)!");
            }
        }
    }
    
    /// <summary>
    /// Calculate consistent spread direction for shotgun pellets using a pattern
    /// </summary>
    private Vector3 CalculateConsistentSpreadDirection(Vector3 baseDirection, float spreadAngle, int pelletIndex, int totalPellets)
    {
        // Create a spread pattern with more randomness for natural feel
        
        // Calculate base angle step for even distribution
        float angleStep = (spreadAngle * 2f) / (totalPellets - 1);
        
        // Calculate the base angle for this specific pellet
        float basePelletAngle = -spreadAngle + (angleStep * pelletIndex);
        
        // Add randomness based on configurable settings
        // Random angle variation within the spread cone
        float randomAngleVariation = Random.Range(-spreadAngle * shotgunRandomnessLevel, spreadAngle * shotgunRandomnessLevel);
        
        // Random distance variation (some pellets closer to center, some further out)
        float minDistance = 1f - shotgunDistanceVariation;
        float maxDistance = 1f + shotgunDistanceVariation;
        float distanceVariation = Random.Range(minDistance, maxDistance);
        
        // Combine base angle with random variation
        float finalAngle = basePelletAngle + randomAngleVariation;
        
        // Apply distance variation to make some pellets travel further than others
        float finalSpreadAngle = finalAngle * distanceVariation;
        
        // Convert angle to radians
        float angleRad = finalSpreadAngle * Mathf.Deg2Rad;
        
        // Create a proper 2D spread that works for all directions
        Vector3 spreadDirection = Calculate2DSpread(baseDirection, angleRad);
        
        return spreadDirection;
    }
    
    /// <summary>
    /// Calculate spread direction using proper 2D mathematics
    /// </summary>
    private Vector3 Calculate2DSpread(Vector3 baseDirection, float angleRad)
    {
        // Normalize the base direction
        Vector3 direction = baseDirection.normalized;
        
        // Calculate the angle of the base direction in the 2D plane
        float baseAngle = Mathf.Atan2(direction.y, direction.x);
        
        // Add the spread angle to the base angle
        float finalAngle = baseAngle + angleRad;
        
        // Convert back to direction vector
        Vector3 spreadDirection = new Vector3(
            Mathf.Cos(finalAngle),
            Mathf.Sin(finalAngle),
            0f
        );
        
        return spreadDirection.normalized;
    }
    
    /// <summary>
    /// Get bullet speed for weapon (or default)
    /// </summary>
    private float GetBulletSpeed(InventoryItem weapon)
    {
        if (weapon?.itemData?.weaponType == WeaponType.Rifle)
            return defaultBulletSpeed * 1.5f; // Rifles shoot faster
        else if (weapon?.itemData?.weaponType == WeaponType.Shotgun)
            return defaultBulletSpeed * 0.8f; // Shotguns shoot slower
        
        return defaultBulletSpeed;
    }
    
    /// <summary>
    /// Get bullet damage for weapon (or default)
    /// </summary>
    private float GetBulletDamage(InventoryItem weapon)
    {
        if (weapon?.itemData?.weaponType == WeaponType.Rifle)
            return defaultBulletDamage * 1.2f; // Rifles do more damage
        else if (weapon?.itemData?.weaponType == WeaponType.Shotgun)
            return defaultBulletDamage * 1.5f; // Shotguns do more total damage
        
        return defaultBulletDamage;
    }
    
    /// <summary>
    /// Get bullet range for weapon (or default)
    /// </summary>
    private float GetBulletRange(InventoryItem weapon)
    {
        if (weapon?.itemData?.weaponType == WeaponType.Rifle)
            return defaultBulletRange * 2f; // Rifles have longer range
        else if (weapon?.itemData?.weaponType == WeaponType.Shotgun)
            return defaultBulletRange * 0.5f; // Shotguns have shorter range
        
        return defaultBulletRange;
    }
    
    /// <summary>
    /// Get fire point for weapon type
    /// </summary>
    public Transform GetFirePoint(WeaponType weaponType)
    {
        switch (weaponType)
        {
            case WeaponType.Pistol:
                return pistolFirePoint != null ? pistolFirePoint : defaultFirePoint;
            case WeaponType.Rifle:
                return rifleFirePoint != null ? rifleFirePoint : defaultFirePoint;
            case WeaponType.Shotgun:
                return shotgunFirePoint != null ? shotgunFirePoint : defaultFirePoint;
            default:
                return defaultFirePoint;
        }
    }
    
    /// <summary>
    /// Get current shotgun spread for the given weapon
    /// </summary>
    public float GetShotgunSpread(InventoryItem weapon)
    {
        if (weapon?.itemData?.weaponType != WeaponType.Shotgun)
            return 0f;
            
        float weaponSpread = weapon.itemData.baseAccuracy;
        float finalSpread = weaponSpread * shotgunAccuracyMultiplier;
        return Mathf.Clamp(finalSpread, 5f, 45f);
    }
    
    /// <summary>
    /// Get shotgun pellet count
    /// </summary>
    public int GetShotgunPelletCount()
    {
        return shotgunPelletCount;
    }
}
