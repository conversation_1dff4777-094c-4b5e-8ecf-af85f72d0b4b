using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Helper script to automatically set up container UI references
/// </summary>
public class ContainerSetupHelper : MonoBehaviour
{
    [Header("Auto-Setup")]
    [Tooltip("Click this button to automatically find and assign UI references")]
    public bool autoSetupReferences = false;
    
    private void Start()
    {
        if (autoSetupReferences)
        {
            SetupReferences();
        }
    }
    
    [ContextMenu("Setup References")]
    public void SetupReferences()
    {
        DualInventoryUI dualUI = GetComponent<DualInventoryUI>();
        if (dualUI == null)
        {
            Debug.LogError("DualInventoryUI component not found!");
            return;
        }
        
        // Find panels
        Transform dualPanel = transform;
        Transform playerPanel = dualPanel.Find("InventoryPanel");
        if (playerPanel == null)
        {
            playerPanel = dualPanel.Find("PlayerInventoryPanel");
        }
        
        Transform containerPanel = dualPanel.Find("ContainerInventoryPanel");
        
        if (playerPanel == null)
        {
            Debug.LogError("Could not find player inventory panel! Make sure it's named 'InventoryPanel' or 'PlayerInventoryPanel'");
            return;
        }
        
        if (containerPanel == null)
        {
            Debug.LogError("Could not find container inventory panel! Make sure it's named 'ContainerInventoryPanel'");
            return;
        }
        
        // Set panel references
        dualUI.dualInventoryPanel = dualPanel.gameObject;
        dualUI.playerInventoryPanel = playerPanel.gameObject;
        dualUI.containerInventoryPanel = containerPanel.gameObject;
        
        // Find UI components
        InventoryUI playerInventoryUI = playerPanel.GetComponent<InventoryUI>();
        ContainerInventoryUI containerInventoryUI = containerPanel.GetComponent<ContainerInventoryUI>();
        
        if (playerInventoryUI == null)
        {
            Debug.LogError("InventoryUI component not found on player panel!");
            return;
        }
        
        if (containerInventoryUI == null)
        {
            Debug.LogWarning("ContainerInventoryUI component not found on container panel. Adding it now...");
            containerInventoryUI = containerPanel.gameObject.AddComponent<ContainerInventoryUI>();
        }
        
        dualUI.playerInventoryUI = playerInventoryUI;
        dualUI.containerInventoryUI = containerInventoryUI;
        
        // Set up container UI references
        SetupContainerInventoryUI(containerInventoryUI, containerPanel, playerInventoryUI);
        
        // Create close button if it doesn't exist
        CreateCloseButtonIfNeeded(dualUI, dualPanel);
        
        // Create container title text if it doesn't exist
        CreateContainerTitleIfNeeded(dualUI, dualPanel);
        
        Debug.Log("Container UI setup completed!");
    }
    
    private void SetupContainerInventoryUI(ContainerInventoryUI containerUI, Transform containerPanel, InventoryUI playerUI)
    {
        // Copy references from player inventory UI
        containerUI.slotPrefab = playerUI.slotPrefab;
        containerUI.itemUIPrefab = playerUI.itemUIPrefab;
        containerUI.slotSize = playerUI.slotSize;
        
        // Find slot and item containers
        Transform slotContainer = containerPanel.Find("ChestSlotContainer");
        if (slotContainer == null)
        {
            slotContainer = containerPanel.Find("SlotContainer");
        }
        
        Transform itemContainer = containerPanel.Find("ChestItemContainer");
        if (itemContainer == null)
        {
            itemContainer = containerPanel.Find("ItemContainer");
        }
        
        if (slotContainer != null)
        {
            containerUI.slotContainer = slotContainer;
        }
        else
        {
            Debug.LogError("Could not find slot container for container inventory!");
        }
        
        if (itemContainer != null)
        {
            containerUI.itemContainer = itemContainer;
        }
        else
        {
            Debug.LogError("Could not find item container for container inventory!");
        }
    }
    
    private void CreateCloseButtonIfNeeded(DualInventoryUI dualUI, Transform dualPanel)
    {
        if (dualUI.closeButton != null) return;
        
        // Create close button
        GameObject closeButtonObj = new GameObject("CloseButton");
        closeButtonObj.transform.SetParent(dualPanel, false);
        
        Button closeButton = closeButtonObj.AddComponent<Button>();
        Image buttonImage = closeButtonObj.AddComponent<Image>();
        buttonImage.color = Color.red;
        
        // Position in top-right corner
        RectTransform rect = closeButtonObj.GetComponent<RectTransform>();
        rect.anchorMin = new Vector2(1, 1);
        rect.anchorMax = new Vector2(1, 1);
        rect.pivot = new Vector2(1, 1);
        rect.anchoredPosition = new Vector2(-10, -10);
        rect.sizeDelta = new Vector2(50, 30);
        
        // Add text
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(closeButtonObj.transform, false);
        Text buttonText = textObj.AddComponent<Text>();
        buttonText.text = "X";
        buttonText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        buttonText.fontSize = 18;
        buttonText.color = Color.white;
        buttonText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform textRect = textObj.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        dualUI.closeButton = closeButton;
    }
    
    private void CreateContainerTitleIfNeeded(DualInventoryUI dualUI, Transform dualPanel)
    {
        if (dualUI.containerTitleText != null) return;
        
        // Create title text
        GameObject titleObj = new GameObject("ContainerTitle");
        titleObj.transform.SetParent(dualPanel, false);
        
        Text titleText = titleObj.AddComponent<Text>();
        titleText.text = "Container";
        titleText.font = Resources.GetBuiltinResource<Font>("Arial.ttf");
        titleText.fontSize = 16;
        titleText.color = Color.white;
        titleText.alignment = TextAnchor.MiddleCenter;
        
        // Position at top center
        RectTransform rect = titleObj.GetComponent<RectTransform>();
        rect.anchorMin = new Vector2(0.5f, 1);
        rect.anchorMax = new Vector2(0.5f, 1);
        rect.pivot = new Vector2(0.5f, 1);
        rect.anchoredPosition = new Vector2(0, -10);
        rect.sizeDelta = new Vector2(200, 30);
        
        dualUI.containerTitleText = titleText;
    }
}
