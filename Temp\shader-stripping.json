{"totalVariantsIn": 3006, "totalVariantsOut": 1745, "shaders": [{"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/Sampling", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "BoxDownsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 5.418200000000001}, {"inputVariants": 1, "outputVariants": 1, "variantName": "BoxDownsample (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0417}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/Stop NaN", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Stop NaN (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0383}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Stop NaN (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0371}]}]}, {"inputVariants": 26, "outputVariants": 26, "name": "Hidden/Universal Render Pipeline/TemporalAA", "pipelines": [{"inputVariants": 26, "outputVariants": 26, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Very Low (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041100000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Very Low (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Low (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0422}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Low (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0438}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Medium (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0439}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Medium (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041600000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality High (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0473}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality High (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0403}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Accumulate - Quality Very High (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0446}, {"inputVariants": 4, "outputVariants": 4, "variantName": "TemporalAA - Accumulate - Quality Very High (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0476}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Copy History (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058100000000000006}, {"inputVariants": 2, "outputVariants": 2, "variantName": "TemporalAA - Copy History (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0439}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Universal Render Pipeline/CameraMotionBlur", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Blur - Low Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0396}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Blur - Low Quality (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0376}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Blur - Medium Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.037700000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Blur - Medium Quality (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Blur - High Quality (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042300000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Camera Motion Blur - High Quality (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034300000000000004}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Universal Render Pipeline/BokehDepthOfField", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.037700000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0334}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0432}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0392}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0364}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0346}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0356}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0323}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0351}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0325}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0318}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field CoC (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0373}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0329}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Prefilter (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.040100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.035300000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Blur (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.036500000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.033}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Post Blur (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.030600000000000002}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0344}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Bokeh Depth Of Field Composite (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0327}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Core/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0451}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0379}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shadow2DShadowSprite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0323}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038700000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/Edge Adaptive Spatial Upsampling", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "EASU (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0359}, {"inputVariants": 1, "outputVariants": 1, "variantName": "EASU (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.04}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Universal/HDRDebugView", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0432}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038700000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0371}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0359}]}]}, {"inputVariants": 14, "outputVariants": 14, "name": "Hidden/Universal Render Pipeline/GaussianDepthOfField", "pipelines": [{"inputVariants": 14, "outputVariants": 14, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.035500000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field CoC (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0488}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0414}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Prefilter (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038900000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0536}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.034800000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field Blur Vertical (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0575}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Gaussian Depth Of Field Blur Vertical (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0648}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.07250000000000001}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Gaussian Depth Of Field Composite (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054900000000000004}]}]}, {"inputVariants": 48, "outputVariants": 24, "name": "Hidden/Universal Render Pipeline/Bloom", "pipelines": [{"inputVariants": 48, "outputVariants": 24, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 8, "outputVariants": 4, "variantName": "Bloom Prefilter (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.22820000000000001}, {"inputVariants": 8, "outputVariants": 4, "variantName": "Bloom Prefilter (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0606}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Bloom Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058100000000000006}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Bloom Blur Horizontal (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0485}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Bloom Blur Vertical (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0543}, {"inputVariants": 4, "outputVariants": 2, "variantName": "Bloom Blur Vertical (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0468}, {"inputVariants": 8, "outputVariants": 4, "variantName": "Bloom Upsample (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0538}, {"inputVariants": 8, "outputVariants": 4, "variantName": "Bloom Upsample (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0611}]}]}, {"inputVariants": 18, "outputVariants": 18, "name": "Hidden/Universal Render Pipeline/SubpixelMorphologicalAntialiasing", "pipelines": [{"inputVariants": 18, "outputVariants": 18, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0506}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.056}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0539}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0499}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0631}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.060500000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/LutBuilderLdr", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "LutBuilderLdr (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0488}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LutBuilderLdr (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.043300000000000005}]}]}, {"inputVariants": 66, "outputVariants": 66, "name": "Hidden/Universal Render Pipeline/LensFlareDataDriven", "pipelines": [{"inputVariants": 66, "outputVariants": 66, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareAdditive (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0488}, {"inputVariants": 12, "outputVariants": 12, "variantName": "LensFlareAdditive (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0731}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareScreen (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.052700000000000004}, {"inputVariants": 12, "outputVariants": 12, "variantName": "LensFlareScreen (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0859}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlarePremultiply (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0471}, {"inputVariants": 12, "outputVariants": 12, "variantName": "LensFlarePremultiply (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.06910000000000001}, {"inputVariants": 4, "outputVariants": 4, "variantName": "LensFlareLerp (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.056100000000000004}, {"inputVariants": 12, "outputVariants": 12, "variantName": "LensFlareLerp (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0618}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0456}, {"inputVariants": 1, "outputVariants": 1, "variantName": "LensFlareOcclusion (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.054400000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Shadow2DUnshadowGeometry", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0458}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0442}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0451}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0397}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Light2D-Shape-Volumetric", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0448}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0451}]}]}, {"inputVariants": 112, "outputVariants": 112, "name": "Hidden/Universal/CoreBlit", "pipelines": [{"inputVariants": 112, "outputVariants": 112, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0472}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0477}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0453}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.040400000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042800000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.04}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0371}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 3 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038200000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 4 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0461}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 4 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0426}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 5 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0375}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 5 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.036500000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 6 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0376}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 6 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.040400000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 7 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0438}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 7 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038700000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 8 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042800000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 8 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0397}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 9 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 9 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.055}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 10 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0422}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 10 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0395}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 11 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.043500000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 11 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0386}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 12 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0409}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 12 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0458}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 13 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041100000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 13 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0364}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 14 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.038}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 14 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033600000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 15 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0444}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 15 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0519}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 16 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041800000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 16 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0465}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 17 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042100000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 17 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0443}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 18 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0494}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 18 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0395}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 19 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0414}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 19 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0422}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 20 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0429}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 20 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0397}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 21 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0439}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 21 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0446}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 22 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.057}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 22 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.046200000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "BilinearDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0436}, {"inputVariants": 4, "outputVariants": 4, "variantName": "BilinearDebugDraw (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0412}, {"inputVariants": 2, "outputVariants": 2, "variantName": "NearestDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.044000000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "NearestDebugDraw (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0475}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Universal Render Pipeline/PaniniProjection", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Panini Projection (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.043300000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Panini Projection (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.044000000000000004}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Custom/SpriteOutline", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0514}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0548}]}]}, {"inputVariants": 4, "outputVariants": 0, "name": "Hidden/Universal/BlitHDROverlay", "pipelines": [{"inputVariants": 4, "outputVariants": 0, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 0, "variantName": "BilinearDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0495}, {"inputVariants": 2, "outputVariants": 0, "variantName": "NearestDebugDraw (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.032}]}]}, {"inputVariants": 130, "outputVariants": 98, "name": "Hidden/Universal Render Pipeline/FinalPost", "pipelines": [{"inputVariants": 130, "outputVariants": 98, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "FinalPost (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.034}, {"inputVariants": 64, "outputVariants": 48, "variantName": "FinalPost (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1429}, {"inputVariants": 1, "outputVariants": 1, "variantName": "FinalPost (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.050100000000000006}, {"inputVariants": 64, "outputVariants": 48, "variantName": "FinalPost (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.1819}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Light2d-Point-Volumetric", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.045000000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.043500000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/ShadowProjected2D", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.045700000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041100000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.039}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0356}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/Debug/DebugReplacement", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Vertex Attributes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.035500000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Vertex Attributes (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0391}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Hidden/Universal/CoreBlitColorAndDepth", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0461}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0458}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041100000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0517}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "Hidden/Light2D-Shape", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0736}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0838}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Shadow2DUnshadowSprite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.059500000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0369}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "Hidden/Light2D-Point", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0674}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0891}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Universal Render Pipeline/FallbackError", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0436}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0367}]}]}, {"inputVariants": 1922, "outputVariants": 721, "name": "Hidden/Universal Render Pipeline/UberPost", "pipelines": [{"inputVariants": 1922, "outputVariants": 721, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 2, "outputVariants": 1, "variantName": "UberPost (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0409}, {"inputVariants": 1920, "outputVariants": 720, "variantName": "UberPost (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 3.1659}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Universal Render Pipeline/Scaling Setup", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "ScalingSetup (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0426}, {"inputVariants": 3, "outputVariants": 3, "variantName": "ScalingSetup (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0459}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Universal Render Pipeline/LutBuilderHdr", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "LutBuilderHdr (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0495}, {"inputVariants": 3, "outputVariants": 3, "variantName": "LutBuilderHdr (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.046400000000000004}]}]}, {"inputVariants": 9, "outputVariants": 9, "name": "TextMeshPro/Mobile/Distance Field (Surface)", "pipelines": [{"inputVariants": 9, "outputVariants": 9, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0459}, {"inputVariants": 2, "outputVariants": 2, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0506}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Caster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0458}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Caster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.044000000000000004}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Mobile/Distance Field Overlay", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.048100000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.048400000000000006}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Mobile/Distance Field - Masking", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.061500000000000006}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0614}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "TextMeshPro/Sprite", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0506}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0512}]}]}, {"inputVariants": 34, "outputVariants": 34, "name": "TextMeshPro/Distance Field (Surface)", "pipelines": [{"inputVariants": 34, "outputVariants": 34, "pipeline": "", "variants": [{"inputVariants": 6, "outputVariants": 6, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.053500000000000006}, {"inputVariants": 4, "outputVariants": 4, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0512}, {"inputVariants": 10, "outputVariants": 10, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06520000000000001}, {"inputVariants": 10, "outputVariants": 10, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0699}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Caster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0524}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Caster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.044000000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Custom/RetroInventoryShader", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0448}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.045700000000000005}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "TextMeshPro/Distance Field", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0716}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0789}]}]}, {"inputVariants": 36, "outputVariants": 36, "name": "Universal Render Pipeline/2D/Sprite-Lit-Default", "pipelines": [{"inputVariants": 36, "outputVariants": 36, "pipeline": "UniversalPipeline", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.076}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Pass 0 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0855}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041100000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0409}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0383}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (ScriptableRenderPipeline) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.035}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "TextMeshPro/Mobile/Distance Field", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 16, "outputVariants": 16, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.06330000000000001}, {"inputVariants": 16, "outputVariants": 16, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.07590000000000001}]}]}, {"inputVariants": 12, "outputVariants": 12, "name": "Legacy Shaders/VertexLit", "pipelines": [{"inputVariants": 12, "outputVariants": 12, "pipeline": "", "variants": [{"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0488}, {"inputVariants": 3, "outputVariants": 3, "variantName": "Pass 0 (Vertex) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.1012}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.051000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (VertexLM) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0414}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0574}, {"inputVariants": 2, "outputVariants": 2, "variantName": "ShadowCaster (ShadowCaster) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.046200000000000005}]}]}, {"inputVariants": 30, "outputVariants": 30, "name": "Legacy Shaders/Diffuse", "pipelines": [{"inputVariants": 30, "outputVariants": 30, "pipeline": "", "variants": [{"inputVariants": 8, "outputVariants": 8, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.058300000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "FORWARD (ForwardBase) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0522}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0543}, {"inputVariants": 5, "outputVariants": 5, "variantName": "FORWARD (ForwardAdd) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.053200000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0538}, {"inputVariants": 4, "outputVariants": 4, "variantName": "DEFERRED (Deferred) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0502}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-StencilWrite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0472}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038400000000000004}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-DepthNormalsTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0356}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0451}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.038400000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0383}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0414}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.046200000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.042100000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.036500000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.049800000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.0403}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0398}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0425}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.039900000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0443}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0366}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.039}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.0413}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.042100000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0361}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.0388}]}]}, {"inputVariants": 32, "outputVariants": 32, "name": "Hidden/Internal-ScreenSpaceShadows", "pipelines": [{"inputVariants": 32, "outputVariants": 32, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0405}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0517}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.05}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.043500000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.0482}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.051800000000000006}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.048}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.0451}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CombineDepthNormals", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0436}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0427}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.048400000000000006}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0487}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0361}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0414}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/ConvertTexture", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0477}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0448}]}]}, {"inputVariants": 54, "outputVariants": 54, "name": "Hidden/Internal-DeferredShading", "pipelines": [{"inputVariants": 54, "outputVariants": 54, "pipeline": "", "variants": [{"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.1023}, {"inputVariants": 26, "outputVariants": 26, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0989}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0446}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0459}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DeferredReflections", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0453}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0475}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0509}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0458}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-MotionVectors", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0543}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (MotionVectors) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0438}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0453}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0417}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0468}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 2 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.039900000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Flare", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0405}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0436}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-Halo", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0438}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.045000000000000005}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyWithDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.044500000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0409}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0507}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0393}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitToDepth_MSAA", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.046200000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.047}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/BlitCopyHDRTonemap", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.037000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0396}]}]}, {"inputVariants": 6, "outputVariants": 6, "name": "Hidden/Internal-DebugPattern", "pipelines": [{"inputVariants": 6, "outputVariants": 6, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0385}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target Color and DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.042}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0328}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only Color (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0344}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0342}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Target only DepthStencil (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0341}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClip", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0378}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0414}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0437}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.038}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureClipText", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0364}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0425}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0458}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.034}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITexture", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0327}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0334}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0376}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.033}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUITextureBlit", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0332}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0442}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.041}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.038900000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRect", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.037000000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041600000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0352}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0323}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-GUIRoundedRectWithColorPerBorder", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0368}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0459}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0402}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.036000000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-UIRDefault", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.031900000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0369}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0391}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0371}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-UIRAtlasBlitCopy", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.042}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0361}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/Internal-UIRDefaultWorld", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0381}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.033600000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0364}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0511}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.041600000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.047400000000000005}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Sprites/Mask", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.049100000000000005}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.050800000000000005}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/Default", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0485}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0509}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "UI/DefaultETC1", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.048100000000000004}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0432}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlur", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0425}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0441}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.038700000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.040100000000000004}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeCopy", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0506}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0368}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0332}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.033}]}]}, {"inputVariants": 4, "outputVariants": 4, "name": "Hidden/CubeBlend", "pipelines": [{"inputVariants": 4, "outputVariants": 4, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.035500000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.037200000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0359}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VR/BlitTexArraySlice", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0325}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0346}]}]}, {"inputVariants": 20, "outputVariants": 20, "name": "Hidden/Internal-ODSWorldTexture", "pipelines": [{"inputVariants": 20, "outputVariants": 20, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0351}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.032100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.0437}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0528}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Vertex)", "stripTimeMs": 0.036000000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 2) (ShaderType: Fragment)", "stripTimeMs": 0.0396}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Vertex)", "stripTimeMs": 0.035300000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 3) (ShaderType: Fragment)", "stripTimeMs": 0.035500000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Vertex)", "stripTimeMs": 0.0366}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 4) (ShaderType: Fragment)", "stripTimeMs": 0.0362}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.046700000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.0392}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Vertex)", "stripTimeMs": 0.0369}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 1 (Normal) (SubShader: 5) (ShaderType: Fragment)", "stripTimeMs": 0.034}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Vertex)", "stripTimeMs": 0.0354}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 6) (ShaderType: Fragment)", "stripTimeMs": 0.033100000000000004}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Vertex)", "stripTimeMs": 0.035}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 7) (ShaderType: Fragment)", "stripTimeMs": 0.0441}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Vertex)", "stripTimeMs": 0.0354}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 8) (ShaderType: Fragment)", "stripTimeMs": 0.034300000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Internal-CubemapToEquirect", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0737}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.037700000000000004}]}]}, {"inputVariants": 5, "outputVariants": 5, "name": "Hidden/VR/BlitFromTex2DToTexArraySlice", "pipelines": [{"inputVariants": 5, "outputVariants": 5, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.04}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.038700000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Vertex)", "stripTimeMs": 0.041600000000000005}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Fragment)", "stripTimeMs": 0.0356}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 1) (ShaderType: Geometry)", "stripTimeMs": 0.038900000000000004}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/VideoComposite", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0332}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Default (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.039}]}]}, {"inputVariants": 30, "outputVariants": 30, "name": "Hidden/VideoDecode", "pipelines": [{"inputVariants": 30, "outputVariants": 30, "pipeline": "", "variants": [{"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0506}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCr_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0441}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0425}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBAFull (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.045700000000000005}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0453}, {"inputVariants": 2, "outputVariants": 2, "variantName": "YCbCrA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.04}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0415}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.041800000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0397}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_RGBASplit_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.037700000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0539}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.040100000000000004}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0381}, {"inputVariants": 2, "outputVariants": 2, "variantName": "Flip_NV12_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0414}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0426}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Flip_P010_To_RGB1 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0396}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/Compositing", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0344}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Mix_RGBA_To_RGBA (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0367}]}]}, {"inputVariants": 2, "outputVariants": 2, "name": "Hidden/TextCore/Distance Field SSD", "pipelines": [{"inputVariants": 2, "outputVariants": 2, "pipeline": "", "variants": [{"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0366}, {"inputVariants": 1, "outputVariants": 1, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0627}]}]}, {"inputVariants": 8, "outputVariants": 8, "name": "Custom/SpriteOutline", "pipelines": [{"inputVariants": 8, "outputVariants": 8, "pipeline": "", "variants": [{"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Vertex)", "stripTimeMs": 0.0531}, {"inputVariants": 4, "outputVariants": 4, "variantName": "Pass 0 (Normal) (SubShader: 0) (ShaderType: Fragment)", "stripTimeMs": 0.0395}]}]}]}