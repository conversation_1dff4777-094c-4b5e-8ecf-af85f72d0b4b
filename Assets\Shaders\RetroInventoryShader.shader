Shader "Custom/RetroInventoryShader"
{
    Properties
    {
        [PerRendererData] _MainTex ("Sprite Texture", 2D) = "white" {}
        _Color ("Tint", Color) = (1,1,1,1)
        _TerminalColor ("Terminal Color", Color) = (0.0, 0.8, 1.0, 1)
        _ScanLineIntensity ("Scan Line Intensity", Range(0, 1)) = 0.15
        _ScanLineCount ("Scan Line Count", Float) = 100
        _ScanLineSpeed ("Scan Line Speed", Range(-10, 10)) = 2
        _GlitchSpeed ("Glitch Speed", Range(0, 50)) = 10
        _GlitchIntensity ("Glitch Intensity", Range(0, 0.1)) = 0.02
        _GlitchInterval ("Glitch Interval", Range(0, 1)) = 0.5
        _NoiseScale ("Noise Scale", Range(0, 100)) = 50
        _NoiseIntensity ("Noise Intensity", Range(0, 1)) = 0.5
        _StaticIntensity ("Static Intensity", Range(0, 1)) = 0.2
        _ColorLevels ("Color Levels", Range(2, 16)) = 4
        _DitherPattern ("Dither Pattern", Range(0, 1)) = 0.5
        _DitherScale ("Dither Scale", Range(1, 4)) = 2
        _BarrelPower ("Barrel Distortion", Range(0, 0.5)) = 0.2
        _SlotBarrelPower ("Slot Barrel Distortion", Range(0, 0.3)) = 0.1
        _JitterIntensity ("Jitter Intensity", Range(0, 0.01)) = 0.003
        _JitterSpeed ("Jitter Speed", Range(0, 10)) = 5.0
        _GridSize ("Grid Size", Vector) = (5, 5, 0, 0)
        _DitherTex ("Dither Pattern Texture", 2D) = "white" {}
        _DitherTexScale ("Dither Texture Scale", Range(0.1, 10)) = 1
        _DitherTexStrength ("Dither Texture Strength", Range(0, 1)) = 0.1
        _VignetteIntensity ("Vignette Intensity", Range(0, 1)) = 0.5
        _VignetteSmoothness ("Vignette Smoothness", Range(0.01, 1)) = 0.5
        _VignetteColor ("Vignette Color", Color) = (0, 0, 0, 1)
        _VignetteDitherScale ("Vignette Dither Scale", Range(0.5, 4)) = 2.0
        
        // UI mask
        _StencilComp ("Stencil Comparison", Float) = 8
        _Stencil ("Stencil ID", Float) = 0
        _StencilOp ("Stencil Operation", Float) = 0
        _StencilWriteMask ("Stencil Write Mask", Float) = 255
        _StencilReadMask ("Stencil Read Mask", Float) = 255
        _ColorMask ("Color Mask", Float) = 15
    }

    SubShader
    {
        Tags
        {
            "Queue"="Transparent"
            "IgnoreProjector"="True"
            "RenderType"="Transparent"
            "PreviewType"="Plane"
            "CanUseSpriteAtlas"="True"
        }

        Stencil
        {
            Ref [_Stencil]
            Comp [_StencilComp]
            Pass [_StencilOp]
            ReadMask [_StencilReadMask]
            WriteMask [_StencilWriteMask]
        }

        Cull Off
        Lighting Off
        ZWrite Off
        ZTest [unity_GUIZTestMode]
        Blend SrcAlpha OneMinusSrcAlpha
        ColorMask [_ColorMask]

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"
            #include "UnityUI.cginc"

            struct appdata_t
            {
                float4 vertex   : POSITION;
                float4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
            };

            struct v2f
            {
                float4 vertex   : SV_POSITION;
                fixed4 color    : COLOR;
                float2 texcoord : TEXCOORD0;
                float4 worldPosition : TEXCOORD1;
            };

            fixed4 _Color;
            fixed4 _TerminalColor;
            float _ScanLineIntensity;
            float _ScanLineCount;
            float _ScanLineSpeed;
            float _GlitchSpeed;
            float _GlitchIntensity;
            float _GlitchInterval;
            float _NoiseScale;
            float _NoiseIntensity;
            float _StaticIntensity;
            float _ColorLevels;
            float _DitherPattern;
            float _DitherScale;
            float _BarrelPower;
            float _SlotBarrelPower;
            float _JitterIntensity;
            float _JitterSpeed;
            float2 _GridSize;

            sampler2D _MainTex;
            float4 _MainTex_ST;
            float4 _ClipRect;

            sampler2D _DitherTex;
            float _DitherTexScale;
            float _DitherTexStrength;
            
            // Vignette parameters
            float _VignetteIntensity;
            float _VignetteSmoothness;
            float4 _VignetteColor;
            float _VignetteDitherScale;

            float random(float2 st)
            {
                return frac(sin(dot(st.xy, float2(12.9898,78.233))) * 43758.5453123);
            }

            float perlinNoise(float2 st)
            {
                float2 i = floor(st);
                float2 f = frac(st);
                
                float a = random(i);
                float b = random(i + float2(1.0, 0.0));
                float c = random(i + float2(0.0, 1.0));
                float d = random(i + float2(1.0, 1.0));

                float2 u = f * f * (3.0 - 2.0 * f);
                return lerp(a, b, u.x) + (c - a)* u.y * (1.0 - u.x) + (d - b) * u.x * u.y;
            }

            float3 generateRGBNoise(float2 uv, float time)
            {
                float3 noiseValue;
                noiseValue.r = perlinNoise(uv * _NoiseScale + time * 5.13);
                noiseValue.g = perlinNoise(uv * _NoiseScale + time * 7.21);
                noiseValue.b = perlinNoise(uv * _NoiseScale + time * 9.37);
                return noiseValue;
            }

            float2 getGlitchOffset(float2 uv, float time, float channelId)
            {
                float timeSlot = floor(time * _GlitchSpeed);
                float shouldGlitch = step(1.0 - _GlitchInterval, random(float2(timeSlot * 0.4, channelId)));
                
                float2 offset = float2(0, 0);
                
                if (shouldGlitch > 0)
                {
                    // Horizontal glitch
                    float randOffset = (random(float2(timeSlot * 0.1 + channelId, uv.y)) - 0.5) * 2.0;
                    offset.x = randOffset * _GlitchIntensity * 5.0;
                    
                    // Vertical glitch
                    float verticalGlitch = step(0.97, random(float2(timeSlot * 0.2, uv.x)));
                    offset.y = verticalGlitch * (random(float2(timeSlot, uv.x)) - 0.5) * 0.1;
                    
                    // Line shifting
                    float lineShift = step(0.5, random(float2(floor(uv.y * 20.0 + timeSlot), channelId)));
                    offset.x += lineShift * (random(float2(timeSlot * 0.7, uv.y)) - 0.5) * 0.1;
                }
                
                return offset;
            }

            float2 barrelDistortion(float2 coord, float power)
            {
                float2 cc = coord - 0.5;
                float dist = dot(cc, cc);
                return coord + cc * (dist * power * 2.0);
            }

            float2 getJitter(float2 uv, float time)
            {
                float2 jitter = float2(
                    random(float2(time * _JitterSpeed, uv.y)) * 2.0 - 1.0,
                    random(float2(time * _JitterSpeed + 999.0, uv.x)) * 2.0 - 1.0
                );
                return jitter * _JitterIntensity;
            }

            float3 getSignalInterference(float2 uv, float time)
            {
                // Optimized by reusing noise values
                float noise1 = perlinNoise(uv * _NoiseScale + time * 5.13);
                float noise2 = perlinNoise(uv * _NoiseScale + time * 7.21);
                float noise3 = perlinNoise(uv * _NoiseScale + time * 9.37);
                
                float3 staticNoise = float3(noise1, noise2, noise3) * _StaticIntensity;
                
                // Optimized color bleeding with shared noise samples
                float2 offset = float2(sin(uv.y * 100.0 + time * 5.0) * 0.01, 0);
                float3 colorBleeding = float3(
                    perlinNoise(uv + offset + time * 0.1),
                    perlinNoise(uv + time * 0.1),
                    perlinNoise(uv - offset + time * 0.1)
                ) * 0.3;
                
                return staticNoise + colorBleeding;
            }
            
            // Optimized dither pattern function
            float getDitherPattern(float2 uv, float scale)
            {
                float2 pixel = uv * _ScreenParams.xy * scale;
                float2 pixelPos = floor(pixel);
                float2 pixelCenter = pixelPos + 0.5;
                
                // 4x4 Bayer matrix for dithering
                const float4x4 bayerMatrix = {
                    0,  8,  2, 10,
                    12, 4, 14,  6,
                    3, 11,  1,  9,
                    15, 7, 13,  5
                };
                
                uint x = (uint)pixelPos.x % 4u;
                uint y = (uint)pixelPos.y % 4u;
                float threshold = bayerMatrix[x][y] / 16.0;
                
                return threshold;
            }
            
            // Vignette with dithering
            float4 applyVignette(float4 color, float2 uv, float time)
            {
                // Calculate distance from center with aspect ratio correction
                float2 center = float2(0.5, 0.5);
                float2 d = (uv - center) * float2(_ScreenParams.x / _ScreenParams.y, 1.0);
                float dist = length(d);
                
                // Smooth vignette falloff
                float vignette = 1.0 - smoothstep(0.7 * _VignetteSmoothness, 1.4 * _VignetteSmoothness, dist);
                vignette = lerp(1.0, vignette, _VignetteIntensity);
                
                // Apply dithering to the vignette edge
                float dither = getDitherPattern(uv * _VignetteDitherScale, 1.0);
                vignette = step(dither, vignette);
                
                // Apply vignette color with dithering
                return lerp(_VignetteColor, color, vignette);
            }

            float3 quantizeColor(float3 color)
            {
                float3 levels = _ColorLevels - 1;
                return floor(color * levels + 0.5) / levels;
            }

            float3 applyDithering(float3 color, float2 screenPos)
            {
                // Reduce resolution by scaling down screen coordinates
                float2 pixelatedPos = floor(screenPos * _ScreenParams.xy / (_DitherScale * 4.0)) * (_DitherScale * 4.0);
                int2 ditherCoord = int2(pixelatedPos);
                
                // Get dither threshold
                float ditherThreshold = getDitherPattern(ditherCoord.x, ditherCoord.y);
                
                // Apply dithering before quantization
                float3 ditheredColor = color + (ditherThreshold - 0.5) * _DitherPattern / _ColorLevels;
                
                // Quantize the dithered color
                return quantizeColor(ditheredColor);
            }

            float3 applyDitherTexture(float3 color, float2 screenPos)
            {
                // Create visible pixels by reducing resolution
                float2 pixelSize = 8.0 * _DitherTexScale; // Increase base pixel size
                float2 pixelatedUV = floor(screenPos * _ScreenParams.xy / pixelSize) * pixelSize / _ScreenParams.xy;
                
                // Sample the dither texture at reduced resolution
                float4 ditherPattern = tex2D(_DitherTex, pixelatedUV * _ScreenParams.xy * _DitherTexScale / 64.0);
                
                // Convert to luminance and apply strength with enhanced contrast
                float ditherLum = dot(ditherPattern.rgb, float3(0.299, 0.587, 0.114));
                ditherLum = (floor(ditherLum * 4.0) / 4.0 - 0.5) * _DitherTexStrength * 1.5;
                
                // Apply the dither pattern
                return color + ditherLum;
            }

            v2f vert(appdata_t v)
            {
                v2f OUT;
                OUT.worldPosition = v.vertex;
                OUT.vertex = UnityObjectToClipPos(OUT.worldPosition);
                OUT.texcoord = TRANSFORM_TEX(v.texcoord, _MainTex);
                OUT.color = v.color * _Color;
                return OUT;
            }

            fixed4 frag(v2f IN) : SV_Target
            {
                float2 uv = IN.texcoord;
                float time = _Time.y;
                
                // Calculate grid position and cell UV
                float2 gridPos = floor(uv * _GridSize);
                float2 cellUV = frac(uv * _GridSize);
                
                // Apply distortions
                float2 distortedCellUV = barrelDistortion(cellUV, _SlotBarrelPower);
                float2 finalUV = (gridPos + distortedCellUV) / _GridSize;
                finalUV = barrelDistortion(finalUV, _BarrelPower);
                
                // Apply jitter and glitch
                float2 jitterOffset = getJitter(finalUV, time);
                finalUV += jitterOffset;
                
                // Sample with RGB split and glitch
                float4 color;
                float2 redOffset = getGlitchOffset(finalUV, time, 0);
                float2 greenOffset = getGlitchOffset(finalUV, time, 1);
                float2 blueOffset = getGlitchOffset(finalUV, time, 2);
                
                color.r = tex2D(_MainTex, finalUV + redOffset).r;
                color.g = tex2D(_MainTex, finalUV + greenOffset).g;
                color.b = tex2D(_MainTex, finalUV + blueOffset).b;
                color.a = tex2D(_MainTex, finalUV).a;
                
                // Add signal interference
                float3 interference = getSignalInterference(finalUV, time);
                color.rgb += interference * _NoiseIntensity;
                
                // Random color tinting
                float3 tintNoise = generateRGBNoise(finalUV + time * 0.1, time) * 0.2;
                color.rgb += tintNoise * color.rgb;
                
                // Apply terminal color
                color.rgb = lerp(color.rgb, _TerminalColor.rgb * color.rgb, 0.5) * IN.color.rgb;
                
                // Apply color levels (posterization)
                color.rgb = floor(color.rgb * _ColorLevels) / _ColorLevels;
                
                // Apply vignette with dithering
                color = applyVignette(color, uv, time);
                
                // Apply scan lines
                float scanlinePos = finalUV.y * _ScanLineCount - time * _ScanLineSpeed;
                float scanline = sin(scanlinePos * 3.14159) * 0.5 + 0.5;
                scanline *= (1.0 + perlinNoise(float2(time * 2.0, finalUV.y)) * 0.2);
                scanline = lerp(1, scanline, _ScanLineIntensity);
                color.rgb *= scanline;
                
                // Apply dithering and color quantization
                color.rgb = applyDithering(color.rgb, IN.vertex.xy);
                
                // Apply the dither texture overlay
                color.rgb = applyDitherTexture(color.rgb, IN.vertex.xy);
                
                // Edge vignette with noise
                float2 vignetteUV = finalUV * 2.0 - 1.0;
                float vignette = 1.0 - dot(vignetteUV, vignetteUV) * 0.3;
                vignette *= (1.0 + perlinNoise(finalUV + time) * 0.1);
                color.rgb *= vignette;
                
                // Maintain proper alpha
                color.a *= IN.color.a;
                
                return color;
            }
            ENDCG
        }
    }
} 