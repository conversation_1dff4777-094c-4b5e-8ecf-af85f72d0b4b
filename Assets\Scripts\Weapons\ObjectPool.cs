using System.Collections.Generic;
using UnityEngine;

/// <summary>
/// Generic object pooling system for efficient object reuse
/// </summary>
public class ObjectPool : MonoBehaviour
{
    [System.Serializable]
    public class Pool
    {
        public string tag;
        public GameObject prefab;
        public int size;
        public bool expandable = true;
    }

    [Header("Pool Configuration")]
    [Tooltip("List of object pools to create")]
    public List<Pool> pools;

    [Header("Debug")]
    [Toolt<PERSON>("Show debug information in console")]
    public bool showDebugInfo = false;

    // Dictionary to store pools
    private Dictionary<string, Queue<GameObject>> poolDictionary;
    private Dictionary<string, GameObject> prefabDictionary;
    private Dictionary<string, Transform> poolParents;

    // Singleton instance
    public static ObjectPool Instance { get; private set; }

    private void Awake()
    {
        // Singleton pattern
        if (Instance == null)
        {
            Instance = this;
            DontDestroyOnLoad(gameObject);
        }
        else
        {
            Destroy(gameObject);
            return;
        }

        InitializePools();
    }

    /// <summary>
    /// Initialize all object pools
    /// </summary>
    private void InitializePools()
    {
        poolDictionary = new Dictionary<string, Queue<GameObject>>();
        prefabDictionary = new Dictionary<string, GameObject>();
        poolParents = new Dictionary<string, Transform>();

        if (pools == null || pools.Count == 0)
        {
            Debug.LogError("ObjectPool: No pools configured! Please add at least one pool in the inspector.");
            return;
        }

        foreach (Pool pool in pools)
        {
            if (string.IsNullOrEmpty(pool.tag))
            {
                Debug.LogError("ObjectPool: Pool has empty tag! Please set a tag for all pools.");
                continue;
            }

            if (pool.prefab == null)
            {
                Debug.LogError($"ObjectPool: Pool '{pool.tag}' has no prefab assigned!");
                continue;
            }

            if (pool.size <= 0)
            {
                Debug.LogError($"ObjectPool: Pool '{pool.tag}' has invalid size ({pool.size})! Size must be greater than 0.");
                continue;
            }

            // Create parent object for this pool
            GameObject poolParent = new GameObject($"Pool_{pool.tag}");
            poolParent.transform.SetParent(transform);
            poolParents[pool.tag] = poolParent.transform;

            // Store prefab reference
            prefabDictionary[pool.tag] = pool.prefab;

            // Create queue for this pool
            Queue<GameObject> objectPool = new Queue<GameObject>();

            // Pre-instantiate objects
            for (int i = 0; i < pool.size; i++)
            {
                GameObject obj = CreateNewObject(pool.tag, poolParent.transform);
                objectPool.Enqueue(obj);
            }

            poolDictionary[pool.tag] = objectPool;

            if (showDebugInfo)
            {
                Debug.Log($"ObjectPool: Created pool '{pool.tag}' with {pool.size} objects");
            }
        }

        // Log summary
        Debug.Log($"ObjectPool: Initialized {poolDictionary.Count} pools with tags: {string.Join(", ", poolDictionary.Keys)}");
    }

    /// <summary>
    /// Create a new object for the pool
    /// </summary>
    private GameObject CreateNewObject(string tag, Transform parent)
    {
        if (string.IsNullOrEmpty(tag))
        {
            Debug.LogError("ObjectPool: Cannot create object with empty tag!");
            return null;
        }

        if (!prefabDictionary.ContainsKey(tag))
        {
            Debug.LogError($"ObjectPool: No prefab found for tag '{tag}'!");
            return null;
        }

        GameObject prefab = prefabDictionary[tag];
        if (prefab == null)
        {
            Debug.LogError($"ObjectPool: Prefab for tag '{tag}' is null!");
            return null;
        }

        GameObject obj = Instantiate(prefab, parent);
        obj.SetActive(false);

        // Add pool reference component
        PooledObject pooledObj = obj.GetComponent<PooledObject>();
        if (pooledObj == null)
        {
            pooledObj = obj.AddComponent<PooledObject>();
        }
        
        // Ensure the pool tag is set correctly
        pooledObj.poolTag = tag;

        if (showDebugInfo)
        {
            Debug.Log($"ObjectPool: Created new object for pool '{tag}' with tag '{pooledObj.poolTag}'");
        }

        return obj;
    }

    /// <summary>
    /// Spawn an object from the pool
    /// </summary>
    public GameObject SpawnFromPool(string tag, Vector3 position, Quaternion rotation)
    {
        if (!poolDictionary.ContainsKey(tag))
        {
            Debug.LogError($"ObjectPool: Pool with tag '{tag}' doesn't exist!");
            return null;
        }

        Queue<GameObject> pool = poolDictionary[tag];
        GameObject objectToSpawn;

        // Check if pool is empty and expandable
        if (pool.Count == 0)
        {
            Pool poolConfig = pools.Find(p => p.tag == tag);
            if (poolConfig != null && poolConfig.expandable)
            {
                // Create new object
                objectToSpawn = CreateNewObject(tag, poolParents[tag]);
                if (showDebugInfo)
                {
                    Debug.Log($"ObjectPool: Expanded pool '{tag}' with new object");
                }
            }
            else
            {
                Debug.LogWarning($"ObjectPool: Pool '{tag}' is empty and not expandable!");
                return null;
            }
        }
        else
        {
            objectToSpawn = pool.Dequeue();
        }

        // Reset and activate object
        objectToSpawn.SetActive(true);
        objectToSpawn.transform.position = position;
        objectToSpawn.transform.rotation = rotation;

        // Reset pooled object state
        PooledObject pooledObj = objectToSpawn.GetComponent<PooledObject>();
        if (pooledObj != null)
        {
            pooledObj.OnSpawnFromPool();
        }

        if (showDebugInfo)
        {
            Debug.Log($"ObjectPool: Spawned object from pool '{tag}' at {position}");
        }

        return objectToSpawn;
    }

    /// <summary>
    /// Return an object to the pool
    /// </summary>
    public void ReturnToPool(GameObject obj)
    {
        PooledObject pooledObj = obj.GetComponent<PooledObject>();
        if (pooledObj == null)
        {
            Debug.LogError($"ObjectPool: Object {obj.name} doesn't have PooledObject component!");
            return;
        }

        string tag = pooledObj.poolTag;
        if (!poolDictionary.ContainsKey(tag))
        {
            Debug.LogError($"ObjectPool: Pool with tag '{tag}' doesn't exist!");
            return;
        }

        // Reset object state
        pooledObj.OnReturnToPool();

        // Deactivate and return to pool
        obj.SetActive(false);
        obj.transform.SetParent(poolParents[tag]);
        poolDictionary[tag].Enqueue(obj);

        if (showDebugInfo)
        {
            Debug.Log($"ObjectPool: Returned object to pool '{tag}'");
        }
    }

    /// <summary>
    /// Get pool statistics
    /// </summary>
    public PoolStats GetPoolStats(string tag)
    {
        if (!poolDictionary.ContainsKey(tag))
        {
            return new PoolStats { tag = tag, totalObjects = 0, activeObjects = 0, inactiveObjects = 0 };
        }

        Queue<GameObject> pool = poolDictionary[tag];
        int totalObjects = pool.Count;
        int inactiveObjects = 0;

        // Count inactive objects in the pool
        foreach (GameObject obj in pool)
        {
            if (!obj.activeInHierarchy)
            {
                inactiveObjects++;
            }
        }

        // Count active objects (total - inactive)
        int activeObjects = totalObjects - inactiveObjects;

        return new PoolStats
        {
            tag = tag,
            totalObjects = totalObjects,
            activeObjects = activeObjects,
            inactiveObjects = inactiveObjects
        };
    }

    /// <summary>
    /// Get statistics for all pools
    /// </summary>
    public Dictionary<string, PoolStats> GetAllPoolStats()
    {
        Dictionary<string, PoolStats> stats = new Dictionary<string, PoolStats>();
        
        foreach (string tag in poolDictionary.Keys)
        {
            stats[tag] = GetPoolStats(tag);
        }

        return stats;
    }

    /// <summary>
    /// Clear all pools (useful for scene transitions)
    /// </summary>
    public void ClearAllPools()
    {
        foreach (var pool in poolDictionary.Values)
        {
            while (pool.Count > 0)
            {
                GameObject obj = pool.Dequeue();
                if (obj != null)
                {
                    Destroy(obj);
                }
            }
        }

        poolDictionary.Clear();
        prefabDictionary.Clear();
        poolParents.Clear();

        if (showDebugInfo)
        {
            Debug.Log("ObjectPool: Cleared all pools");
        }
    }
}

/// <summary>
/// Component that identifies objects as part of a pool
/// </summary>
public class PooledObject : MonoBehaviour
{
    [HideInInspector] public string poolTag;

    /// <summary>
    /// Called when object is spawned from pool
    /// </summary>
    public virtual void OnSpawnFromPool()
    {
        // Override in derived classes for custom spawn behavior
    }

    /// <summary>
    /// Called when object is returned to pool
    /// </summary>
    public virtual void OnReturnToPool()
    {
        // Override in derived classes for custom return behavior
    }

    /// <summary>
    /// Return this object to its pool
    /// </summary>
    public void ReturnToPool()
    {
        if (ObjectPool.Instance != null)
        {
            ObjectPool.Instance.ReturnToPool(gameObject);
        }
    }
}

/// <summary>
/// Statistics for a pool
/// </summary>
[System.Serializable]
public struct PoolStats
{
    public string tag;
    public int totalObjects;
    public int activeObjects;
    public int inactiveObjects;
} 