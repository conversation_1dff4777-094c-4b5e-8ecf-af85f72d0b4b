using UnityEngine;

public class InventoryPanelResizer : MonoBehaviour
{
    [Header("Inventory Manager Reference")]
    public InventoryManager inventoryManager;

    private RectTransform border2Rect;
    private RectTransform borderRect;
    private RectTransform backgroundRect;
    private RectTransform screenShaderRect;

    private int lastWidth = -1;
    private int lastHeight = -1;

    void Awake()
    {
        // Find children by name
        border2Rect = transform.Find("Border2")?.GetComponent<RectTransform>();
        borderRect = transform.Find("Border")?.GetComponent<RectTransform>();
        backgroundRect = transform.Find("Background")?.GetComponent<RectTransform>();
        screenShaderRect = transform.Find("ScreenShader")?.GetComponent<RectTransform>();
    }

    void Start()
    {
        if (inventoryManager == null)
        {
            inventoryManager = FindObjectOfType<InventoryManager>();
        }
        UpdateRects();
    }

    void Update()
    {
        if (inventoryManager == null) return;
        if (inventoryManager.width != lastWidth || inventoryManager.height != lastHeight)
        {
            UpdateRects();
        }
    }

    void UpdateRects()
    {
        if (inventoryManager == null) return;
        int w = inventoryManager.width;
        int h = inventoryManager.height;
        lastWidth = w;
        lastHeight = h;

        // Set InventoryPanel position
        if (w == 5 && h == 5)
        {
            SetPanelPosition(380, -220);
            SetRect(border2Rect, 250, -250, 620, 620);
            SetRect(borderRect, 250, -250, 600, 600);
            SetRect(backgroundRect, 250, -250, 510, 510);
            SetRect(screenShaderRect, 250, -250, 510, 510);
        }
        else if (w == 6 && h == 6)
        {
            SetPanelPosition(340, -150);
            SetRect(border2Rect, 300, -300, 730, 730);
            SetRect(borderRect, 300, -300, 710, 710);
            SetRect(backgroundRect, 300, -300, 610, 610);
            SetRect(screenShaderRect, 300, -300, 610, 610);
        }
        else if (w == 7 && h == 7)
        {
            SetPanelPosition(240, -100);
            SetRect(border2Rect, 350, -350, 850, 850);
            SetRect(borderRect, 350, -350, 830, 830);
            SetRect(backgroundRect, 350, -350, 710, 710);
            SetRect(screenShaderRect, 350, -350, 710, 710);
        }
    }

    void SetRect(RectTransform rect, float posX, float posY, float width, float height)
    {
        if (rect == null) return;
        rect.anchoredPosition = new Vector2(posX, posY);
        rect.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, width);
        rect.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, height);
    }

    void SetPanelPosition(float posX, float posY)
    {
        var panelRect = GetComponent<RectTransform>();
        if (panelRect != null)
        {
            Vector2 newPos = new Vector2(posX, posY);
            panelRect.anchoredPosition = newPos;
            // Also update InventoryUI's initialAnchoredPosition if present
            var inventoryUI = GetComponent<InventoryUI>();
            if (inventoryUI != null)
            {
                var field = typeof(InventoryUI).GetField("initialAnchoredPosition", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                if (field != null)
                {
                    field.SetValue(inventoryUI, newPos);
                }
            }
        }
    }
} 