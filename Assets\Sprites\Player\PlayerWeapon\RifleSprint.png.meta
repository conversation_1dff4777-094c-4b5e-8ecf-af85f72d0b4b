fileFormatVersion: 2
guid: 6c407ee047911b341a83a1a272f60ca6
TextureImporter:
  internalIDToNameTable: []
  externalObjects: {}
  serializedVersion: 13
  mipmaps:
    mipMapMode: 0
    enableMipMap: 0
    sRGBTexture: 1
    linearTexture: 0
    fadeOut: 0
    borderMipMap: 0
    mipMapsPreserveCoverage: 0
    alphaTestReferenceValue: 0.5
    mipMapFadeDistanceStart: 1
    mipMapFadeDistanceEnd: 3
  bumpmap:
    convertToNormalMap: 0
    externalNormalMap: 0
    heightScale: 0.25
    normalMapFilter: 0
    flipGreenChannel: 0
  isReadable: 0
  streamingMipmaps: 0
  streamingMipmapsPriority: 0
  vTOnly: 0
  ignoreMipmapLimit: 0
  grayScaleToAlpha: 0
  generateCubemap: 6
  cubemapConvolution: 0
  seamlessCubemap: 0
  textureFormat: 1
  maxTextureSize: 2048
  textureSettings:
    serializedVersion: 2
    filterMode: 0
    aniso: 1
    mipBias: 0
    wrapU: 1
    wrapV: 1
    wrapW: 1
  nPOTScale: 0
  lightmap: 0
  compressionQuality: 50
  spriteMode: 2
  spriteExtrude: 1
  spriteMeshType: 1
  alignment: 0
  spritePivot: {x: 0.5, y: 0.5}
  spritePixelsToUnits: 30
  spriteBorder: {x: 0, y: 0, z: 0, w: 0}
  spriteGenerateFallbackPhysicsShape: 1
  alphaUsage: 1
  alphaIsTransparency: 1
  spriteTessellationDetail: -1
  textureType: 8
  textureShape: 1
  singleChannelComponent: 0
  flipbookRows: 1
  flipbookColumns: 1
  maxTextureSizeSet: 0
  compressionQualitySet: 0
  textureFormatSet: 0
  ignorePngGamma: 0
  applyGammaDecoding: 0
  swizzle: 50462976
  cookieLightType: 0
  platformSettings:
  - serializedVersion: 3
    buildTarget: DefaultTexturePlatform
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 0
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: Standalone
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  - serializedVersion: 3
    buildTarget: WebGL
    maxTextureSize: 2048
    resizeAlgorithm: 0
    textureFormat: -1
    textureCompression: 1
    compressionQuality: 50
    crunchedCompression: 0
    allowsAlphaSplitting: 0
    overridden: 0
    ignorePlatformSupport: 0
    androidETC2FallbackOverride: 0
    forceMaximumCompressionQuality_BC6H_BC7: 0
  spriteSheet:
    serializedVersion: 2
    sprites:
    - serializedVersion: 2
      name: RifleSprint_0
      rect:
        serializedVersion: 2
        x: 0
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 97ebff79d5b97d749a98d9bd8af3009a
      internalID: -1552321195
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_1
      rect:
        serializedVersion: 2
        x: 26
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 58a79025f9554cf48b1fd03d81a0f45f
      internalID: -1408857090
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_2
      rect:
        serializedVersion: 2
        x: 52
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: f3d744fc2b0c3bc40a434dd91c2576e9
      internalID: 415691272
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_3
      rect:
        serializedVersion: 2
        x: 78
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: b10278336bc80e941bfb4ca0205f543f
      internalID: 255259158
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_4
      rect:
        serializedVersion: 2
        x: 104
        y: 156
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5ea6e481bc12ced4aaa40c27ab4aa279
      internalID: -959826158
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_5
      rect:
        serializedVersion: 2
        x: 0
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: d9bde93c31360f041ada48beacf9e28c
      internalID: -2147019347
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_6
      rect:
        serializedVersion: 2
        x: 26
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 64a342853befd9b4083042d9c7b398b0
      internalID: 981058745
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_7
      rect:
        serializedVersion: 2
        x: 52
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: dd9002cf9af55184587bce28bc69d682
      internalID: 1368335386
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_8
      rect:
        serializedVersion: 2
        x: 78
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 6959f7b2c8f29e247b3929c2078a83ad
      internalID: -185836983
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_9
      rect:
        serializedVersion: 2
        x: 104
        y: 104
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0a683339520b18f4cbef48de5fff5907
      internalID: -1922629844
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_10
      rect:
        serializedVersion: 2
        x: 0
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ee818e1e960bd174a85ce937df2330b2
      internalID: -1437227688
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_11
      rect:
        serializedVersion: 2
        x: 26
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 5b4c4673009b4434189068d97f068f39
      internalID: -1843222469
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_12
      rect:
        serializedVersion: 2
        x: 52
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: e5cb35d4e7d92394ba201573750c66ed
      internalID: 403164032
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_13
      rect:
        serializedVersion: 2
        x: 78
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 0acd93fb4978e3a4a924cb4a3eaca974
      internalID: 672651333
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_14
      rect:
        serializedVersion: 2
        x: 104
        y: 52
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: c546aacace0ff37438046e7765e86251
      internalID: -367748815
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_15
      rect:
        serializedVersion: 2
        x: 0
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 019967f4b1883bf45ae3ebd1b783218a
      internalID: 1440771353
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_16
      rect:
        serializedVersion: 2
        x: 26
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 16da2fbbbd374284f80b5f8eac2506fe
      internalID: -1587604809
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_17
      rect:
        serializedVersion: 2
        x: 52
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: 80126a92884d8bd46a04a50f8c85c81d
      internalID: 699326406
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_18
      rect:
        serializedVersion: 2
        x: 78
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: ed9637089de3cbf47b3f72e08c876b8b
      internalID: -590527204
      vertices: []
      indices: 
      edges: []
      weights: []
    - serializedVersion: 2
      name: RifleSprint_19
      rect:
        serializedVersion: 2
        x: 104
        y: 0
        width: 26
        height: 52
      alignment: 0
      pivot: {x: 0, y: 0}
      border: {x: 0, y: 0, z: 0, w: 0}
      outline: []
      physicsShape: []
      tessellationDetail: 0
      bones: []
      spriteID: a5d37a85ac600744f9a737a2f2e10b07
      internalID: 245112852
      vertices: []
      indices: 
      edges: []
      weights: []
    outline: []
    physicsShape: []
    bones: []
    spriteID: 5e97eb03825dee720800000000000000
    internalID: 0
    vertices: []
    indices: 
    edges: []
    weights: []
    secondaryTextures: []
    nameFileIdTable:
      RifleSprint_0: -1552321195
      RifleSprint_1: -1408857090
      RifleSprint_10: -1437227688
      RifleSprint_11: -1843222469
      RifleSprint_12: 403164032
      RifleSprint_13: 672651333
      RifleSprint_14: -367748815
      RifleSprint_15: 1440771353
      RifleSprint_16: -1587604809
      RifleSprint_17: 699326406
      RifleSprint_18: -590527204
      RifleSprint_19: 245112852
      RifleSprint_2: 415691272
      RifleSprint_3: 255259158
      RifleSprint_4: -959826158
      RifleSprint_5: -2147019347
      RifleSprint_6: 981058745
      RifleSprint_7: 1368335386
      RifleSprint_8: -185836983
      RifleSprint_9: -1922629844
  mipmapLimitGroupName: 
  pSDRemoveMatte: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
