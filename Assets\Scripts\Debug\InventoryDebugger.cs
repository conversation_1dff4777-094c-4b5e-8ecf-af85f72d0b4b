using UnityEngine;

/// <summary>
/// Debug script to help verify inventory setup
/// </summary>
public class InventoryDebugger : MonoBehaviour
{
    [Head<PERSON>("Debug Controls")]
    [Tooltip("Press this key to run debug checks")]
    public KeyCode debugKey = KeyCode.F1;
    
    private void Update()
    {
        if (Input.GetKeyDown(debugKey))
        {
            RunDebugChecks();
        }
    }
    
    [ContextMenu("Run Debug Checks")]
    public void RunDebugChecks()
    {
        Debug.Log("=== INVENTORY DEBUG CHECKS ===");
        
        // Check PlayerController
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController != null)
        {
            Debug.Log($"✓ PlayerController found: {playerController.name}");
            Debug.Log($"  - inventoryUI: {(playerController.inventoryUI != null ? playerController.inventoryUI.name : "NULL")}");
            Debug.Log($"  - dualInventoryUI: {(playerController.dualInventoryUI != null ? playerController.dualInventoryUI.name : "NULL")}");
        }
        else
        {
            Debug.LogError("✗ PlayerController not found!");
        }
        
        // Check DualInventoryUI
        DualInventoryUI dualUI = FindObjectOfType<DualInventoryUI>();
        if (dualUI != null)
        {
            Debug.Log($"✓ DualInventoryUI found: {dualUI.name}");
            Debug.Log($"  - dualInventoryPanel: {(dualUI.dualInventoryPanel != null ? dualUI.dualInventoryPanel.name : "NULL")}");
            Debug.Log($"  - playerInventoryPanel: {(dualUI.playerInventoryPanel != null ? dualUI.playerInventoryPanel.name : "NULL")}");
            Debug.Log($"  - containerInventoryPanel: {(dualUI.containerInventoryPanel != null ? dualUI.containerInventoryPanel.name : "NULL")}");
            Debug.Log($"  - playerInventoryUI: {(dualUI.playerInventoryUI != null ? dualUI.playerInventoryUI.name : "NULL")}");
            Debug.Log($"  - containerInventoryUI: {(dualUI.containerInventoryUI != null ? dualUI.containerInventoryUI.name : "NULL")}");
        }
        else
        {
            Debug.LogError("✗ DualInventoryUI not found!");
        }
        
        // Check InventoryUI
        InventoryUI[] inventoryUIs = FindObjectsOfType<InventoryUI>();
        Debug.Log($"Found {inventoryUIs.Length} InventoryUI components:");
        foreach (var ui in inventoryUIs)
        {
            Debug.Log($"  - {ui.name} (active: {ui.gameObject.activeInHierarchy})");
        }
        
        // Check ContainerInventoryUI
        ContainerInventoryUI[] containerUIs = FindObjectsOfType<ContainerInventoryUI>();
        Debug.Log($"Found {containerUIs.Length} ContainerInventoryUI components:");
        foreach (var ui in containerUIs)
        {
            Debug.Log($"  - {ui.name} (active: {ui.gameObject.activeInHierarchy})");
        }
        
        // Check ContainerInteractable
        ContainerInteractable[] containers = FindObjectsOfType<ContainerInteractable>();
        Debug.Log($"Found {containers.Length} ContainerInteractable components:");
        foreach (var container in containers)
        {
            Debug.Log($"  - {container.name}");
        }
        
        // Check GameObject hierarchy
        Debug.Log("=== GAMEOBJECT HIERARCHY ===");
        GameObject dualPanel = GameObject.Find("DualInventoryPanel");
        if (dualPanel != null)
        {
            Debug.Log($"✓ DualInventoryPanel found: {dualPanel.name}");
            Debug.Log($"  - Active: {dualPanel.activeInHierarchy}");
            Debug.Log($"  - Children: {dualPanel.transform.childCount}");
            for (int i = 0; i < dualPanel.transform.childCount; i++)
            {
                Transform child = dualPanel.transform.GetChild(i);
                Debug.Log($"    - {child.name} (active: {child.gameObject.activeInHierarchy})");
            }
        }
        else
        {
            Debug.LogError("✗ DualInventoryPanel GameObject not found!");
        }
        
        Debug.Log("=== DEBUG CHECKS COMPLETE ===");
    }
    
    [ContextMenu("Test Player Inventory")]
    public void TestPlayerInventory()
    {
        DualInventoryUI dualUI = FindObjectOfType<DualInventoryUI>();
        if (dualUI != null)
        {
            Debug.Log("Testing OpenPlayerInventoryOnly...");
            dualUI.OpenPlayerInventoryOnly();
        }
        else
        {
            Debug.LogError("DualInventoryUI not found for testing!");
        }
    }
    
    [ContextMenu("Close Inventory")]
    public void CloseInventory()
    {
        DualInventoryUI dualUI = FindObjectOfType<DualInventoryUI>();
        if (dualUI != null)
        {
            Debug.Log("Testing CloseDualInventory...");
            dualUI.CloseDualInventory();
        }
        else
        {
            Debug.LogError("DualInventoryUI not found for testing!");
        }
    }
}
