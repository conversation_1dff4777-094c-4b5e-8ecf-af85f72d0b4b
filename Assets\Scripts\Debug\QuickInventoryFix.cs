using UnityEngine;

/// <summary>
/// Quick fix for inventory setup based on debug output
/// </summary>
public class QuickInventoryFix : MonoBehaviour
{
    [ContextMenu("Quick Fix Everything")]
    public void QuickFixEverything()
    {
        Debug.Log("=== QUICK FIX EVERYTHING ===");
        
        // Step 1: Find PlayerController
        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController == null)
        {
            Debug.LogError("❌ PlayerController not found!");
            return;
        }
        Debug.Log($"✅ Found PlayerController: {playerController.name}");
        
        // Step 2: Find InventoryUI (search more thoroughly)
        InventoryUI inventoryUI = FindObjectOfType<InventoryUI>();
        if (inventoryUI == null)
        {
            Debug.LogError("❌ InventoryUI component not found!");

            // Try to find by GameObject name
            GameObject playerInventoryPanel = GameObject.Find("PlayerInventoryPanel");
            if (playerInventoryPanel == null)
            {
                playerInventoryPanel = GameObject.Find("InventoryPanel");
            }

            if (playerInventoryPanel != null)
            {
                Debug.Log($"Found inventory panel: {playerInventoryPanel.name}");
                inventoryUI = playerInventoryPanel.GetComponent<InventoryUI>();
                if (inventoryUI == null)
                {
                    Debug.LogError($"❌ No InventoryUI component on {playerInventoryPanel.name}!");
                    Debug.Log("Adding InventoryUI component...");
                    inventoryUI = playerInventoryPanel.AddComponent<InventoryUI>();
                }
            }
            else
            {
                Debug.LogError("❌ Could not find PlayerInventoryPanel or InventoryPanel!");
                return;
            }
        }
        Debug.Log($"✅ Found InventoryUI: {inventoryUI.name}");
        
        // Step 3: Assign basic inventory reference
        playerController.inventoryUI = inventoryUI.gameObject;
        Debug.Log($"✅ Assigned PlayerController.inventoryUI = {inventoryUI.name}");
        
        // Step 4: Find or create DualInventoryPanel
        GameObject dualPanel = GameObject.Find("DualInventoryPanel");
        if (dualPanel == null)
        {
            Debug.LogError("❌ DualInventoryPanel not found!");
            return;
        }
        Debug.Log($"✅ Found DualInventoryPanel: {dualPanel.name}");
        
        // Step 5: Add DualInventoryUI component if missing
        DualInventoryUI dualUI = dualPanel.GetComponent<DualInventoryUI>();
        if (dualUI == null)
        {
            dualUI = dualPanel.AddComponent<DualInventoryUI>();
            Debug.Log("✅ Added DualInventoryUI component");
        }
        else
        {
            Debug.Log("✅ DualInventoryUI component already exists");
        }
        
        // Step 6: Assign dual inventory reference
        playerController.dualInventoryUI = dualPanel;
        Debug.Log($"✅ Assigned PlayerController.dualInventoryUI = {dualPanel.name}");
        
        // Step 7: Setup DualInventoryUI references
        dualUI.dualInventoryPanel = dualPanel;
        dualUI.playerInventoryPanel = inventoryUI.gameObject;
        dualUI.playerInventoryUI = inventoryUI;
        Debug.Log("✅ Setup DualInventoryUI basic references");
        
        // Step 8: Create container panel if it doesn't exist
        Transform containerPanel = dualPanel.transform.Find("ContainerInventoryPanel");
        if (containerPanel == null)
        {
            GameObject containerPanelObj = new GameObject("ContainerInventoryPanel");
            containerPanelObj.transform.SetParent(dualPanel.transform, false);
            
            // Add RectTransform
            RectTransform containerRect = containerPanelObj.AddComponent<RectTransform>();
            containerRect.anchorMin = new Vector2(0.5f, 0);
            containerRect.anchorMax = new Vector2(1, 1);
            containerRect.offsetMin = new Vector2(5, 10);
            containerRect.offsetMax = new Vector2(-10, -10);
            
            containerPanel = containerPanelObj.transform;
            Debug.Log("✅ Created ContainerInventoryPanel");
        }
        else
        {
            Debug.Log("✅ ContainerInventoryPanel already exists");
        }
        
        dualUI.containerInventoryPanel = containerPanel.gameObject;
        
        // Step 9: Add ContainerInventoryUI if needed
        ContainerInventoryUI containerUI = containerPanel.GetComponent<ContainerInventoryUI>();
        if (containerUI == null)
        {
            containerUI = containerPanel.gameObject.AddComponent<ContainerInventoryUI>();
            
            // Copy settings from player inventory
            containerUI.slotPrefab = inventoryUI.slotPrefab;
            containerUI.itemUIPrefab = inventoryUI.itemUIPrefab;
            containerUI.slotSize = inventoryUI.slotSize;
            
            Debug.Log("✅ Added ContainerInventoryUI");
        }
        else
        {
            Debug.Log("✅ ContainerInventoryUI already exists");
        }
        
        dualUI.containerInventoryUI = containerUI;
        
        // Step 10: Hide dual inventory initially
        dualPanel.SetActive(false);
        
        // Step 11: Make sure regular inventory is hidden
        inventoryUI.gameObject.SetActive(false);
        // Note: isInventoryOpen is now public so this should work
        
        Debug.Log("🎉 QUICK FIX COMPLETE!");
        Debug.Log("✅ All references assigned");
        Debug.Log("✅ DualInventoryUI component added");
        Debug.Log("✅ Container panel created");
        Debug.Log("✅ Inventory hidden initially");
        Debug.Log("📋 Try pressing Tab to open inventory now!");
    }
    
    [ContextMenu("Test Tab Key")]
    public void TestTabKey()
    {
        Debug.Log("=== TESTING TAB KEY ===");

        PlayerController playerController = FindObjectOfType<PlayerController>();
        if (playerController == null)
        {
            Debug.LogError("❌ PlayerController not found!");
            return;
        }

        Debug.Log($"Current inventory state: {(playerController.isInventoryOpen ? "OPEN" : "CLOSED")}");

        if (playerController.inventoryUI != null)
        {
            // Toggle the inventory
            bool newState = !playerController.isInventoryOpen;
            playerController.inventoryUI.SetActive(newState);
            playerController.isInventoryOpen = newState;

            Debug.Log($"✅ Toggled inventory to: {(newState ? "OPEN" : "CLOSED")}");
            Debug.Log($"✅ GameObject active: {playerController.inventoryUI.activeInHierarchy}");
        }
        else
        {
            Debug.LogError("❌ PlayerController.inventoryUI is null!");
        }
    }
    
    [ContextMenu("Debug Scene Hierarchy")]
    public void DebugSceneHierarchy()
    {
        Debug.Log("=== DEBUGGING SCENE HIERARCHY ===");

        // Find all GameObjects with "Inventory" in the name
        GameObject[] allObjects = FindObjectsOfType<GameObject>();
        Debug.Log("🔍 Searching for inventory-related objects...");

        foreach (GameObject obj in allObjects)
        {
            if (obj.name.ToLower().Contains("inventory") || obj.name.ToLower().Contains("dual"))
            {
                Debug.Log($"📦 {obj.name}");
                Debug.Log($"   - Active: {obj.activeInHierarchy}");
                Debug.Log($"   - Parent: {(obj.transform.parent != null ? obj.transform.parent.name : "ROOT")}");

                // Check components
                InventoryUI invUI = obj.GetComponent<InventoryUI>();
                DualInventoryUI dualUI = obj.GetComponent<DualInventoryUI>();
                ContainerInventoryUI containerUI = obj.GetComponent<ContainerInventoryUI>();

                if (invUI != null) Debug.Log($"   - Has InventoryUI ✅");
                if (dualUI != null) Debug.Log($"   - Has DualInventoryUI ✅");
                if (containerUI != null) Debug.Log($"   - Has ContainerInventoryUI ✅");

                Debug.Log(""); // Empty line for readability
            }
        }

        // Check components separately
        Debug.Log("=== COMPONENT SEARCH ===");
        InventoryUI[] inventoryUIs = FindObjectsOfType<InventoryUI>();
        Debug.Log($"📋 Found {inventoryUIs.Length} InventoryUI components:");
        foreach (var ui in inventoryUIs)
        {
            Debug.Log($"   - {ui.name} (active: {ui.gameObject.activeInHierarchy})");
        }

        DualInventoryUI[] dualInventoryUIs = FindObjectsOfType<DualInventoryUI>();
        Debug.Log($"📋 Found {dualInventoryUIs.Length} DualInventoryUI components:");
        foreach (var ui in dualInventoryUIs)
        {
            Debug.Log($"   - {ui.name} (active: {ui.gameObject.activeInHierarchy})");
        }
    }

    [ContextMenu("Verify Setup")]
    public void VerifySetup()
    {
        Debug.Log("=== VERIFYING SETUP ===");

        PlayerController playerController = FindObjectOfType<PlayerController>();
        DualInventoryUI dualUI = FindObjectOfType<DualInventoryUI>();
        InventoryUI inventoryUI = FindObjectOfType<InventoryUI>();

        Debug.Log($"PlayerController: {(playerController != null ? "✅" : "❌")}");
        Debug.Log($"DualInventoryUI: {(dualUI != null ? "✅" : "❌")}");
        Debug.Log($"InventoryUI: {(inventoryUI != null ? "✅" : "❌")}");

        if (playerController != null)
        {
            Debug.Log($"  inventoryUI: {(playerController.inventoryUI != null ? "✅ " + playerController.inventoryUI.name : "❌ NULL")}");
            Debug.Log($"  dualInventoryUI: {(playerController.dualInventoryUI != null ? "✅ " + playerController.dualInventoryUI.name : "❌ NULL")}");
        }

        if (dualUI != null)
        {
            Debug.Log($"  dualInventoryPanel: {(dualUI.dualInventoryPanel != null ? "✅ " + dualUI.dualInventoryPanel.name : "❌ NULL")}");
            Debug.Log($"  playerInventoryPanel: {(dualUI.playerInventoryPanel != null ? "✅ " + dualUI.playerInventoryPanel.name : "❌ NULL")}");
            Debug.Log($"  containerInventoryPanel: {(dualUI.containerInventoryPanel != null ? "✅ " + dualUI.containerInventoryPanel.name : "❌ NULL")}");
        }
    }
}
